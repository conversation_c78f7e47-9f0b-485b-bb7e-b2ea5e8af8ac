/* eslint-disable max-classes-per-file */

import assert from "assert";
import crypto from "crypto";

import {
  addMinutes,
  differenceInCalendarDays,
  isAfter,
  isWithinInterval,
  isSameDay,
  endOfYear,
  differenceInCalendarWeeks,
  differenceInMonths,
  nextMonday,
  startOfMonth,
  addMonths,
  addYears,
  startOfYear,
  startOfDay,
  eachDayOfInterval,
  areIntervalsOverlapping,
  getDay,
  isBefore,
} from "date-fns";
import { round, uniqBy } from "lodash";
import pluralize from "pluralize";

// eslint-disable-next-line import/no-cycle
import {
  ActivityLog,
  BalanceChangedType,
  TimeOffRequestChangedType,
  logBalanceChangeActivity,
  logTimeOffRequestActivity,
} from "./activityLog";
import {
  getAnniversaryDateThisYear,
  getBirthdayDateThisYear,
  getNextAnniversaryDate,
} from "./announcements";
// eslint-disable-next-line import/no-cycle
import { getNotificationPeriod } from "./data-completion";
import { getNode, getParentPosition, getParentDepartment } from "./org-chart";

import {
  TimeOffNotification,
  Schedule,
  Member,
  DeletedTimeOffNotificationsType,
  ReadonlyDeep,
  TimeOffRequest,
  SlackAdapter,
  Workspace,
  TimeOffNotificationType,
  Position,
  Logger,
  AccrualsFrequency,
  YearStart,
  replaceMember,
  Holiday,
  CelebrationEvent,
  TimeOffType,
  CelebrationType,
  TimeOffRequestStatus,
  HolidayType,
  Member as DomainMember,
  TimeOffPolicy,
} from ".";

export const DEFAULT_TIME_OFF_POLICY_REF = "Default Time Off Policy";
export const TYPES_WITH_POLICIES = ["VACATION", "SICK_DAYS"];

export async function deleteTimeOffReminder(
  slackAdapter: SlackAdapter,
  now: Date,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>,
  timeOffReminder: ReadonlyDeep<TimeOffNotification>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const timeOffReminderIndex = timeOffRequest.notifications.findIndex(
    (nt) => nt === timeOffReminder
  );

  assert(timeOffReminderIndex !== -1, "Expected time off reminder to exist");
  assert(
    timeOffReminder.scheduleTime,
    "Expected time off reminder schedule time to exist"
  );

  if (
    !timeOffReminder.deleted &&
    isAfter(timeOffReminder.scheduleTime as Date, now)
  ) {
    await slackAdapter.deleteReminder(
      timeOffReminder.recipientId,
      timeOffReminder.ts
    );

    const newNotifications = [...timeOffRequest.notifications];

    newNotifications[timeOffReminderIndex] = {
      ...timeOffReminder,
      deleted: true,
    };

    return {
      ...timeOffRequest,
      notifications: newNotifications,
    };
  }

  return timeOffRequest;
}

export async function deleteTimeOffRequest(
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  logger: Logger,
  now: Date,
  workspace: ReadonlyDeep<Workspace>,
  initiator: ReadonlyDeep<Member>,
  request: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<Workspace>> {
  assert(
    canDeleteTimeOffRequest(request, initiator, now),
    "Only approver, admin or the requester itself can delete the time off"
  );

  if (request.status === TimeOffRequestStatus.Pending) {
    const messages = request.notifications.filter(
      (nt) => nt.type === TimeOffNotificationType.TimeOffRequest
    );

    for (const message of messages) {
      await slackAdapter
        .updateTimeOffRequest(
          {
            workspace,
          },
          request,
          message.recipientId,
          message.ts,
          initiator.id
        )
        .catch((error) => {
          logger.error(error);
        });
    }
  }

  if (
    request.status === TimeOffRequestStatus.Approved &&
    request.memberId !== initiator.id &&
    !request.approversIds.includes(initiator.id)
  ) {
    await notifyThatApprovedRequestWasDeleted(slackAdapter, request, initiator);
  }

  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found");

  const timeOffReminders = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffReminder
  );

  for (const timeOffReminder of timeOffReminders) {
    request = await deleteTimeOffReminder(
      slackAdapter,
      now,
      request,
      timeOffReminder
    );
  }

  const notifications = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffRequest
  );

  for (const notification of notifications) {
    if (!slackAdapter.isDirectMessageChannel(notification.recipientId)) {
      await slackAdapter
        .archiveChannel(notification.recipientId)
        .catch((error) => {
          logger.error(error);
        });
    }
  }

  if (request.status === "APPROVED") {
    const requester = workspace.members.find((m) => m.id === request.memberId);

    assert(requester, "Expected member to exist");

    workspace = (
      await updateMemberTimeOffBalance(
        activityLog,
        requester,
        request.type,
        {
          increase: calculateTimeOffDuration(
            workspace,
            requester,
            request.startDate as Date,
            request.endDate as Date
          ),
          type: BalanceChangedType.requestDelete,
          initiatorId: initiator.id,
        },
        workspace
      )
    )[1];
  }

  workspace = {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      requests: workspace.timeOffs.requests.filter((r) => r.id !== request.id),
    },
  };

  await logTimeOffRequestActivity(activityLog, workspace, {
    memberId: request.memberId,
    requestId: request.id,
    startDate: request.startDate as Date,
    endDate: request.endDate as Date,
    typeId: request.type,
    updateType: TimeOffRequestChangedType.delete,
    initiatorId: initiator.id,
  });

  return workspace;
}

async function notifyThatApprovedRequestWasDeleted(
  slackAdapter: SlackAdapter,
  request: ReadonlyDeep<TimeOffRequest>,
  initiator: ReadonlyDeep<Member>
): Promise<void> {
  const requesterMessages = request.notifications.filter(
    (n) => n.type === TimeOffNotificationType.TimeOffStatusToRequester
  );

  for (const requesterMessage of requesterMessages) {
    await slackAdapter.notifyThatApprovedTimeOffWasDeleted(
      requesterMessage.recipientId,
      initiator.id,
      DeletedTimeOffNotificationsType.RequesterMessage,
      requesterMessage.ts
    );
  }

  const managerMessages = request.notifications.filter(
    (n) => n.type === TimeOffNotificationType.TimeOffStatusToMemberManager
  );

  for (const managerMessage of managerMessages) {
    await slackAdapter.notifyThatApprovedTimeOffWasDeleted(
      managerMessage.recipientId,
      initiator.id,
      DeletedTimeOffNotificationsType.ApprovedTimeOffManagerMessage,
      managerMessage.ts
    );
  }
}

export class TimeOffStartDateCantBeInThePastError extends Error {}

export class TimeOffEndDateCantBeEarlierThanStartDateError extends Error {}

export class TimeOffDuplicateError extends Error {}

export class TimeOffInvalidApproversError extends Error {}

export class TimeOffMultipleApproversNeedsNewPermissionsError extends Error {}

export interface NewTimeOffInput {
  type: string;
  approversIds: string[];
  startDate: string;
  endDate: string;
  comment?: string;
}

export interface NewTimeOffOptions {
  allowPastTimeOffs?: boolean;
}

export async function newTimeOff(
  activityLog: ActivityLog,
  now: Date,
  workspace: ReadonlyDeep<Workspace>,
  memberId: string,
  input: NewTimeOffInput,
  options?: NewTimeOffOptions
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<TimeOffRequest>]> {
  const member = workspace.members.find((m) => m.id === memberId);

  assert(member, "Member not found");
  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  if (
    input.approversIds.length === 0 ||
    !input.approversIds.every((managerId) =>
      workspace.members.some((m) => m.id === managerId)
    )
  ) {
    throw new TimeOffInvalidApproversError();
  }

  assert(
    input.approversIds.length === 1 ||
      workspace.slackBotToken.scopes.includes("groups:write"),
    new TimeOffMultipleApproversNeedsNewPermissionsError()
  );

  const request: ReadonlyDeep<TimeOffRequest> = {
    id: crypto.randomBytes(10).toString("hex"),
    memberId,
    approversIds: input.approversIds,
    createdAt: now,
    updatedAt: now,
    startDate: new Date(new Date(input.startDate).setHours(0, 0, 0)),
    endDate: new Date(new Date(input.endDate).setHours(23, 59, 59)),
    type: input.type,
    status: TimeOffRequestStatus.Pending,
    notifications: [],
    comment: input.comment,
  };

  const today = new Date(now).setHours(0, 0, 0, 0);
  const selectedStartDate = new Date(input.startDate).setHours(0, 0, 0);
  const selectedEndDate = new Date(input.endDate).setHours(23, 59, 59);

  if (!options?.allowPastTimeOffs && selectedStartDate < today) {
    throw new TimeOffStartDateCantBeInThePastError();
  }

  if (selectedEndDate < selectedStartDate) {
    throw new TimeOffEndDateCantBeEarlierThanStartDateError();
  }

  const hasDuplicate = workspace.timeOffs.requests.some(
    (r) =>
      r.memberId === memberId &&
      r.type === input.type &&
      (r.status === "PENDING" || r.status === "APPROVED") &&
      isSameDay(r.startDate as Date, selectedStartDate) &&
      isSameDay(r.endDate as Date, selectedEndDate)
  );

  if (hasDuplicate) {
    throw new TimeOffDuplicateError();
  }

  workspace = {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      requests: [request, ...workspace.timeOffs.requests],
    },
  };

  await logTimeOffRequestActivity(activityLog, workspace, {
    memberId,
    requestId: request.id,
    startDate: request.startDate as Date,
    endDate: request.endDate as Date,
    typeId: request.type,
    updateType: TimeOffRequestChangedType.create,
    // NOTE: a request can be either created by an admin or by a member
    initiatorId: options?.allowPastTimeOffs
      ? request.approversIds[0]
      : memberId,
  });

  return [workspace, request];
}

interface RequestTimeOffInput {
  type: string;
  approversIds: string[];
  startDate: string;
  endDate: string;
  comment?: string;
}

interface RequestTimeOffOptions {
  onValidationPass?: (request: ReadonlyDeep<TimeOffRequest>) => Promise<void>;
}

export async function requestTimeOff(
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  logger: Logger,
  now: Date,
  workspace: ReadonlyDeep<Workspace>,
  memberId: string,
  input: RequestTimeOffInput,
  options?: RequestTimeOffOptions
): Promise<ReadonlyDeep<[Workspace, TimeOffRequest]>> {
  let request: ReadonlyDeep<TimeOffRequest>;

  [workspace, request] = await newTimeOff(
    activityLog,
    now,
    workspace,
    memberId,
    {
      approversIds: input.approversIds,
      startDate: input.startDate,
      endDate: input.endDate,
      type: input.type,
      comment: input.comment,
    }
  );

  await options?.onValidationPass?.(request);

  if (
    workspace.timeOffs.createDiscussionChannelWhenMultipleApprovers &&
    request.approversIds.length >= 2
  ) {
    request = await discussTimeOffRequest(
      workspace,
      slackAdapter,
      logger,
      request
    ).catch((error) => {
      logger.error(error);

      return request;
    });
  }

  request = await notifyApproversAboutNewTimeOff(
    now,
    workspace,
    slackAdapter,
    request
  );

  workspace = replaceTimeOffRequest(workspace, request);

  return [workspace, request];
}

export interface UpdateTimeOffInput {
  type: string;
  startDate: string;
  endDate: string;
}

export async function editTimeOffRequest(
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  editor: ReadonlyDeep<Member>,
  request: ReadonlyDeep<TimeOffRequest>,
  input: UpdateTimeOffInput
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<TimeOffRequest>]> {
  assert(
    canEditTimeOffRequest(request, editor),
    "Only admins can edit time offs from the web"
  );

  const prevRequest = request;
  let requester = workspace.members.find((m) => m.id === request.memberId);

  assert(requester, "Member not found");

  request = {
    ...request,
    updatedAt: new Date(),
    startDate: new Date(new Date(input.startDate).setHours(0, 0, 0)),
    endDate: new Date(new Date(input.endDate).setHours(23, 59, 59)),
    type: input.type,
  };

  if (request.status === "APPROVED") {
    await postTimeOffRequestChangeInChannel(
      slackAdapter,
      workspace,
      editor,
      prevRequest,
      request
    );

    if (request.memberId !== editor.id) {
      request = await notifyRequesterAboutTimeOffChange(
        slackAdapter,
        workspace,
        editor,
        prevRequest,
        request
      );
    }

    if (prevRequest.type !== request.type) {
      /**
       * NOTE: if we change a request's type we want to revert
       * the previous type balance and update the new one
       */
      workspace = (
        await updateMemberTimeOffBalance(
          activityLog,
          requester,
          prevRequest.type,
          {
            increase: calculateTimeOffDuration(
              workspace,
              requester,
              prevRequest.startDate as Date,
              prevRequest.endDate as Date
            ),
            type: BalanceChangedType.requestUpdate,
            initiatorId: editor.id,
          },
          workspace
        )
      )[1];

      /**
       * NOTE: edge case when an admin changes their own request
       */
      requester = workspace.members.find((m) => m.id === request.memberId);
      assert(requester, "Member not found");

      workspace = (
        await updateMemberTimeOffBalance(
          activityLog,
          requester,
          request.type,
          {
            increase: -calculateTimeOffDuration(
              workspace,
              requester,
              request.startDate as Date,
              request.endDate as Date
            ),
            type: BalanceChangedType.requestUpdate,
            initiatorId: editor.id,
          },
          workspace
        )
      )[1];
    } else {
      const balanceDelta =
        calculateTimeOffDuration(
          workspace,
          requester,
          prevRequest.startDate as Date,
          prevRequest.endDate as Date
        ) -
        calculateTimeOffDuration(
          workspace,
          requester,
          request.startDate as Date,
          request.endDate as Date
        );

      if (balanceDelta) {
        workspace = (
          await updateMemberTimeOffBalance(
            activityLog,
            requester,
            request.type,
            {
              increase: balanceDelta,
              type: BalanceChangedType.requestUpdate,
              initiatorId: editor.id,
            },
            workspace
          )
        )[1];
      }
    }
  }

  workspace = replaceTimeOffRequest(workspace, request);

  await logTimeOffRequestActivity(activityLog, workspace, {
    memberId: request.memberId,
    requestId: request.id,
    startDate: request.startDate as Date,
    endDate: request.endDate as Date,
    typeId: request.type,
    updateType: TimeOffRequestChangedType.update,
    initiatorId: editor.id,
  });

  return [workspace, request];
}

export async function notifyApproversAboutNewTimeOff(
  now: Date,
  workspace: ReadonlyDeep<Workspace>,
  slackAdapter: SlackAdapter,
  request: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found");
  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  const channels = request.approversIds;

  for (const channel of channels) {
    const response = await slackAdapter.sendTimeOffRequest(request, channel, {
      workspace,
    });

    request = {
      ...request,
      notifications: [
        ...request.notifications,
        {
          ts: response.ts,
          recipientId: response.channel,
          type: TimeOffNotificationType.TimeOffRequest,
        },
      ],
    };

    const diffInDays = differenceInCalendarDays(request.startDate as Date, now);

    if (diffInDays >= 2) {
      const scheduleTime = addMinutes(
        request.createdAt as Date,
        getNotificationPeriod(Schedule.DAY) / 1000 / 60
      );

      const reminderResponse =
        await slackAdapter.sendReminderAboutTimeOffRequest(
          channel,
          scheduleTime,
          response.ts
        );

      request = {
        ...request,
        notifications: [
          ...request.notifications,
          {
            ts: reminderResponse.ts,
            recipientId: reminderResponse.channel,
            type: TimeOffNotificationType.TimeOffReminder,
            scheduleTime,
          },
        ],
      };
    }
  }

  return request;
}

export async function discussTimeOffRequest(
  workspace: ReadonlyDeep<Workspace>,
  slackAdapter: SlackAdapter,
  logger: Logger,
  request: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const member = workspace.members.find((m) => m.id === request.memberId);

  assert(member, "Member not found");
  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  assert(
    workspace.slackBotToken.scopes.includes("groups:write"),
    new TimeOffMultipleApproversNeedsNewPermissionsError()
  );

  if (
    request.notifications.some(
      (x) =>
        x.type === TimeOffNotificationType.TimeOffRequest &&
        !slackAdapter.isDirectMessageChannel(x.recipientId)
    )
  ) {
    return request;
  }

  const channel = await createTimeOffRequestDiscussionChannel(
    slackAdapter,
    workspace,
    member,
    request
  );

  const response = await slackAdapter.sendTimeOffRequest(request, channel, {
    workspace,
  });

  request = {
    ...request,
    notifications: [
      ...request.notifications,
      {
        ts: response.ts,
        recipientId: response.channel,
        type: TimeOffNotificationType.TimeOffRequest,
      },
    ],
  };

  const diffInDays = differenceInCalendarDays(
    request.startDate as Date,
    new Date()
  );

  if (diffInDays >= 2) {
    const scheduleTime = addMinutes(
      request.createdAt as Date,
      getNotificationPeriod(Schedule.DAY) / 1000 / 60
    );

    const reminderResponse = await slackAdapter.sendReminderAboutTimeOffRequest(
      channel,
      scheduleTime
    );

    request = {
      ...request,
      notifications: [
        ...request.notifications,
        {
          ts: reminderResponse.ts,
          recipientId: reminderResponse.channel,
          type: TimeOffNotificationType.TimeOffReminder,
          scheduleTime,
        },
      ],
    };
  }

  const managerNotifications = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffRequest
  );

  for (const managerNotification of managerNotifications) {
    await slackAdapter
      .updateTimeOffRequest(
        {
          workspace,
        },
        request,
        managerNotification.recipientId,
        managerNotification.ts
      )
      // eslint-disable-next-line @typescript-eslint/no-loop-func
      .catch((error) => {
        logger.error(error);
      });
  }

  return request;
}

export async function rejectTimeOffRequest(
  workspace: ReadonlyDeep<Workspace>,
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  logger: Logger,
  now: Date,
  request: ReadonlyDeep<TimeOffRequest>,
  rejectReason: string | null,
  approver: ReadonlyDeep<Member>
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<TimeOffRequest>]> {
  assert(
    request.status === "PENDING",
    "Only pending time off requests can be rejected"
  );

  const status = TimeOffRequestStatus.Rejected;

  assert(
    request.approversIds.includes(approver.id) || approver.isAdmin,
    "Only an approver or an admin can reject time off"
  );

  request = {
    ...request,
    handledBy: approver.id,
    status,
    rejectReason: rejectReason ?? undefined,
    updatedAt: now,
  };

  const reminders = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffReminder
  );

  for (const reminder of reminders) {
    request = await deleteTimeOffReminder(
      slackAdapter,
      now,
      request,
      reminder
      // eslint-disable-next-line @typescript-eslint/no-loop-func
    ).catch((error) => {
      logger.error(error);

      return request;
    });
  }

  const managerNotifications = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffRequest
  );

  for (const managerNotification of managerNotifications) {
    await slackAdapter
      .updateTimeOffRequest(
        {
          workspace,
        },
        request,
        managerNotification.recipientId,
        managerNotification.ts
      )
      .catch((error) => {
        logger.error(error);
      });

    if (!slackAdapter.isDirectMessageChannel(managerNotification.recipientId)) {
      await slackAdapter
        .archiveChannel(managerNotification.recipientId)
        .catch((error) => {
          logger.error(error);
        });
    }
  }

  const wasCreatedByRequester = managerNotifications.length > 0;

  if (wasCreatedByRequester) {
    request = await notifyRequesterAboutTimeOffStatusChange(
      slackAdapter,
      workspace,
      request
    );
  }

  request = await notifyOrgChartManagerAboutTimeOff(
    slackAdapter,
    workspace,
    request
  );

  workspace = replaceTimeOffRequest(workspace, request);

  await logTimeOffRequestActivity(activityLog, workspace, {
    memberId: request.memberId,
    requestId: request.id,
    startDate: request.startDate as Date,
    endDate: request.endDate as Date,
    typeId: request.type,
    updateType: TimeOffRequestChangedType.reject,
    initiatorId: approver.id,
  });

  return [workspace, request];
}

export async function approveTimeOffRequest(
  workspace: ReadonlyDeep<Workspace>,
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  logger: Logger,
  now: Date,
  request: ReadonlyDeep<TimeOffRequest>,
  approver: ReadonlyDeep<Member>
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<TimeOffRequest>]> {
  assert(
    request.status === TimeOffRequestStatus.Pending,
    "Only pending time off requests can be approved"
  );

  const status = TimeOffRequestStatus.Approved;

  assert(
    request.approversIds.includes(approver.id) || approver.isAdmin,
    "Only an approver or an admin can approve time off"
  );

  request = {
    ...request,
    handledBy: approver.id,
    status,
    updatedAt: now,
  };

  await postTimeOffRequestInChannel(slackAdapter, workspace, request);

  const reminders = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffReminder
  );

  for (const reminder of reminders) {
    request = await deleteTimeOffReminder(
      slackAdapter,
      now,
      request,
      reminder
      // eslint-disable-next-line @typescript-eslint/no-loop-func
    ).catch((error) => {
      logger.error(error);

      return request;
    });
  }

  const managerNotifications = request.notifications.filter(
    (nt) => nt.type === TimeOffNotificationType.TimeOffRequest
  );

  for (const managerNotification of managerNotifications) {
    await slackAdapter
      .updateTimeOffRequest(
        {
          workspace,
        },
        request,
        managerNotification.recipientId,
        managerNotification.ts
      )
      .catch((error) => {
        logger.error(error);
      });

    if (!slackAdapter.isDirectMessageChannel(managerNotification.recipientId)) {
      await slackAdapter
        .archiveChannel(managerNotification.recipientId)
        .catch((error) => {
          logger.error(error);
        });
    }
  }

  const wasCreatedByRequester = managerNotifications.length > 0;

  if (wasCreatedByRequester) {
    request = await notifyRequesterAboutTimeOffStatusChange(
      slackAdapter,
      workspace,
      request
    );
  }

  request = await notifyOrgChartManagerAboutTimeOff(
    slackAdapter,
    workspace,
    request
  );

  const requester = workspace.members.find((m) => m.id === request.memberId);

  assert(requester, "Member not found");

  const balanceDelta = calculateTimeOffDuration(
    workspace,
    requester,
    request.startDate as Date,
    request.endDate as Date
  );

  if (balanceDelta) {
    workspace = (
      await updateMemberTimeOffBalance(
        activityLog,
        requester,
        request.type,
        {
          increase: -balanceDelta,
          type: BalanceChangedType.requestUpdate,
          initiatorId: approver.id,
        },
        workspace
      )
    )[1];
  }

  workspace = replaceTimeOffRequest(workspace, request);

  await logTimeOffRequestActivity(activityLog, workspace, {
    memberId: request.memberId,
    requestId: request.id,
    startDate: request.startDate as Date,
    endDate: request.endDate as Date,
    typeId: request.type,
    updateType: TimeOffRequestChangedType.approve,
    initiatorId: approver.id,
  });

  return [workspace, request];
}

async function notifyOrgChartManagerAboutTimeOff(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const member = workspace.members.find(
    (m) => m.id === timeOffRequest.memberId
  );

  const position = getNode(
    workspace.orgTree.rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member?.id
  );

  const chief = position ? getParentPosition(workspace, position.id) : null;

  if (
    chief?.memberId &&
    !timeOffRequest.approversIds.includes(chief.memberId)
  ) {
    const response = await slackAdapter.notifyMemberMangerAboutTimeOffStatus(
      workspace.id,
      chief.memberId,
      timeOffRequest,
      {
        workspace,
      }
    );

    return {
      ...timeOffRequest,
      updatedAt: new Date(),
      notifications: [
        ...timeOffRequest.notifications,
        {
          type: TimeOffNotificationType.TimeOffStatusToMemberManager,
          ts: response.ts,
          recipientId: response.channel,
        },
      ],
    };
  }

  return timeOffRequest;
}

async function notifyRequesterAboutTimeOffStatusChange(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const response = await slackAdapter.notifyRequesterAboutTimeOff(
    timeOffRequest,
    {
      workspace,
    }
  );

  return {
    ...timeOffRequest,
    updatedAt: new Date(),
    notifications: [
      ...timeOffRequest.notifications,
      {
        type: TimeOffNotificationType.TimeOffStatusToRequester,
        ts: response.ts,
        recipientId: timeOffRequest.memberId,
      },
    ],
  };
}

async function notifyRequesterAboutTimeOffChange(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  editor: ReadonlyDeep<Member>,
  prevTimeOffRequest: ReadonlyDeep<TimeOffRequest>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): Promise<ReadonlyDeep<TimeOffRequest>> {
  const response = await slackAdapter.notifyRequesterAboutTimeOffChange(
    prevTimeOffRequest,
    timeOffRequest,
    editor,
    {
      workspace,
    }
  );

  return {
    ...timeOffRequest,
    updatedAt: new Date(),
    notifications: [
      ...timeOffRequest.notifications,
      {
        type: TimeOffNotificationType.TimeOffChangeToRequester,
        ts: response.ts,
        recipientId: timeOffRequest.memberId,
      },
    ],
  };
}

async function postTimeOffRequestInChannel(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): Promise<void> {
  if (workspace.timeOffs.channelId && workspace.timeOffs.isEnable) {
    await slackAdapter.postTimeOffInChannel(
      timeOffRequest,
      workspace.timeOffs.channelId,
      {
        workspace,
      }
    );
  }
}

async function postTimeOffRequestChangeInChannel(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  editor: ReadonlyDeep<Member>,
  prevTimeOffRequest: ReadonlyDeep<TimeOffRequest>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): Promise<void> {
  if (workspace.timeOffs.channelId && workspace.timeOffs.isEnable) {
    await slackAdapter.postTimeOffChangeInChannel(
      prevTimeOffRequest,
      timeOffRequest,
      editor,
      workspace.timeOffs.channelId,
      {
        workspace,
      }
    );
  }
}

function getHolidaysInRange(
  holidays: readonly ReadonlyDeep<Holiday>[],
  startDate: Date,
  endDate: Date
): Date[] {
  return uniqBy(
    holidays.flatMap((holiday) =>
      eachDayOfInterval({
        start: holiday.startDate as Date,
        end: holiday.endDate as Date,
      }).filter((day) =>
        isWithinInterval(day, { start: startDate, end: endDate })
      )
    ),
    (date) => date.toDateString()
  );
}

function calculateWorkingDays(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): readonly number[] {
  const policy =
    workspace.timeOffs.policies.find(
      (p) => p.id === member.timeOffTypePolicyId
    ) ?? workspace.timeOffs.policies.find((p) => p.isDefault);

  return policy ? policy.workDays : [1, 2, 3, 4, 5];
}

export function calculateTimeOffDuration(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  timeOffStartDate: Date,
  timeOffEndDate: Date,
  // Note: observableIterval is used to filter out days that are not within the observable interval
  observableIterval?: Interval
): number {
  assert(
    isSameDay(timeOffEndDate, timeOffStartDate) ||
      timeOffEndDate > timeOffStartDate,
    "End date should be greater than start date"
  );

  const officialHolidays = getMemberOfficialHolidays(member, workspace);
  const daysToExclude: Date[] = getHolidaysInRange(
    officialHolidays,
    timeOffStartDate,
    timeOffEndDate
  );
  const workingDays = calculateWorkingDays(workspace, member);
  const policy =
    workspace.timeOffs.policies.find(
      (p) => p.id === member.timeOffTypePolicyId
    ) ?? workspace.timeOffs.policies.find((p) => p.isDefault);

  const includedWeekendDays = policy?.includedWeekendDays ?? [];

  return eachDayOfInterval({
    start: timeOffStartDate,
    end: timeOffEndDate,
  }).filter((day, i, days) => {
    if (
      observableIterval &&
      !(
        isWithinInterval(day, observableIterval) ||
        isSameDay(day, observableIterval.start) ||
        isSameDay(day, observableIterval.end)
      )
    ) {
      return false;
    }

    const isExcluded = daysToExclude.some((d) => isSameDay(d, day));
    // getDay returns Sunday as 0, so we convert it to 7
    const dayOfWeek = getDay(day) || 7;
    /**
     * NOTE: we include weekend days into calculation
     * only if there is a working day after them
     */
    const hasWorkingDayAfter = days
      .slice(i + 1)
      .some((d) => workingDays.includes(getDay(d) || 7));

    return (
      !isExcluded &&
      (workingDays.includes(dayOfWeek) ||
        (includedWeekendDays.includes(dayOfWeek) && hasWorkingDayAfter))
    );
  }).length;
}

export function getHolidayDurationInDays(
  holiday: ReadonlyDeep<Holiday>
): number {
  assert(
    isSameDay(holiday.endDate as Date, holiday.startDate as Date) ||
      holiday.endDate > holiday.startDate,
    "End date should be greater than start date"
  );
  const duration =
    differenceInCalendarDays(
      holiday.endDate as Date,
      holiday.startDate as Date
    ) + 1;

  return duration;
}

export function canEditTimeOffRequest(
  request: ReadonlyDeep<TimeOffRequest>,
  member: ReadonlyDeep<Member>
): boolean {
  return member.isAdmin;
}

export function canDeleteTimeOffRequest(
  request: ReadonlyDeep<TimeOffRequest>,
  member: ReadonlyDeep<Member>,
  now: Date = new Date()
): boolean {
  if (
    request.status === TimeOffRequestStatus.Approved ||
    request.status === TimeOffRequestStatus.Rejected
  ) {
    return (
      (member.id === request.memberId && now < request.startDate) ||
      request.approversIds.includes(member.id) ||
      member.isAdmin
    );
  }

  return member.id === request.memberId || member.isAdmin;
}

export function updateApproversOnMemberChannelJoin(
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  channelId: string
): ReadonlyDeep<Workspace> {
  const requests = [...workspace.timeOffs.requests];
  const index = requests.findIndex((r) =>
    r.notifications.some(
      (notification) => notification.recipientId === channelId
    )
  );

  if (index === -1) {
    return workspace;
  }

  let request = requests[index];

  if (request.memberId === member.id) {
    return workspace;
  }

  logger.info("Time Off - Adding an approver after channel join");

  request = {
    ...request,
    approversIds: [...new Set([...request.approversIds, member.id])],
  };

  requests[index] = request;

  workspace = {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      requests,
    },
  };

  return workspace;
}

export function updateApproversOnMemberChannelLeave(
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  channelId: string
): ReadonlyDeep<Workspace> {
  const requests = [...workspace.timeOffs.requests];
  const index = requests.findIndex((r) =>
    r.notifications.some(
      (notification) => notification.recipientId === channelId
    )
  );

  if (index === -1) {
    return workspace;
  }

  let request = requests[index];

  if (request.memberId === member.id) {
    return workspace;
  }

  logger.info("Time Off - Removing an approver after channel leave");

  request = {
    ...request,
    approversIds: request.approversIds.filter((x) => x !== member.id),
  };

  requests[index] = request;

  workspace = {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      requests,
    },
  };

  return workspace;
}

export function replaceTimeOffRequest(
  workspace: ReadonlyDeep<Workspace>,
  timeOffRequest: ReadonlyDeep<TimeOffRequest>
): ReadonlyDeep<Workspace> {
  const index = workspace.timeOffs.requests.findIndex(
    (r) => r.id === timeOffRequest.id
  );

  if (index === -1) {
    return workspace;
  }

  const requests = [...workspace.timeOffs.requests];

  requests[index] = timeOffRequest;

  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      requests,
    },
  };
}

export class ChannelNameTakenError extends Error {}

export class ChannelCreateForbiddenError extends Error {}

const SLACK_MAX_CHANNEL_LENGTH = 80;

async function createTimeOffRequestDiscussionChannel(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  request: ReadonlyDeep<TimeOffRequest>
): Promise<string> {
  function formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}${month}${day}`;
  }

  const startDate = formatDate(request.startDate as Date);
  const endDate = formatDate(request.endDate as Date);
  const dateRange =
    startDate === endDate ? startDate : `${startDate}-${endDate}`;
  const nonce =
    String.fromCharCode(
      "a".charCodeAt(0) +
        Math.floor(Math.random() * ("z".charCodeAt(0) - "a".charCodeAt(0)))
    ) +
    String.fromCharCode(
      "a".charCodeAt(0) +
        Math.floor(Math.random() * ("z".charCodeAt(0) - "a".charCodeAt(0)))
    );
  const type = slugify(
    workspace.timeOffs.types.find((t) => t.id === request.type)?.label ?? ""
  );

  assert(type, "Expected time-off type to exist");

  const memberName = slugify(
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
    member.displayName || member.realName || member.name || "xxx"
  ).slice(
    0,
    SLACK_MAX_CHANNEL_LENGTH - `${type}--${dateRange}`.length - nonce.length
  );
  const channelName = `${type}-${memberName}-${dateRange}`;
  const participants =
    request.approversIds.length === 1
      ? [request.approversIds[0], request.memberId]
      : [...request.approversIds];

  const channelResponse = await slackAdapter
    .createPrivateChannel(channelName, participants)
    .catch((error) => {
      if (error instanceof ChannelNameTakenError) {
        return slackAdapter.createPrivateChannel(
          channelName + nonce,
          participants
        );
      }

      throw error;
    });

  return channelResponse.channel;
}

function slugify(str: string): string {
  return String(str)
    .normalize("NFKD") // split accented characters into their base characters and diacritical marks
    .replace(/[\u0300-\u036f]/g, "") // remove all the accents, which happen to be all in the \u03xx UNICODE block.
    .trim() // trim leading or trailing whitespace
    .toLowerCase() // convert to lowercase
    .replace(/[^a-z0-9 -]/g, "") // remove non-alphanumeric characters
    .replace(/\s+/g, "-") // replace spaces with hyphens
    .replace(/-+/g, "-"); // remove consecutive hyphens
}

export async function updateMemberTimeOffBalance(
  activityLog: ActivityLog,
  member: ReadonlyDeep<Member>,
  timeOffTypeId: string,
  update:
    | {
        increase: number;
        type: BalanceChangedType;
        initiatorId?: string;
      }
    | {
        absolute: number;
        type: BalanceChangedType;
        initiatorId?: string;
      },
  workspace: ReadonlyDeep<Workspace>
): Promise<[ReadonlyDeep<Member>, ReadonlyDeep<Workspace>]> {
  const policy =
    workspace.timeOffs.policies.find(
      (p) => p.id === member.timeOffTypePolicyId
    ) ?? workspace.timeOffs.policies.find((p) => p.isDefault);

  // NOTE: change balance only if we have policies and the request was approved
  if (!policy) {
    return [member, workspace];
  }

  const typePolicy = policy.typePolicies.find(
    (tp) => tp.typeId === timeOffTypeId
  );

  if (!typePolicy) {
    return [member, workspace];
  }

  // NOTE: do nothing if we reached the maxCapacity during increasing
  if (
    "increase" in update &&
    update.increase > 0 &&
    typePolicy.maxCapacity &&
    member.timeOffs[timeOffTypeId]?.balance === typePolicy.maxCapacity
  ) {
    return [member, workspace];
  }

  const prevBalance = member.timeOffs[timeOffTypeId]?.balance ?? 0;
  let balance = prevBalance;

  if ("absolute" in update) {
    if (typePolicy.maxCapacity) {
      balance = Math.min(update.absolute, typePolicy.maxCapacity);
    } else {
      balance = update.absolute;
    }
  } else if (update.increase) {
    if (typePolicy.maxCapacity) {
      balance = Math.min(
        round(balance + update.increase, 2),
        typePolicy.maxCapacity
      );
    } else {
      balance = round(balance + update.increase, 2);
    }
  }

  const updatedMember: ReadonlyDeep<Member> = {
    ...member,
    timeOffs: {
      ...member.timeOffs,
      [timeOffTypeId]: {
        nextResetAt: null,
        ...member.timeOffs[timeOffTypeId],
        balance,
      },
    },
  };

  const updatedWorkspace: ReadonlyDeep<Workspace> = replaceMember(
    workspace,
    updatedMember
  );

  await logBalanceChangeActivity(activityLog, updatedWorkspace, {
    memberId: member.id,
    initiatorId: update.initiatorId,
    typeId: timeOffTypeId,
    prevBalance,
    absolute: "absolute" in update ? update.absolute : undefined,
    increase: "increase" in update ? update.increase : undefined,
    updateType: update.type,
  });

  return [updatedMember, updatedWorkspace];
}

/**
 * NOTE: this function should be executed each time members joinedAt or
 * policy changes
 */
export function updateMemberNextTimeOffResetDates(
  policy: ReadonlyDeep<TimeOffPolicy>,
  members: ReadonlyDeep<Member>[],
  workspace: ReadonlyDeep<Workspace>,
  now: Date
): ReadonlyDeep<Workspace> {
  let updatedWorkspace = workspace;

  for (let member of members) {
    for (const typePolicy of policy.typePolicies) {
      let nextResetAt = member.timeOffs[typePolicy.typeId]?.nextResetAt ?? null;

      if (typePolicy.yearStart === YearStart.StartDate) {
        const nextAnniversary = getNextAnniversaryDate(member, now);

        if (nextAnniversary) {
          nextResetAt = nextAnniversary;
        }
      } else {
        nextResetAt = getNextReset(now);
      }

      member = {
        ...member,
        timeOffs: {
          ...member.timeOffs,
          [typePolicy.typeId]: {
            balance: 0,
            ...member.timeOffs[typePolicy.typeId],
            nextResetAt,
          },
        },
      };
    }

    updatedWorkspace = replaceMember(updatedWorkspace, member);
  }

  return updatedWorkspace;
}

export function clearMemberNextTimeOffResetDates(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Workspace> {
  return replaceMember(workspace, {
    ...member,
    timeOffs: Object.fromEntries(
      Object.entries(member.timeOffs).map(([typeId, t]) => [
        typeId,
        { balance: 0, ...t, nextResetAt: null },
      ])
    ),
  });
}

export function balanceByEOY(
  accrualsQuota: number,
  accuralsFrequency: AccrualsFrequency,
  getTime: () => Date
): number {
  const today = getTime();
  const lastDay = endOfYear(today);

  if (accuralsFrequency === AccrualsFrequency.Week) {
    return (
      accrualsQuota *
      differenceInCalendarWeeks(lastDay, today, { weekStartsOn: 1 })
    );
  }

  return accrualsQuota * differenceInMonths(lastDay, today);
}

export function balanceForYear(
  accrualsQuota: number,
  accuralsFrequency: AccrualsFrequency
): number {
  const weeksPerYear = 52;
  const monthsPerYear = 12;

  if (accuralsFrequency === AccrualsFrequency.Week) {
    return accrualsQuota * weeksPerYear;
  }

  return Math.floor(accrualsQuota * monthsPerYear);
}

export function getNextAccruals(
  accuralsFrequency: AccrualsFrequency,
  from: Date
): Date {
  const normalizedFrom = startOfDay(from);

  if (accuralsFrequency === AccrualsFrequency.Week) {
    return nextMonday(normalizedFrom);
  }

  return addMonths(startOfMonth(normalizedFrom), 1);
}

export function getNextReset(from: Date): Date {
  const normalizedFrom = startOfDay(from);

  return addYears(startOfYear(normalizedFrom), 1);
}

export function floatDaysToDaysAndHours(days: number): [number, number] {
  const daysInt = Math.floor(days);
  // NOTE: rounde hours to 2 decimal places
  const hours = round((days - daysInt) * 8, 2);

  return [daysInt, hours];
}

export function daysAndHoursToFloatDays(days: number, hours: number): number {
  const daysFloat = Math.floor(days) + Math.min(hours, 8) / 8;

  return daysFloat;
}

export function getDaysAndHoursInfo(days: number): string {
  if (days === 0) {
    return "0 days";
  }

  const [daysInt, hours] = floatDaysToDaysAndHours(days);

  const dates = [];

  if (daysInt) {
    dates.push(`${daysInt} ${pluralize("day", daysInt)}`);
  }

  if (hours) {
    dates.push(`${hours} ${pluralize("hour", hours)}`);
  }

  return dates.join(" ");
}

export function getMemberOfficialHolidays(
  member: ReadonlyDeep<Member>,
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Holiday[]> {
  if (member.country) {
    return workspace.holidays.filter(
      (h) => h.country === member.country && h.isOfficial
    );
  }

  return [];
}

export const matchesFilter = (
  value: string,
  filters: readonly string[]
): boolean => {
  return filters.length === 0 || filters.includes(value);
};

function getMembers(
  workspace: ReadonlyDeep<Workspace>,
  filters: ReadonlyDeep<{
    departments: string[];
    teams: string[];
    countries: string[];
  }>
): ReadonlyDeep<Member>[] {
  const { members } = workspace;
  const { departments, teams, countries } = filters;

  return members.filter((member) => {
    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (n): n is Position => n.type === "position" && n.memberId === member.id
    );
    const department = memberPosition
      ? getParentDepartment(workspace, memberPosition.id)
      : null;

    if (
      departments.length &&
      (!department || !matchesFilter(department.id, departments))
    ) {
      return false;
    }

    const memberTeams = memberPosition
      ? workspace.teams.filter((team) =>
          memberPosition.teamIds.includes(team.id)
        )
      : [];

    if (
      teams.length &&
      !memberTeams.some((team) => matchesFilter(team.id, teams))
    ) {
      return false;
    }

    if (
      countries.length &&
      (!member.country || !matchesFilter(member.country, countries))
    ) {
      return false;
    }

    return true;
  });
}

export function getTimeOffs(
  workspace: ReadonlyDeep<Workspace>,
  filters: ReadonlyDeep<{
    status: TimeOffRequestStatus;
    managers: string[];
    types: string[];
    departments: string[];
    teams: string[];
    countries: string[];
  }>,
  range: Interval
): ReadonlyDeep<TimeOffRequest>[] {
  const { types, status } = filters;

  const memberIds = getMembers(workspace, filters).map((m) => m.id);

  const typeById = workspace.timeOffs.types.reduce<Record<string, TimeOffType>>(
    (acc, x) => {
      if (matchesFilter(x.id, types)) {
        acc[x.id] = x;
      }

      return acc;
    },
    {}
  );

  return workspace.timeOffs.requests.filter(
    (timeOff) =>
      (isWithinInterval(new Date(timeOff.startDate as Date), range) ||
        isWithinInterval(new Date(timeOff.endDate as Date), range) ||
        isWithinInterval(range.start, {
          start: timeOff.startDate as Date,
          end: timeOff.endDate as Date,
        })) &&
      memberIds.includes(timeOff.memberId) &&
      timeOff.status === status &&
      timeOff.type in typeById
  );
}

export function getCelebrations(
  workspace: ReadonlyDeep<Workspace>,
  filters: ReadonlyDeep<{
    managers: string[];
    types: string[];
    departments: string[];
    teams: string[];
    countries: string[];
  }>,
  range: {
    start: Date;
    end: Date;
  }
): ReadonlyDeep<CelebrationEvent>[] {
  const celebrations: CelebrationEvent[] = [];
  const { types } = filters;

  getMembers(workspace, filters).forEach((member) => {
    const startYear = range.start.getFullYear();
    const endYear = range.end.getFullYear();
    const yearsToCheck = [];

    // Add all years between start and end year inclusive
    for (let year = startYear; year <= endYear; year += 1) {
      yearsToCheck.push(year);
    }

    // Check celebrations for each relevant year
    yearsToCheck.forEach((year) => {
      const birthdayDate =
        matchesFilter("BIRTHDAY", types) &&
        getBirthdayDateThisYear(member, year);

      if (birthdayDate) {
        celebrations.push({
          memberId: member.id,
          startDate: new Date(new Date(birthdayDate).setHours(0, 0, 0)),
          endDate: new Date(new Date(birthdayDate).setHours(23, 59, 59)),
          type: CelebrationType.Birthday,
        });
      }

      const anniversaryDate =
        matchesFilter("ANNIVERSARY", types) &&
        getAnniversaryDateThisYear(member, year);

      if (anniversaryDate) {
        celebrations.push({
          memberId: member.id,
          startDate: new Date(new Date(anniversaryDate).setHours(0, 0, 0)),
          endDate: new Date(new Date(anniversaryDate).setHours(23, 59, 59)),
          type: CelebrationType.Anniversary,
        });
      }
    });
  });

  return celebrations;
}

export function getHolidays(
  workspace: ReadonlyDeep<Workspace>,
  filters: ReadonlyDeep<{
    types: HolidayType[];
    countries: string[];
  }>,
  range: {
    start: Date;
    end: Date;
  }
): ReadonlyDeep<Holiday>[] {
  const { countries, types } = filters;

  return workspace.holidays
    .filter(
      (holiday) =>
        matchesFilter(`${holiday.country}`, countries) &&
        (isWithinInterval(holiday.startDate as Date, range) ||
          isWithinInterval(holiday.endDate as Date, range) ||
          isWithinInterval(range.start, {
            start: holiday.startDate as Date,
            end: holiday.endDate as Date,
          }))
    )
    .filter((holiday) => {
      if (types.length === 0 || types.length === 2) {
        return true;
      }

      return types.includes(HolidayType.Official)
        ? holiday.isOfficial
        : !holiday.isOfficial;
    });
}

export function getOverlappingMemberTimeOffRequests(
  workspace: ReadonlyDeep<Workspace>,
  range: {
    start: Date;
    end: Date;
  }
): ReadonlyDeep<TimeOffRequest>[] {
  return workspace.timeOffs.requests.filter(
    (r) =>
      r.status === TimeOffRequestStatus.Approved &&
      areIntervalsOverlapping(
        {
          start: r.startDate.getTime(),
          end: r.endDate.getTime(),
        },
        range
      )
  );
}

export function setUpTimeOffPolicyOnMembersJoin(
  workspace: ReadonlyDeep<Workspace>,
  newMembers: ReadonlyDeep<Member>[],
  now: Date
): ReadonlyDeep<Workspace> {
  const defaultPolicy = workspace.timeOffs.policies.find((p) => p.isDefault);

  if (defaultPolicy) {
    workspace = updateMemberNextTimeOffResetDates(
      defaultPolicy,
      newMembers,
      workspace,
      now
    );
  }

  return workspace;
}
interface WorkingDaysResult {
  workedDays: number;
  totalWorkingDays: number;
}

export function calculateWorkingDaysStats(
  workspace: ReadonlyDeep<Workspace>,
  domainMember: ReadonlyDeep<DomainMember>,
  range: { start: Date; end: Date },
  current: Date = new Date()
): WorkingDaysResult {
  const currentDate = startOfDay(current);
  const endDate = range.end;
  const workingDaysResult = { workedDays: 0, totalWorkingDays: 0 };
  let joinedAtDate = domainMember.joinedAt as Date | undefined;
  let startDate = range.start;
  let isNoWorkedDays = false;

  if (joinedAtDate) {
    joinedAtDate = startOfDay(joinedAtDate);

    if (joinedAtDate >= startDate && joinedAtDate <= endDate) {
      startDate = joinedAtDate;
    }

    if (joinedAtDate > endDate || currentDate < startDate) {
      isNoWorkedDays = true;
    }
  }

  const events = workspace.timeOffs.requests;

  const approvedTimeOffDays = events
    .filter(
      (event) =>
        event.status === TimeOffRequestStatus.Approved &&
        event.memberId === domainMember.id &&
        isWithinInterval(event.startDate as Date, {
          start: startDate,
          end: endDate,
        })
    )
    .flatMap((event) =>
      eachDayOfInterval({
        start: new Date(event.startDate as Date),
        end: new Date(event.endDate as Date),
      })
    );

  const officialHolidays = getMemberOfficialHolidays(domainMember, workspace);
  const holyDaysToExclude: Date[] = getHolidaysInRange(
    officialHolidays,
    startDate,
    endDate
  );

  const workingDays = calculateWorkingDays(workspace, domainMember);

  return eachDayOfInterval({
    start: startDate,
    end: endDate,
  }).reduce((acc, day) => {
    const isHolidayExcluded = holyDaysToExclude.some((holiday) =>
      isSameDay(holiday, day)
    );
    const isTimeOffExcluded = approvedTimeOffDays.some((timeOff) =>
      isSameDay(timeOff, day)
    );
    const dayOfWeek = getDay(day) || 7;

    if (!isHolidayExcluded && workingDays.includes(dayOfWeek)) {
      acc.totalWorkingDays += 1;

      if (
        (!isTimeOffExcluded && !isNoWorkedDays && isBefore(day, currentDate)) ||
        isSameDay(day, currentDate)
      ) {
        acc.workedDays += 1;
      }
    }

    return acc;
  }, workingDaysResult);
}
