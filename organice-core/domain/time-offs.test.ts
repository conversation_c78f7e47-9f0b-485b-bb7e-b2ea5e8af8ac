/* eslint-disable @typescript-eslint/unbound-method */
import { expect, test } from "@jest/globals";
import { addMinutes, isSameDay, isEqual } from "date-fns";

import { createMember, createWorkspace } from "../utils/testUtils";

import { ActivityLog, NoOpActivityLog } from "./activityLog";
import {
  createPosition,
  getMemberPosition,
  moveMemberToManager,
} from "./org-chart";
import {
  getNextAccruals,
  getNextReset,
  approveTimeOffRequest,
  deleteTimeOffRequest,
  rejectTimeOffRequest,
  requestTimeOff,
  TimeOffDuplicateError,
  TimeOffInvalidApproversError,
  TimeOffStartDateCantBeInThePastError,
  discussTimeOffRequest,
  editTimeOffRequest,
  calculateTimeOffDuration,
  calculateWorkingDaysStats,
} from "./time-offs";

import {
  AccrualsFrequency,
  Color,
  Logger,
  Member,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lackAdapter,
  TimeOffRequest,
  Workspace,
  YearStart,
  TimeOffRequestStatus,
} from ".";

const initialRequester = createMember({
  overrides: {
    id: "U1",
    isAdmin: false,
    timeOffs: {
      VACATION: { balance: 30, nextResetAt: null },
    },
  },
});
const initialApprover = createMember({
  overrides: {
    id: "U2",
    isAdmin: false,
  },
});
const initialAdmin = createMember({
  overrides: {
    id: "U3",
    isAdmin: true,
    isSlackWorkspaceAdmin: true,
  },
});
const initialManager = createMember({
  overrides: {
    id: "U4",
    isAdmin: false,
  },
});
const initialSomeOtherGuy = createMember({
  overrides: {
    id: "U5",
    isAdmin: false,
  },
});
const initialWorkspace = createWorkspace({
  overrides: {
    id: "T1",
    timeOffs: {
      requests: [],
      types: [
        {
          id: "VACATION",
          color: Color.Amber,
          label: "Vacation",
          emoji: "🌴",
          slackEmoji: ":palm_tree:",
        },
      ],
      isEnable: false,
      createDiscussionChannelWhenMultipleApprovers: false,
      changeSlackStatus: true,
      policies: [
        {
          id: "DEFAULT",
          title: "DEFAULT",
          isDefault: true,
          typePolicies: [
            {
              typeId: "VACATION",
              yearStart: YearStart.Calendar,
              onStartQuota: 0,
              rollOverToNextYear: false,
              accrualsQuota: 0,
              accuralsFrequency: AccrualsFrequency.Month,
              nextAccruals: new Date("2032-05-05"),
              maxCapacity: 100,
            },
          ],
          workDays: [1, 2, 3, 4, 5],
          includedWeekendDays: [],
          notifyAccruals: false,
        },
      ],
    },
    members: [
      initialRequester,
      initialApprover,
      initialAdmin,
      initialManager,
      initialSomeOtherGuy,
    ],
    slackBotToken: {
      botId: "UX",
      botMemberId: "UX",
      scopes: ["groups:write"],
      token: "xoxb-1",
    },
    slackMemberTokens: {
      [initialAdmin.id]: {
        memberId: initialAdmin.id,
        scopes: [],
        token: "xoxp-1",
      },
    },
  },
});

describe("Time Offs", () => {
  let workspace: ReadonlyDeep<Workspace>;
  let requester: ReadonlyDeep<Member>;
  let approver: ReadonlyDeep<Member>;
  let admin: ReadonlyDeep<Member>;
  let manager: ReadonlyDeep<Member>;
  let someOtherGuy: ReadonlyDeep<Member>;
  let activityLog: ActivityLog;
  let slackAdapter: SlackAdapter;
  let logger: Logger;
  let now: Date;

  beforeEach(() => {
    workspace = initialWorkspace;
    requester = initialRequester;
    approver = initialApprover;
    admin = initialAdmin;
    manager = initialManager;
    someOtherGuy = initialSomeOtherGuy;
    activityLog = new NoOpActivityLog();
    slackAdapter = {
      sendTimeOffRequest: jest
        .fn()
        .mockImplementation((_timeOff, channel: string) => ({
          channel,
          ts: "111.000",
        })),
      updateTimeOffRequest: jest.fn().mockResolvedValue(undefined),
      isDirectMessageChannel: (channelId: string) =>
        channelId.startsWith("D") || channelId.startsWith("U"),
      notifyRequesterAboutTimeOff: jest
        .fn()
        .mockResolvedValue({ ts: "222.000" }),
      notifyMemberMangerAboutTimeOffStatus: jest
        .fn()
        .mockResolvedValue({ ts: "333.000" }),
      notifyThatApprovedTimeOffWasDeleted: jest
        .fn()
        .mockResolvedValue(undefined),
      sendReminderAboutTimeOffRequest: jest
        .fn()
        .mockResolvedValue({ ts: "444.00", channel: "D2" }),
      postTimeOffChangeInChannel: jest
        .fn()
        .mockResolvedValue({ ts: "555.00", channel: "D3" }),
      notifyRequesterAboutTimeOffChange: jest
        .fn()
        .mockResolvedValue({ ts: "555.00", channel: "D3" }),
      postTimeOffInChannel: jest.fn().mockResolvedValue(undefined),
      archiveChannel: jest.fn().mockResolvedValue(undefined),
      createPrivateChannel: jest.fn().mockResolvedValue({ channel: "C1" }),
      updateMember: jest.fn().mockResolvedValue(undefined),
      deleteReminder: jest.fn(),
    } as unknown as SlackAdapter;
    logger = { info: () => {}, error: () => {} } as Logger;
    now = new Date();
  });

  describe("when requesting a time off", () => {
    test("disallow to specify past start and end dates", async () => {
      now = new Date("2024-06-17T00:00:00");

      await expect(
        requestTimeOff(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          requester.id,
          {
            type: "VACATION",
            approversIds: [approver.id],
            startDate: "2024-06-15",
            endDate: "2024-06-16",
          }
        )
      ).rejects.toBeInstanceOf(TimeOffStartDateCantBeInThePastError);
      await expect(
        requestTimeOff(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          requester.id,
          {
            type: "VACATION",
            approversIds: [approver.id],
            startDate: "2024-06-15",
            endDate: "2024-06-18",
          }
        )
      ).rejects.toBeInstanceOf(TimeOffStartDateCantBeInThePastError);
    });

    test("disallow to specify invalid member as an approver", async () => {
      now = new Date("2024-06-17T00:00:00");

      await expect(
        requestTimeOff(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          requester.id,
          {
            type: "VACATION",
            approversIds: ["INVALID"],
            startDate: "2024-06-17",
            endDate: "2024-06-18",
          }
        )
      ).rejects.toBeInstanceOf(TimeOffInvalidApproversError);
    });

    test("disallow to create a duplicate", async () => {
      now = new Date("2024-06-17T00:00:00");

      [workspace] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-17",
          endDate: "2024-06-18",
        }
      );

      await expect(
        requestTimeOff(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          requester.id,
          {
            type: "VACATION",
            approversIds: [approver.id],
            startDate: "2024-06-17",
            endDate: "2024-06-18",
          }
        )
      ).rejects.toBeInstanceOf(TimeOffDuplicateError);
    });

    test("send approvers a notification", async () => {
      now = new Date("2024-06-17T00:00:00");

      [workspace] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-17",
          endDate: "2024-06-18",
        }
      );

      expect(slackAdapter.sendTimeOffRequest).toHaveBeenCalledTimes(1);
      expect(slackAdapter.sendTimeOffRequest).toHaveBeenCalledWith(
        expect.anything(),
        approver.id,
        expect.anything()
      );
    });

    test("create a discussion channel if an admin enabled this and there are two or more approvers", async () => {
      now = new Date("2024-06-17T00:00:00");

      workspace = {
        ...workspace,
        timeOffs: {
          ...workspace.timeOffs,
          createDiscussionChannelWhenMultipleApprovers: true,
        },
      };

      [workspace] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id, admin.id],
          startDate: "2024-06-17",
          endDate: "2024-06-18",
        }
      );

      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledTimes(1);
    });
  });

  describe("when editing a time off", () => {
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      now = new Date("2024-06-17T00:00:00");
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    test("allow editing for an admin", async () => {
      await expect(
        editTimeOffRequest(
          activityLog,
          slackAdapter,
          workspace,
          admin,
          timeOff,
          {
            type: "VACATION",
            startDate: "2024-06-21",
            endDate: "2024-06-22",
          }
        )
      ).resolves.toBeTruthy();
    });

    test("disallow editing for anyone else", async () => {
      await expect(
        editTimeOffRequest(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          timeOff,
          {
            type: "VACATION",
            startDate: "2024-06-21",
            endDate: "2024-06-22",
          }
        )
      ).rejects.toBeTruthy();
      await expect(
        editTimeOffRequest(
          activityLog,
          slackAdapter,
          workspace,
          manager,
          timeOff,
          {
            type: "VACATION",
            startDate: "2024-06-21",
            endDate: "2024-06-22",
          }
        )
      ).rejects.toBeTruthy();
      await expect(
        editTimeOffRequest(
          activityLog,
          slackAdapter,
          workspace,
          approver,
          timeOff,
          {
            type: "VACATION",
            startDate: "2024-06-21",
            endDate: "2024-06-22",
          }
        )
      ).rejects.toBeTruthy();
      await expect(
        editTimeOffRequest(
          activityLog,
          slackAdapter,
          workspace,
          someOtherGuy,
          timeOff,
          {
            type: "VACATION",
            startDate: "2024-06-21",
            endDate: "2024-06-22",
          }
        )
      ).rejects.toBeTruthy();
    });

    test("post an announcement to the channel if time off was approved", async () => {
      workspace = {
        ...workspace,
        timeOffs: {
          ...workspace.timeOffs,
          channelId: "C1",
          isEnable: true,
        },
      };

      expect(slackAdapter.postTimeOffChangeInChannel).toHaveBeenCalledTimes(0);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );
      await editTimeOffRequest(
        activityLog,
        slackAdapter,
        workspace,
        admin,
        timeOff,
        {
          type: "VACATION",
          startDate: "2024-06-21",
          endDate: "2024-06-22",
        }
      );

      expect(slackAdapter.postTimeOffChangeInChannel).toHaveBeenCalledTimes(1);
      expect(slackAdapter.postTimeOffInChannel).toHaveBeenCalledWith(
        expect.anything(),
        workspace.timeOffs.channelId,
        expect.anything()
      );
    });

    test("notify requester", async () => {
      expect(
        slackAdapter.notifyRequesterAboutTimeOffChange
      ).toHaveBeenCalledTimes(0);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      await editTimeOffRequest(
        activityLog,
        slackAdapter,
        workspace,
        admin,
        timeOff,
        {
          type: "VACATION",
          startDate: "2024-06-21",
          endDate: "2024-06-22",
        }
      );

      expect(
        slackAdapter.notifyRequesterAboutTimeOffChange
      ).toHaveBeenCalledTimes(1);
    });

    test("recalculate time offs balance if it was approved", async () => {
      expect(requester.timeOffs.VACATION?.balance).toEqual(30);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );
      [workspace, timeOff] = await editTimeOffRequest(
        activityLog,
        slackAdapter,
        workspace,
        admin,
        timeOff,
        {
          type: "VACATION",
          startDate: "2024-06-21",
          endDate: "2024-06-28",
        }
      );

      requester = workspace.members.find(
        (member) => member.id === requester.id
      )!;

      expect(requester.timeOffs.VACATION?.balance).toEqual(24);
    });

    test("leave time offs balance intact if it wasn't approved", async () => {
      expect(requester.timeOffs.VACATION?.balance).toEqual(30);

      [workspace, timeOff] = await editTimeOffRequest(
        activityLog,
        slackAdapter,
        workspace,
        admin,
        timeOff,
        {
          type: "VACATION",
          startDate: "2024-06-21",
          endDate: "2024-06-28",
        }
      );

      requester = workspace.members.find(
        (member) => member.id === requester.id
      )!;

      expect(requester.timeOffs.VACATION?.balance).toEqual(30);
    });
  });

  describe("when approving a time off", () => {
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      now = new Date("2024-06-17T00:00:00");
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    test("allow to approve for an approver", async () => {
      await expect(
        approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        )
      ).resolves.toBeTruthy();
    });

    test("allow to approve for an admin", async () => {
      await expect(
        approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          admin
        )
      ).resolves.toBeTruthy();
    });

    test("disallow to approve for anyone else", async () => {
      await expect(
        approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          requester
        )
      ).rejects.toBeTruthy();
      await expect(
        approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          someOtherGuy
        )
      ).rejects.toBeTruthy();
    });

    test("disallow to approve twice", async () => {
      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      await expect(
        approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        )
      ).rejects.toBeTruthy();
    });

    test("post an announcement to the channel", async () => {
      workspace = {
        ...workspace,
        timeOffs: {
          ...workspace.timeOffs,
          channelId: "C1",
          isEnable: true,
        },
      };

      expect(slackAdapter.postTimeOffInChannel).toHaveBeenCalledTimes(0);

      await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      expect(slackAdapter.postTimeOffInChannel).toHaveBeenCalledTimes(1);
      expect(slackAdapter.postTimeOffInChannel).toHaveBeenCalledWith(
        expect.anything(),
        workspace.timeOffs.channelId,
        expect.anything()
      );
    });

    test("don't remind approver anymore", async () => {
      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(0);

      await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(1);
    });

    test("notify requester about approve", async () => {
      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(0);

      await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(1);
    });

    test("notify org chart manager about approve if he wasn't among approvers", async () => {
      let position = getMemberPosition(workspace, requester);

      if (!position) {
        [workspace, position] = createPosition(workspace, requester.id);
      }

      const moveResult = moveMemberToManager(workspace, requester, position, {
        memberId: manager.id,
      });

      workspace = moveResult.workspace;

      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(0);

      await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      expect(
        slackAdapter.notifyMemberMangerAboutTimeOffStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        slackAdapter.notifyMemberMangerAboutTimeOffStatus
      ).toHaveBeenCalledWith(
        workspace.id,
        manager.id,
        expect.anything(),
        expect.anything()
      );
    });

    test("archive the discussion channel", async () => {
      timeOff = await discussTimeOffRequest(
        workspace,
        slackAdapter,
        logger,
        timeOff
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(0);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(1);
    });

    test("decrement time offs balance", async () => {
      expect(requester.timeOffs.VACATION?.balance).toEqual(30);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      requester = workspace.members.find(
        (member) => member.id === requester.id
      )!;

      expect(requester.timeOffs.VACATION?.balance).toEqual(28);
    });
  });

  describe("when rejecting a time off", () => {
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      now = new Date("2024-06-17T00:00:00");
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    test("allow to reject for an approver", async () => {
      await expect(
        rejectTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          null,
          approver
        )
      ).resolves.toBeTruthy();
    });

    test("allow to reject for an admin", async () => {
      await expect(
        rejectTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          null,
          admin
        )
      ).resolves.toBeTruthy();
    });

    test("disallow to reject for anyone else", async () => {
      await expect(
        rejectTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          null,
          requester
        )
      ).rejects.toBeTruthy();
      await expect(
        rejectTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          null,
          someOtherGuy
        )
      ).rejects.toBeTruthy();
    });

    test("disallow to reject twice", async () => {
      [, timeOff] = await rejectTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        null,
        approver
      );

      await expect(
        rejectTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          null,
          approver
        )
      ).rejects.toBeTruthy();
    });

    test("don't remind approver anymore", async () => {
      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(0);

      await rejectTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        null,
        approver
      );

      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(1);
    });

    test("notify requester about reject", async () => {
      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(0);

      await rejectTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        null,
        approver
      );

      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(1);
    });

    test("notify org chart manager about reject if he wasn't among approvers", async () => {
      let position = getMemberPosition(workspace, requester);

      if (!position) {
        [workspace, position] = createPosition(workspace, requester.id);
      }

      const moveResult = moveMemberToManager(workspace, requester, position, {
        memberId: manager.id,
      });

      workspace = moveResult.workspace;

      expect(slackAdapter.notifyRequesterAboutTimeOff).toHaveBeenCalledTimes(0);

      await rejectTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        null,
        approver
      );

      expect(
        slackAdapter.notifyMemberMangerAboutTimeOffStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        slackAdapter.notifyMemberMangerAboutTimeOffStatus
      ).toHaveBeenCalledWith(
        workspace.id,
        manager.id,
        expect.anything(),
        expect.anything()
      );
    });

    test("archive the discussion channel", async () => {
      timeOff = await discussTimeOffRequest(
        workspace,
        slackAdapter,
        logger,
        timeOff
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(0);

      [, timeOff] = await rejectTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        null,
        approver
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(1);
    });
  });

  describe("when approver ignores a time off for a while", () => {
    test("remind approver to take action", async () => {
      now = new Date("2024-06-17T12:34:00");

      [workspace] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-25",
          endDate: "2024-06-26",
        }
      );

      expect(
        slackAdapter.sendReminderAboutTimeOffRequest
      ).toHaveBeenCalledTimes(1);
      expect(slackAdapter.sendReminderAboutTimeOffRequest).toHaveBeenCalledWith(
        approver.id,
        new Date("2024-06-18T12:34:00"),
        expect.anything()
      );
    });
  });

  describe("when deleting a time off", () => {
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      now = new Date("2024-06-17T00:00:00");
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    test("allow to delete for the requester", async () => {
      await expect(
        deleteTimeOffRequest(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          requester,
          timeOff
        )
      ).resolves.toBeTruthy();
    });

    test("allow to delete for an admin", async () => {
      await expect(
        deleteTimeOffRequest(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          admin,
          timeOff
        )
      ).resolves.toBeTruthy();
    });

    test("disallow to delete for anyone else", async () => {
      await expect(
        deleteTimeOffRequest(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          manager,
          timeOff
        )
      ).rejects.toBeTruthy();
      await expect(
        deleteTimeOffRequest(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          someOtherGuy,
          timeOff
        )
      ).rejects.toBeTruthy();
    });

    test("don't remind approver anymore", async () => {
      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(0);

      await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester,
        timeOff
      );

      expect(slackAdapter.deleteReminder).toHaveBeenCalledTimes(1);
    });

    test("notify requester that the time off has been deleted if admin deletes an approved time off", async () => {
      const channel = "CXXX";
      const ts = "123456.00";

      slackAdapter.notifyRequesterAboutTimeOff = jest
        .fn()
        .mockResolvedValueOnce({ channel, ts });

      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledTimes(0);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        admin
      );

      await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        admin,
        timeOff
      );

      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledTimes(1);
      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledWith(requester.id, admin.id, "REQUESTER_MESSAGE", ts);
    });

    test("notify manager that the time off has been deleted if admin deletes an approved time off", async () => {
      const channel = "CYYYY";
      const ts = "654321.00";

      slackAdapter.notifyMemberMangerAboutTimeOffStatus = jest
        .fn()
        .mockResolvedValueOnce({ channel, ts });

      let position = getMemberPosition(workspace, requester);

      if (!position) {
        [workspace, position] = createPosition(workspace, requester.id);
      }

      const moveResult = moveMemberToManager(workspace, requester, position, {
        memberId: manager.id,
      });

      workspace = moveResult.workspace;

      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledTimes(0);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        admin
      );

      await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        admin,
        timeOff
      );

      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledTimes(2);
      expect(
        slackAdapter.notifyThatApprovedTimeOffWasDeleted
      ).toHaveBeenCalledWith(
        channel,
        admin.id,
        "APPROVED_TIME_OFF_MANAGER_MESSAGE",
        ts
      );
    });

    test("archive the discussion channel", async () => {
      timeOff = await discussTimeOffRequest(
        workspace,
        slackAdapter,
        logger,
        timeOff
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(0);

      workspace = await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester,
        timeOff
      );

      expect(slackAdapter.archiveChannel).toHaveBeenCalledTimes(1);
    });

    test("leave time offs balance intact if time off wasn't approved", async () => {
      expect(requester.timeOffs.VACATION?.balance).toEqual(30);

      workspace = await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester,
        timeOff
      );

      requester = workspace.members.find(
        (member) => member.id === requester.id
      )!;

      expect(requester.timeOffs.VACATION?.balance).toEqual(30);
    });

    test("revert time offs balance if time off was approved", async () => {
      expect(requester.timeOffs.VACATION?.balance).toEqual(30);

      [workspace, timeOff] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now,
        timeOff,
        approver
      );

      workspace = await deleteTimeOffRequest(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        approver,
        timeOff
      );

      requester = workspace.members.find(
        (member) => member.id === requester.id
      )!;

      expect(requester.timeOffs.VACATION?.balance).toEqual(30);
    });
  });

  describe("when approver wants to discuss a time off", () => {
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      now = new Date("2024-06-17T00:00:00");
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id, admin.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    test("create a private channel with all approvers if there are multiple approvers", async () => {
      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledTimes(0);

      await discussTimeOffRequest(workspace, slackAdapter, logger, timeOff);

      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledTimes(1);
      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledWith(
        expect.anything(),
        [approver.id, admin.id]
      );
    });

    test("create a private channel with the requester if there is only one approver", async () => {
      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-21",
          endDate: "2024-06-22",
        }
      );

      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledTimes(0);

      await discussTimeOffRequest(workspace, slackAdapter, logger, timeOff);

      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledTimes(1);
      expect(slackAdapter.createPrivateChannel).toHaveBeenCalledWith(
        expect.anything(),
        [approver.id, requester.id]
      );
    });
  });

  test("should return the beginning of the day for the next accruals", () => {
    const sunday = new Date("2024-05-19");
    const monday = new Date("2024-05-20");

    const monday1 = getNextAccruals(AccrualsFrequency.Week, sunday);
    const monday2 = getNextAccruals(
      AccrualsFrequency.Week,
      addMinutes(sunday, 10)
    );
    const nextMonday = getNextAccruals(AccrualsFrequency.Week, monday);

    expect(
      isSameDay(monday, monday1) && isEqual(monday1, monday2)
    ).toBeTruthy();
    expect(isSameDay(nextMonday, new Date("2024-05-27"))).toBeTruthy();
    const nextMonth = getNextAccruals(AccrualsFrequency.Month, monday);

    expect(isSameDay(nextMonth, new Date("2024-06-01"))).toBeTruthy();
  });

  test("should return the beginning of the day for the next reset", () => {
    const sunday = new Date("2024-05-19");

    const nextYear1 = getNextReset(sunday);
    const nextYear2 = getNextReset(addMinutes(sunday, 10));

    expect(
      isEqual(nextYear1, nextYear2) &&
        isSameDay(nextYear1, new Date("2025-01-01"))
    ).toBeTruthy();
  });

  test("should return correct time off duration", () => {
    const sunday = new Date("2024-12-29");
    const friday = new Date("2024-12-27");
    const thursday = new Date("2025-01-02");
    const nextSunday = new Date("2025-01-05");
    const startOfMonth = new Date("2024-12-01");

    now = startOfMonth;

    let duration = calculateTimeOffDuration(
      workspace,
      requester,
      friday,
      thursday
    );

    expect(duration).toEqual(5);

    workspace = includeWeekendDays(workspace);
    duration = calculateTimeOffDuration(workspace, requester, friday, thursday);

    expect(duration).toEqual(7);

    workspace = excludeWeekendDays(workspace);
    [workspace, requester] = addUSAOfficialHolidays(workspace, requester);
    duration = calculateTimeOffDuration(workspace, requester, friday, thursday);

    expect(duration).toEqual(4);

    [workspace, requester] = removeOfficialHolidays(workspace, requester);
    duration = calculateTimeOffDuration(workspace, requester, sunday, thursday);

    expect(duration).toEqual(4);

    duration = calculateTimeOffDuration(
      workspace,
      requester,
      thursday,
      nextSunday
    );

    expect(duration).toEqual(2);
  });

  test("should correctly calculate time off duration with observable interval", () => {
    const timeOffStart = new Date("2024-12-23");
    const timeOffEnd = new Date("2024-12-30");
    const friday = new Date("2024-12-27");
    const saturday = new Date("2024-12-28");
    const sunday = new Date("2024-12-29");
    const nextFriday = new Date("2025-01-03");

    const workingWeek = {
      start: timeOffStart,
      end: friday,
    };

    let duration = calculateTimeOffDuration(
      workspace,
      requester,
      timeOffStart,
      timeOffEnd,
      workingWeek
    );

    expect(duration).toEqual(5);

    workspace = includeWeekendDays(workspace);

    const workingWeekAndSaturday = {
      start: timeOffStart,
      end: saturday,
    };

    duration = calculateTimeOffDuration(
      workspace,
      requester,
      timeOffStart,
      timeOffEnd,
      workingWeekAndSaturday
    );
    expect(duration).toEqual(6);

    const weekend = {
      start: saturday,
      end: sunday,
    };

    duration = calculateTimeOffDuration(
      workspace,
      requester,
      timeOffStart,
      timeOffEnd,
      weekend
    );

    expect(duration).toEqual(2);

    const nextWeek = {
      start: timeOffEnd,
      end: nextFriday,
    };

    duration = calculateTimeOffDuration(
      workspace,
      requester,
      timeOffStart,
      timeOffEnd,
      nextWeek
    );

    expect(duration).toEqual(1);
  });
});

function includeWeekendDays(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      policies: [
        { ...workspace.timeOffs.policies[0], includedWeekendDays: [6, 7] },
      ],
    },
  };
}

function excludeWeekendDays(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      policies: [
        { ...workspace.timeOffs.policies[0], includedWeekendDays: [] },
      ],
    },
  };
}

function addUSAOfficialHolidays(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): [ReadonlyDeep<Workspace>, ReadonlyDeep<Member>] {
  workspace = {
    ...workspace,
    holidays: [
      {
        id: "1",
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-01-01"),
        name: "New Year",
        description: "",
        isOfficial: true,
        country: "USA",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "2",
        startDate: new Date("2024-11-28"),
        endDate: new Date("2024-11-28"),
        name: "Thanksgiving Day",
        description: "",
        isOfficial: true,
        country: "USA",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
  };
  member = {
    ...member,
    country: "USA",
  };

  return [workspace, member];
}

function removeOfficialHolidays(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): [ReadonlyDeep<Workspace>, ReadonlyDeep<Member>] {
  workspace = {
    ...workspace,
    holidays: [],
  };
  member = {
    ...member,
    country: undefined,
  };

  return [workspace, member];
}

describe("calculateWorkingDaysStats", () => {
  const baseMember = {
    ...initialRequester,
    joinedAt: new Date("2025-05-30"),
  };
  const baseWorkspace = {
    ...initialWorkspace,
    holidays: [],
    timeOffs: {
      ...initialWorkspace.timeOffs,
      requests: [],
    },
  };

  test("counts all working days in range when no holidays or time off", () => {
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-06"),
    }; // Mon-Fri
    const result = calculateWorkingDaysStats(
      baseWorkspace,
      baseMember,
      range,
      new Date("2025-06-06")
    );

    expect(result.totalWorkingDays).toBe(5);
    expect(result.workedDays).toBe(5);
  });

  test("starts counting from joinedAt if joined during range", () => {
    const member = { ...baseMember, joinedAt: new Date("2025-06-05") };
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-06"),
    };
    const result = calculateWorkingDaysStats(
      baseWorkspace,
      member,
      range,
      new Date("2025-06-06")
    );

    expect(result.totalWorkingDays).toBe(2); // 5,6 June (Thu-Fri)
    expect(result.workedDays).toBe(2);
  });

  test("returns 0 workedDays if joined after range", () => {
    const member = { ...baseMember, joinedAt: new Date("2025-06-10") };
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-06"),
    };
    const result = calculateWorkingDaysStats(
      baseWorkspace,
      member,
      range,
      new Date("2025-06-11")
    );

    expect(result.totalWorkingDays).toBe(5);
    expect(result.workedDays).toBe(0);
  });

  test("excludes holidays from working days", () => {
    const workspace = {
      ...baseWorkspace,
    };
    const member = { ...baseMember, joinedAt: new Date("2024-11-01") };

    const [workspaceWithUSAHolidays, memberWithCUSAountry] =
      addUSAOfficialHolidays(workspace, member);

    const range = {
      start: new Date("2024-11-25"),
      end: new Date("2024-11-29"),
    };
    const result = calculateWorkingDaysStats(
      workspaceWithUSAHolidays,
      memberWithCUSAountry,
      range,
      new Date("2024-11-28")
    );

    expect(result.totalWorkingDays).toBe(4); // 28th November is a USA holiday
    expect(result.workedDays).toBe(3);
  });

  test("excludes approved time off days from working days", () => {
    const workspace = {
      ...baseWorkspace,
      timeOffs: {
        ...baseWorkspace.timeOffs,
        requests: [
          {
            id: "to1",
            memberId: baseMember.id,
            approversIds: [initialApprover.id],
            createdAt: new Date("2025-06-02"),
            updatedAt: new Date("2025-06-02"),
            startDate: new Date("2025-06-04"),
            endDate: new Date("2025-06-04"),
            type: "VACATION",
            status: TimeOffRequestStatus.Approved,
            notifications: [],
          },
        ],
      },
    };
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-06"),
    };
    const result = calculateWorkingDaysStats(
      workspace as ReadonlyDeep<Workspace>,
      baseMember,
      range,
      new Date("2025-06-06")
    );

    expect(result.totalWorkingDays).toBe(5);
    expect(result.workedDays).toBe(4); // 4th June is time off
  });

  test("workedDays is less than totalWorkingDays if workedUntil is before end of the range", () => {
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-06"),
    };
    const result = calculateWorkingDaysStats(
      baseWorkspace,
      baseMember,
      range,
      new Date("2025-06-05")
    );

    expect(result.totalWorkingDays).toBe(5);
    expect(result.workedDays).toBe(4); // 2d, 3rd, 4th, 5th
  });

  test("only counts workDays from policy (no weekends)", () => {
    const range = {
      start: new Date("2025-06-02"),
      end: new Date("2025-06-09"),
    }; // Mon-Sun
    const result = calculateWorkingDaysStats(
      baseWorkspace,
      baseMember,
      range,
      new Date("2025-06-09")
    );

    expect(result.totalWorkingDays).toBe(6); // Mon-Fri and Mon
    expect(result.workedDays).toBe(6);
  });
});
