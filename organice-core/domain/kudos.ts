/* eslint-disable max-classes-per-file */

import assert from "assert";
import crypto from "crypto";

import {
  format,
  subWeeks,
  subMonths,
  addWeeks,
  addMonths,
  setDate,
} from "date-fns";

import {
  Logger,
  KudosSettings,
  Member,
  ReadonlyDeep,
  SlackAdapter,
  Workspace,
  convertTime12To24,
  KudosResetFrequency,
} from ".";

export interface KudosQueryHandler {
  getSentKudosQuantity(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    from: Date,
    to?: Date
  ): Promise<number>;
}

export interface KudosRepository {
  addMany(workspaceId: string, kudos: ReadonlyDeep<Kudos[]>): Promise<void>;
}

export interface Kudos {
  id: string;
  quantity: number;
  createdAt: Date;
  senderId: string;
  receiverId: string;
  message: string;
  channel: string;
  messageTS: string;
  gifUrl?: string;
  valuesIds: string[];
}

export function getMemberKudosQuotaLeft(
  kudosSettings: ReadonlyDeep<KudosSettings>,
  quantitySentThisCycle: number
): number {
  return Math.max(0, kudosSettings.kudosLimit - quantitySentThisCycle);
}

export function getNextCycleReset(
  kudosSettings: ReadonlyDeep<KudosSettings>,
  now: Date
): Date {
  const cycleStart = getCurrentCycleStart(kudosSettings, now);
  const { resetFrequency } = kudosSettings;

  if (resetFrequency === KudosResetFrequency.Week) {
    return addWeeks(cycleStart, 1);
  }

  return addMonths(cycleStart, 1);
}

export function getCurrentCycleStart(
  kudosSettings: ReadonlyDeep<KudosSettings>,
  now: Date
): Date {
  const { resetFrequency, resetDay, resetTime, resetTimezone } = kudosSettings;

  let date = now;

  const timeOfPosting = convertTime12To24(resetTime);

  if (resetFrequency === KudosResetFrequency.Week) {
    // NOTE: resetDay for Week frequency has values from 1 to 7
    const normalizedWeekDay = resetDay % 7;

    const diff = (now.getDay() - normalizedWeekDay + 7) % 7;

    date = setDate(date, now.getDate() - diff);
  }

  if (resetFrequency === KudosResetFrequency.Month) {
    date = setDate(date, resetDay);
  }

  const cycleStart = new Date(
    `${format(date, "yyyy-MM-dd")} ${timeOfPosting} ${resetTimezone}`
  );

  if (cycleStart > now) {
    if (resetFrequency === KudosResetFrequency.Week) {
      return subWeeks(cycleStart, 1);
    }

    return subMonths(cycleStart, 1);
  }

  return cycleStart;
}

export function isCurrentCycleKudos(
  kudos: ReadonlyDeep<Kudos>,
  kudosSettings: ReadonlyDeep<KudosSettings>,
  now: Date
): boolean {
  const currentCycleStart = getCurrentCycleStart(kudosSettings, now);

  return kudos.createdAt > currentCycleStart;
}

export class CantGiveKudosToYourselfError extends Error {}

export class NotEnoughKudosLeftError extends Error {
  constructor(public kudosLeftNumber: number) {
    super();
  }
}
interface GiveKudosInput {
  kudosMessage: string;
  recipientsIds: string[];
  quantity: number;
  selectedGIF: string | undefined;
  kudosValuesIds: string[];
}

interface GiveKudosOptions {
  onValidationPass?: () => Promise<void>;
}

export async function giveKudos(
  slackAdapter: SlackAdapter,
  logger: Logger,
  queryHandler: KudosQueryHandler,
  workspace: ReadonlyDeep<Workspace>,
  giver: ReadonlyDeep<Member>,
  input: GiveKudosInput,
  options?: GiveKudosOptions
): Promise<ReadonlyDeep<Kudos[]>> {
  const { recipientsIds, quantity, kudosMessage, selectedGIF } = input;

  assert(
    recipientsIds.every((id) => workspace.members.some((m) => m.id === id)),
    `Recepient should be a workspace member`
  );

  if (recipientsIds.includes(giver.id)) {
    throw new CantGiveKudosToYourselfError();
  }

  const quantitySentThisCycle = await queryHandler.getSentKudosQuantity(
    workspace,
    giver,
    getCurrentCycleStart(workspace.kudosSettings, new Date())
  );
  let kudosLeft = getMemberKudosQuotaLeft(
    workspace.kudosSettings,
    quantitySentThisCycle
  );

  const kudosToSend = quantity * recipientsIds.length;

  if (kudosLeft < kudosToSend) {
    throw new NotEnoughKudosLeftError(kudosLeft);
  }

  kudosLeft -= kudosToSend;

  await options?.onValidationPass?.();

  logger = logger.withContext({
    member: giver,
    recipientsIds,
    kudosMessage,
    quantity,
  });

  let result: {
    directMessages: { ts: string; channel: string }[];
    publicChannel: { ts: string; channel: string } | null;
  };

  if (workspace.kudosSettings.enable && workspace.kudosSettings.channel) {
    const publicChannel = await slackAdapter.postKudosInChannel(
      giver.id,
      workspace.kudosSettings.channel.id,
      recipientsIds,
      {
        quantity,
        message: kudosMessage,
        gifUrl: selectedGIF,
        kudosValues: workspace.kudosSettings.values.filter((item) =>
          input.kudosValuesIds.includes(item.id)
        ),
      }
    );

    logger.info(`Member sent kudos in public channel`);

    result = {
      publicChannel,
      directMessages: [],
    };
  } else {
    const directMessages = await slackAdapter.sendKudosAsDirectMessage(
      giver.id,
      recipientsIds,
      {
        quantity,
        message: kudosMessage,
        gifUrl: selectedGIF,
        kudosValues: workspace.kudosSettings.values.filter((item) =>
          input.kudosValuesIds.includes(item.id)
        ),
      }
    );

    logger.info(`Member sent kudos as direct message`);

    result = {
      directMessages,
      publicChannel: null,
    };
  }

  void notifyAboutSendingKudos(slackAdapter, logger, {
    senderId: giver.id,
    recipientsIds,
    kudosLeft,
  }).catch((error): void => {
    logger.error(error);
  });

  const testCreatedAt = /TESTDATE="(.+?)"/.exec(kudosMessage)?.[1];
  const createdAt = testCreatedAt ? new Date(testCreatedAt) : new Date();
  const kudos: Kudos[] = recipientsIds.map((id, idx) => ({
    id: crypto.randomBytes(10).toString("hex"),
    createdAt,
    receiverId: id,
    senderId: giver.id,
    quantity,
    message: kudosMessage,
    gifUrl: selectedGIF,
    messageTS: result.publicChannel
      ? result.publicChannel.ts
      : result.directMessages[idx].ts,
    channel: result.publicChannel
      ? result.publicChannel.channel
      : result.directMessages[idx].channel,
    valuesIds: input.kudosValuesIds.filter((valueId) =>
      workspace.kudosSettings.values.some((item) => item.id === valueId)
    ),
  }));

  return kudos;
}

async function notifyAboutSendingKudos(
  slackAdapter: SlackAdapter,
  logger: Logger,
  data: {
    kudosLeft: number;
    recipientsIds: string[];
    senderId: string;
  }
): Promise<void> {
  const { kudosLeft, recipientsIds, senderId } = data;

  await slackAdapter.notifyAboutSendingKudos(
    senderId,
    recipientsIds,
    kudosLeft
  );

  logger.info(`Notify member about sending kudos`);
}
