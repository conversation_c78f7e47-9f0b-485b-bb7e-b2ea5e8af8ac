/* eslint-disable import/no-cycle */
import assert from "assert";
import crypto from "crypto";

import { collectMemberData } from "./data-completion";

import {
  ReadonlyDeep,
  Workspace,
  Member,
  PresetPolicyId,
  Position,
  RootNode,
  Department,
  Color,
  OrgChartPredictor,
  PredictOrgChartInput,
  PredictedRootNode,
  PredictedDepartment,
  PredictedPosition,
  Team,
  replaceMember,
} from ".";

export function replaceNode(
  initialRootNode: ReadonlyDeep<RootNode>,
  nodeUpdate: ReadonlyDeep<RootNode | Position | Department>
): ReadonlyDeep<RootNode> {
  if (nodeUpdate.type === "root") {
    return nodeUpdate as RootNode;
  }

  const { path } = searchNodeWithPathOfNodes(initialRootNode, nodeUpdate.id);

  assert(path, `Can't find node with 'id' = '${nodeUpdate.id}'`);

  return [initialRootNode, ...path.slice(0, -1), nodeUpdate].reduceRight(
    (child, parent) => {
      if (parent.type === "position" || parent.type === "department") {
        assert(
          child.type === "position" || child.type === "department",
          "Only a position or a department can be a subordinate to the position"
        );

        const subordinates = [...parent.subordinates];
        const childIndex = subordinates.findIndex((n) => n.id === child.id);

        assert(childIndex !== -1, "Node not found");

        subordinates[childIndex] = child;

        return { ...parent, subordinates };
      }

      assert(
        child.type === "position" || child.type === "department",
        "Only a position or a department can be a subordinate to the position"
      );

      const subordinates = [...parent.subordinates];
      const childIndex = subordinates.findIndex((n) => n.id === child.id);

      assert(childIndex !== -1, "Node not found");

      subordinates[childIndex] = child;

      return { ...parent, subordinates };
    }
  ) as RootNode;
}

export function searchNodeWithPathOfNodes(
  rootNode: ReadonlyDeep<RootNode>,
  nodeId: string
): {
  node: ReadonlyDeep<Position | Department | null>;
  path: ReadonlyDeep<Position | Department>[] | null;
} {
  const stack: [
    ReadonlyDeep<RootNode | Position | Department>,
    ReadonlyDeep<Position | Department>[]
  ][] = [[rootNode, []]];

  while (stack.length > 0) {
    const [node, path] = stack.pop()!;

    if (node.id === nodeId && node.type !== "root") {
      return { node, path };
    }

    if (node.subordinates.length > 0) {
      for (const sub of node.subordinates) {
        stack.push([sub, [...path, sub]]);
      }
    }
  }

  return { node: null, path: null };
}

export function getParentDepartment(
  workspace: ReadonlyDeep<Workspace>,
  currentNodeId: string
): Department | null {
  const { path } = searchNodeWithPathOfNodes(
    workspace.orgTree.rootNode,
    currentNodeId
  );

  if (!path) {
    return null;
  }
  const departmentsAndCurrentNode = path.filter(
    (n): n is Department => n.type === "department" || n.id === currentNodeId
  );
  const parentDepartment = departmentsAndCurrentNode.at(-2) ?? null;

  return parentDepartment;
}

export function getParentPosition(
  workspace: ReadonlyDeep<Workspace>,
  currentNodeId: string
): Position | null {
  const { path } = searchNodeWithPathOfNodes(
    workspace.orgTree.rootNode,
    currentNodeId
  );

  if (!path) {
    return null;
  }
  const positionsAndCurrentNode = path.filter(
    (n): n is Position => n.type === "position" || n.id === currentNodeId
  );
  const parentPosition = positionsAndCurrentNode.at(-2) ?? null;

  return parentPosition;
}

export function getSubordinatesInfo(
  subordinates: ReadonlyDeep<(Department | Position)[]>
): {
  assignedPositionNumber: number;
  allPositionNumber: number;
} {
  return subordinates.reduce(
    (sum, curr) => {
      const info = {
        assignedPositionNumber:
          curr.type === "position" && curr.memberId ? 1 : 0,
        allPositionNumber: curr.type === "position" ? 1 : 0,
      };

      const currentInfo = info;

      if (curr.subordinates.length > 0) {
        const { assignedPositionNumber, allPositionNumber } =
          getSubordinatesInfo(curr.subordinates);

        currentInfo.assignedPositionNumber += assignedPositionNumber;
        currentInfo.allPositionNumber += allPositionNumber;
      }

      return {
        assignedPositionNumber:
          sum.assignedPositionNumber + currentInfo.assignedPositionNumber,
        allPositionNumber:
          sum.allPositionNumber + currentInfo.allPositionNumber,
      };
    },
    {
      assignedPositionNumber: 0,
      allPositionNumber: 0,
    }
  );
}

export function getNodes<S extends Position | Department>(
  rootNode: ReadonlyDeep<RootNode | Position | Department>,
  callback: (
    currentNode: ReadonlyDeep<RootNode | Position | Department>
  ) => currentNode is S,
  resultLength = Infinity
): ReadonlyDeep<S>[] {
  const stack: ReadonlyDeep<RootNode | Position | Department>[] = [rootNode];
  let node;
  const nodes: ReadonlyDeep<S>[] = [];
  let counter = 0;

  while (stack.length > 0) {
    node = stack.pop();

    if (
      node &&
      node.type !== "root" &&
      callback(node) &&
      counter <= resultLength
    ) {
      counter += 1;
      nodes.push(node);
    }

    if (node?.subordinates && node.subordinates.length > 0) {
      for (const sub of node.subordinates) {
        stack.push(sub);
      }
    }
  }

  return nodes.length ? nodes : [];
}

export function getNode<S extends RootNode | Position | Department>(
  rootNode: ReadonlyDeep<RootNode | Position | Department>,
  callback: (
    currentNode: ReadonlyDeep<RootNode | Position | Department>
  ) => currentNode is S
): ReadonlyDeep<S | null> {
  const stack: ReadonlyDeep<RootNode | Position | Department>[] = [rootNode];
  let node;

  while (stack.length > 0) {
    node = stack.pop();

    if (node && callback(node)) {
      return node;
    }

    if (node?.subordinates && node.subordinates.length > 0) {
      for (const sub of node.subordinates) {
        stack.push(sub);
      }
    }
  }

  return null;
}

export function getManagerEmptyPositions(
  rootNode: ReadonlyDeep<RootNode>,
  managerId: string
): ReadonlyDeep<Position>[] {
  const managerPosition = getNode<Position>(
    rootNode,
    (node): node is Position =>
      node.type === "position" && node.memberId === managerId
  );

  if (!managerPosition) return [];

  const stack: ReadonlyDeep<Position | Department>[] = [
    ...managerPosition.subordinates,
  ];
  const nodes: ReadonlyDeep<Position>[] = [];

  while (stack.length > 0) {
    const node = stack.pop() as Position | Department;

    if (
      node.type === "position" &&
      node.memberId === null &&
      node.title !== ""
    ) {
      nodes.push(node);
    }

    if (node.type === "department" && node.subordinates.length > 0) {
      for (const sub of node.subordinates) {
        stack.push(sub);
      }
    }
  }

  return nodes;
}

export function canPredictOrgChart(
  workspace: ReadonlyDeep<Workspace>
): boolean {
  const allNodes = getNodes(
    workspace.orgTree.rootNode,
    (x): x is Position | Department => true
  );

  return Boolean(allNodes.length < 5);
}

export async function predictOrgChart(
  orgChartPredictor: OrgChartPredictor,
  workspace: ReadonlyDeep<Workspace>
): Promise<ReadonlyDeep<Workspace>> {
  assert(
    workspace.predictedOrgTree.requested,
    "Cannot predict org chart until it was requested"
  );

  const input: PredictOrgChartInput = {
    members: workspace.members.map((member) => {
      const memberData = collectMemberData(workspace, member);

      return {
        id: member.id,
        name: member.realName ?? "",
        title: memberData[PresetPolicyId.JOB_TITLE] ?? "",
      };
    }),
  };
  const predictedRootNode = await orgChartPredictor.predictOrgChart(input);
  const rootNode = materializePredictedOrgChart(workspace, predictedRootNode);

  workspace = {
    ...workspace,
    orgTree: { rootNode },
    predictedOrgTree: {
      requested: false,
      rootNode: predictedRootNode,
    },
  };

  return workspace;
}

function materializePredictedOrgChart(
  workspace: ReadonlyDeep<Workspace>,
  predictedRootNode: PredictedRootNode
): RootNode {
  function materializePredictedPosition(
    predictedPosition: PredictedPosition,
    parentId: string
  ): Position {
    const positionId = crypto.randomBytes(10).toString("hex");
    const position: Position = {
      id: positionId,
      timestamp: Date.now(),
      type: "position",
      title: predictedPosition.title,
      memberId: predictedPosition.memberId,
      withoutManagerManual: false,
      parentId,
      teamIds: [],
      managedDepartmentId: null,
      managerTeamIds: [],
      subordinates: predictedPosition.subordinates.map((subordinate) =>
        subordinate.type === "position"
          ? materializePredictedPosition(subordinate, positionId)
          : materializePredictedDepartment(subordinate, positionId)
      ),
      reference: {
        isIncludeToReferrals: false,
        bonusDescription: "",
        jobDescriptionLink: "",
        candidates: [],
      },
    };

    return position;
  }

  function materializePredictedDepartment(
    predictedDepartment: PredictedDepartment,
    parentId: string
  ): Department {
    const colors = Object.values(Color);
    const departmentId = crypto.randomBytes(10).toString("hex");
    const department: Department = {
      id: departmentId,
      timestamp: Date.now(),
      type: "department",
      title: predictedDepartment.name,
      parentId,
      managerId: null,
      color: colors[Math.floor(Math.random() * colors.length)],
      subordinates: predictedDepartment.subordinates.map((subordinate) =>
        subordinate.type === "position"
          ? materializePredictedPosition(subordinate, departmentId)
          : materializePredictedDepartment(subordinate, departmentId)
      ),
    };

    return department;
  }

  const rootNodeId = workspace.orgTree.rootNode.id;
  const rootNode: RootNode = {
    id: rootNodeId,
    type: "root",
    subordinates: predictedRootNode.subordinates.map((subordinate) =>
      materializePredictedPosition(subordinate, rootNodeId)
    ),
  };

  return rootNode;
}

export function getMembersWithoutManager(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Member>[] {
  const membersWithoutManager = workspace.members.filter((m) => {
    const position = getNode(
      workspace.orgTree.rootNode,
      (n): n is Position => n.type === "position" && n.memberId === m.id
    );

    return !position;
  });

  return membersWithoutManager;
}

function getNodeWithAllChildren(
  currentNode: ReadonlyDeep<Position | Department>
): ReadonlyDeep<Position | Department>[] {
  const stack: ReadonlyDeep<Position | Department>[] = [currentNode];
  const nodes: ReadonlyDeep<Position | Department>[] = [];

  while (stack.length > 0) {
    const node = stack.pop()!;

    nodes.push(node);

    if (node.subordinates.length > 0) {
      for (const sub of node.subordinates) {
        stack.push(sub);
      }
    }
  }

  return nodes;
}

export function getPossibleParents(
  workspace: ReadonlyDeep<Workspace>,
  nodeId: string
): {
  possibleMembers: ReadonlyDeep<Member>[];
  possiblePositions: ReadonlyDeep<Position>[];
  possibleOpenPositions: ReadonlyDeep<Position>[];
  possibleDepartments: ReadonlyDeep<Department>[];
} {
  const nodes = getNodes(
    workspace.orgTree.rootNode,
    (n): n is Position | Department =>
      ["position", "department"].includes(n.type)
  );

  const currentNode = nodes.find((n) => n.id === nodeId);

  assert(currentNode, "Current node not found");
  const currentNodeWithAllChildren = getNodeWithAllChildren(currentNode).map(
    (n) => n.id
  );
  const possibleParentPositions: ReadonlyDeep<Position>[] = [];
  const possibleDepartments: ReadonlyDeep<Department>[] = [];

  nodes.forEach((n) => {
    if (!currentNodeWithAllChildren.includes(n.id)) {
      if (n.type === "position") {
        possibleParentPositions.push(n);
      }

      if (n.type === "department") {
        possibleDepartments.push(n);
      }
    }
  });

  const possiblePositions: ReadonlyDeep<Position>[] = [];

  const possibleOpenPositions: ReadonlyDeep<Position>[] = [];

  possibleParentPositions.forEach((p) => {
    const member = workspace.members.find((m) => m.id === p.memberId);

    if (member) {
      possiblePositions.push(p);
    } else {
      possibleOpenPositions.push(p);
    }
  });

  return {
    possibleMembers: getMembersWithoutManager(workspace),
    possiblePositions,
    possibleOpenPositions,
    possibleDepartments,
  };
}

export function getAllDepartments(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Department[]> {
  return getNodes(
    workspace.orgTree.rootNode,
    (n): n is Department => n.type === "department"
  );
}

export function changeNodeOrder<N extends Position | Department>(
  workspace: ReadonlyDeep<Workspace>,
  node: ReadonlyDeep<N>,
  nextItemIndex?: number | null
): {
  workspace: ReadonlyDeep<Workspace>;
  parent: ReadonlyDeep<RootNode | Position | Department>;
  node: ReadonlyDeep<N>;
} {
  const rootNode = workspace.orgTree.rootNode;
  let currentParent = getNode(
    rootNode,
    (n): n is RootNode | Position | Department => n.id === node.parentId
  );

  assert(currentParent, "Parent node not found");

  /**
   * NOTE: don't do extra work if the node has the right position
   */
  if (
    typeof nextItemIndex === "number" &&
    currentParent.subordinates[nextItemIndex]?.id === node.id
  ) {
    return {
      workspace,
      parent: currentParent,
      node,
    };
  }

  const updatedNode: ReadonlyDeep<N> = {
    ...node,
    timestamp: Date.now(),
  };

  let currentParentSubordinates = [...currentParent.subordinates];
  let insertIndex = nextItemIndex ?? currentParentSubordinates.length;

  const currentParentManagerId: ReadonlyDeep<string | null> =
    ("managerId" in currentParent && currentParent.managerId) || null;
  const departmentManagerNode = currentParentSubordinates.find(
    (subordinate) =>
      currentParentManagerId !== null &&
      subordinate.id === currentParentManagerId
  );

  /**
   * NOTE: if node's parent is a department we do not allow any other node
   * rather than that departments manager to take the first postion in the list
   */
  if (departmentManagerNode) {
    if (departmentManagerNode.id === node.id) {
      insertIndex = 0;
    } else {
      insertIndex = insertIndex === 0 ? 1 : insertIndex;
    }
  }

  currentParentSubordinates.splice(insertIndex, 0, updatedNode);
  currentParentSubordinates = currentParentSubordinates.filter(
    (n) => n !== node
  );

  currentParent = {
    ...currentParent,
    subordinates: currentParentSubordinates,
  };

  const updatedRootNode = replaceNode(rootNode, currentParent);

  const updatedWorkspace = {
    ...workspace,
    orgTree: {
      rootNode: updatedRootNode,
    },
  };

  return {
    workspace: updatedWorkspace,
    parent: currentParent,
    node: updatedNode,
  };
}

export function moveNode<
  N extends Position | Department,
  P extends RootNode | Position | Department
>(
  workspace: ReadonlyDeep<Workspace>,
  node: ReadonlyDeep<N>,
  nextParent: ReadonlyDeep<P>
): {
  workspace: ReadonlyDeep<Workspace>;
  prevParent: ReadonlyDeep<RootNode | Position | Department>;
  nextParent: ReadonlyDeep<P>;
  node: ReadonlyDeep<N>;
} {
  let rootNode = workspace.orgTree.rootNode;
  let prevParent = getNode(
    rootNode,
    (n): n is RootNode | Position | Department => n.id === node.parentId
  );

  assert(prevParent, "Parent node not found");

  const currentParentSubordinates = prevParent.subordinates;

  prevParent = {
    ...prevParent,
    subordinates: currentParentSubordinates.filter((sub) => sub.id !== node.id),
  };

  // NOTE: we do currentParent modification before newParent encounter in order to keep changes synchronized.
  rootNode = replaceNode(rootNode, prevParent);

  let newParent = getNode(rootNode, (n): n is P => n.id === nextParent.id);

  assert(newParent, "New parent node not found");

  const updatedNode: ReadonlyDeep<N> = {
    ...node,
    parentId: newParent.id,
    timestamp: Date.now(),
  };

  newParent = {
    ...newParent,
    subordinates: [
      ...(newParent.subordinates as P["subordinates"]),
      updatedNode,
    ],
  };

  rootNode = replaceNode(rootNode, newParent);

  let updatedWorkspace = workspace;

  updatedWorkspace = {
    ...updatedWorkspace,
    orgTree: {
      ...updatedWorkspace.orgTree,
      rootNode,
    },
  };

  return {
    workspace: updatedWorkspace,
    prevParent,
    nextParent: newParent,
    node: updatedNode,
  };
}

export function createPosition(
  workspace: ReadonlyDeep<Workspace>,
  memberId: string
): [ReadonlyDeep<Workspace>, ReadonlyDeep<Position>] {
  let rootNode = workspace.orgTree.rootNode;

  const newPosition: Position = {
    id: crypto.randomBytes(10).toString("hex"),
    timestamp: Date.now(),
    type: "position",
    title: "",
    memberId,
    withoutManagerManual: false,
    parentId: workspace.orgTree.rootNode.id,
    teamIds: [],
    managedDepartmentId: null,
    managerTeamIds: [],
    subordinates: [],
    reference: {
      isIncludeToReferrals: false,
      bonusDescription: "",
      jobDescriptionLink: "",
      candidates: [],
    },
  };

  rootNode = {
    ...workspace.orgTree.rootNode,
    subordinates: [...workspace.orgTree.rootNode.subordinates, newPosition],
  };

  workspace = {
    ...workspace,
    orgTree: { rootNode },
  };

  return [workspace, newPosition];
}

export function getMemberPosition(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Position | null> {
  return getNode(
    workspace.orgTree.rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member.id
  );
}

export function getMemberDepartment(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Department> | null {
  const position = getMemberPosition(workspace, member);

  return position && getParentDepartment(workspace, position.id);
}

export function isSameDepartment(
  workspace: ReadonlyDeep<Workspace>,
  memberA: ReadonlyDeep<Member>,
  memberB: ReadonlyDeep<Member>
): boolean {
  const deparmentA = getMemberDepartment(workspace, memberA);
  const deparmentB = getMemberDepartment(workspace, memberB);

  return (
    deparmentA != null && deparmentB != null && deparmentA.id === deparmentB.id
  );
}

export function getMemberTeams(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Team>[] {
  const position = getMemberPosition(workspace, member);

  return workspace.teams.filter((t) => position?.teamIds.includes(t.id));
}

export function moveMemberToManager(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  position: ReadonlyDeep<Position>,
  manager: {
    memberId?: string;
    positionId?: string;
  }
): {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  prevParent: ReadonlyDeep<RootNode | Position | Department>;
  nextParent: ReadonlyDeep<Position>;
  node: ReadonlyDeep<Position>;
} {
  position = {
    ...position,
    withoutManagerManual: false,
  };
  workspace = {
    ...workspace,
    orgTree: {
      rootNode: replaceNode(workspace.orgTree.rootNode, position),
    },
  };

  member = {
    ...member,
    botState: {
      ...member.botState,
      wasAskedAboutManager: true,
    },
  };
  workspace = replaceMember(workspace, member);

  let managerPosition = getNode(
    workspace.orgTree.rootNode,
    (n): n is Position =>
      n.type === "position" &&
      (n.id === manager.positionId || n.memberId === manager.memberId)
  );

  if (!managerPosition) {
    assert(
      manager.memberId,
      "Expect to receive manager memberId if position not found"
    );

    [workspace, managerPosition] = createPosition(workspace, manager.memberId);
  }

  const moveResult = moveNode(workspace, position, managerPosition);

  return {
    workspace: moveResult.workspace,
    member,
    prevParent: moveResult.prevParent,
    nextParent: moveResult.nextParent,
    node: moveResult.node,
  };
}

export function moveMemberToDepartment(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  position: ReadonlyDeep<Position>,
  departmentId: string
): {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  prevParent: ReadonlyDeep<RootNode | Position | Department>;
  nextParent: ReadonlyDeep<Department>;
  node: ReadonlyDeep<Position>;
} {
  member = {
    ...member,
    botState: {
      ...member.botState,
      wasAskedAboutDepartment: true,
    },
  };

  const department = getNode(
    workspace.orgTree.rootNode,
    (n): n is Department => n.type === "department" && n.id === departmentId
  );

  assert(department, "Unknown department");

  const moveResult = moveNode(workspace, position, department);

  return {
    workspace: moveResult.workspace,
    member,
    prevParent: moveResult.prevParent,
    nextParent: moveResult.nextParent,
    node: moveResult.node,
  };
}

export function markMemberAsNotHavingManager(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  position: ReadonlyDeep<Position>
): {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  prevParent: ReadonlyDeep<RootNode | Position | Department>;
  nextParent: ReadonlyDeep<RootNode>;
  node: ReadonlyDeep<Position>;
} {
  position = {
    ...position,
    withoutManagerManual: true,
  };
  workspace = {
    ...workspace,
    orgTree: {
      rootNode: replaceNode(workspace.orgTree.rootNode, position),
    },
  };

  member = {
    ...member,
    botState: {
      ...member.botState,
      wasAskedAboutManager: true,
    },
  };
  workspace = replaceMember(workspace, member);

  const moveResult = moveNode(workspace, position, workspace.orgTree.rootNode);

  return {
    workspace: moveResult.workspace,
    member,
    prevParent: moveResult.prevParent,
    nextParent: moveResult.nextParent,
    node: moveResult.node,
  };
}

export function resetMemberAsNotHavingManager(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  position: ReadonlyDeep<Position>
): {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  prevParent: ReadonlyDeep<RootNode | Position | Department>;
  nextParent: ReadonlyDeep<RootNode>;
  node: ReadonlyDeep<Position>;
} {
  position = {
    ...position,
    withoutManagerManual: false,
  };
  workspace = {
    ...workspace,
    orgTree: {
      rootNode: replaceNode(workspace.orgTree.rootNode, position),
    },
  };

  member = {
    ...member,
    botState: {
      ...member.botState,
      wasAskedAboutManager: false,
    },
  };
  workspace = replaceMember(workspace, member);

  const moveResult = moveNode(workspace, position, workspace.orgTree.rootNode);

  return {
    workspace: moveResult.workspace,
    member,
    prevParent: moveResult.prevParent,
    nextParent: moveResult.nextParent,
    node: moveResult.node,
  };
}

export function unlinkManagerPrevDepartment(
  workspace: ReadonlyDeep<Workspace>,
  manager: ReadonlyDeep<Position>
): {
  workspace: ReadonlyDeep<Workspace>;
  manager: ReadonlyDeep<Position>;
  prevDepartment: ReadonlyDeep<Department> | null;
} {
  let rootNode = workspace.orgTree.rootNode;

  const managerPrevDepartment = getNode(
    rootNode,
    (n): n is Department =>
      n.type === "department" && n.id === manager.managedDepartmentId
  );

  let updatedManagerPrevDepartment = managerPrevDepartment;

  if (managerPrevDepartment) {
    updatedManagerPrevDepartment = {
      ...managerPrevDepartment,
      managerId: null,
    };

    rootNode = replaceNode(rootNode, updatedManagerPrevDepartment);
  }

  manager = {
    ...manager,
    managedDepartmentId: null,
  };
  rootNode = replaceNode(rootNode, manager);

  workspace = {
    ...workspace,
    orgTree: {
      rootNode,
    },
  };

  return {
    workspace,
    manager,
    prevDepartment: updatedManagerPrevDepartment,
  };
}

export function unlinkTeamManager(
  workspace: ReadonlyDeep<Workspace>,
  team: ReadonlyDeep<Team>
): {
  workspace: ReadonlyDeep<Workspace>;
  team: ReadonlyDeep<Team>;
  prevManager: ReadonlyDeep<Position> | null;
} {
  if (!team.managerId) {
    return {
      workspace,
      team,
      prevManager: null,
    };
  }
  let rootNode = workspace.orgTree.rootNode;

  let teamPrevManager = getNode(
    rootNode,
    (n): n is Position => n.type === "position" && n.id === team.managerId
  );

  assert(teamPrevManager, "Expect team manager to exist");

  team = {
    ...team,
    managerId: null,
  };

  workspace = {
    ...workspace,
    teams: workspace.teams.map((t) => {
      if (t.id === team.id) {
        return team;
      }

      return t;
    }),
  };

  teamPrevManager = {
    ...teamPrevManager,
    managerTeamIds: teamPrevManager.managerTeamIds.filter((t) => t !== team.id),
  };

  rootNode = replaceNode(rootNode, teamPrevManager);

  workspace = {
    ...workspace,
    orgTree: {
      rootNode,
    },
  };

  return {
    workspace,
    team,
    prevManager: teamPrevManager,
  };
}

export function linkTeamManager(
  workspace: ReadonlyDeep<Workspace>,
  team: ReadonlyDeep<Team>,
  manager: ReadonlyDeep<Position>
): {
  workspace: ReadonlyDeep<Workspace>;
  team: ReadonlyDeep<Team>;
  manager: ReadonlyDeep<Position>;
} {
  let rootNode = workspace.orgTree.rootNode;

  if (!manager.teamIds.find((teamId) => teamId === team.id)) {
    manager = {
      ...manager,
      teamIds: [...manager.teamIds, team.id],
    };
  }

  manager = {
    ...manager,
    managerTeamIds: [...manager.managerTeamIds, team.id],
  };

  rootNode = replaceNode(rootNode, manager);

  team = {
    ...team,
    managerId: manager.id,
  };

  workspace = {
    ...workspace,
    teams: workspace.teams.map((t) => {
      if (t.id === team.id) {
        return team;
      }

      return t;
    }),
    orgTree: {
      rootNode,
    },
  };

  return {
    workspace,
    team,
    manager,
  };
}

export function unlinkDepartmentManager(
  workspace: ReadonlyDeep<Workspace>,
  department: ReadonlyDeep<Department>
): {
  workspace: ReadonlyDeep<Workspace>;
  department: ReadonlyDeep<Department>;
  prevManager: ReadonlyDeep<Position> | null;
} {
  if (!department.managerId) {
    return {
      workspace,
      department,
      prevManager: null,
    };
  }

  let rootNode = workspace.orgTree.rootNode;

  let departmentPrevManager = getNode(
    rootNode,
    (n): n is Position => n.type === "position" && n.id === department.managerId
  );

  /**
   * NOTE: When removing a position from the database, the department.managerId
   * reference is not automatically cleared. We need to manually clear
   * this reference to maintain data consistency.
   */

  if (departmentPrevManager) {
    departmentPrevManager = {
      ...departmentPrevManager,
      managedDepartmentId: null,
    };

    rootNode = replaceNode(rootNode, departmentPrevManager);
  }

  const updatedDepartment = getNode(
    rootNode,
    (n): n is Department => n.type === "department" && n.id === department.id
  );

  assert(updatedDepartment, "Expect department to exist");

  department = {
    ...updatedDepartment,
    managerId: null,
    subordinates: updatedDepartment.subordinates.map((s) => {
      if (s.id === departmentPrevManager?.id) {
        return departmentPrevManager;
      }

      return s;
    }),
  };
  rootNode = replaceNode(rootNode, department);

  workspace = {
    ...workspace,
    orgTree: {
      rootNode,
    },
  };

  return {
    workspace,
    department,
    prevManager: departmentPrevManager,
  };
}

export function linkDeparmentManager(
  workspace: ReadonlyDeep<Workspace>,
  department: ReadonlyDeep<Department>,
  manager: ReadonlyDeep<Position>
): {
  workspace: ReadonlyDeep<Workspace>;
  department: ReadonlyDeep<Department>;
  manager: ReadonlyDeep<Position>;
} {
  let rootNode = workspace.orgTree.rootNode;

  department = {
    ...department,
    managerId: manager.id,
    subordinates: department.subordinates.map((s) => {
      if (s.id === manager.id) {
        return {
          ...s,
          managedDepartmentId: department.id,
        };
      }

      return s;
    }),
  };

  rootNode = replaceNode(rootNode, department);

  manager = {
    ...manager,
    managedDepartmentId: department.id,
  };

  rootNode = replaceNode(rootNode, manager);

  workspace = {
    ...workspace,
    orgTree: {
      rootNode,
    },
  };

  return {
    workspace,
    department,
    manager,
  };
}
