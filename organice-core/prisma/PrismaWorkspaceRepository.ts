import assert from "assert";
import crypto from "crypto";

import {
  Announcement as PrismaAnnouncement,
  CalendarFeed as PrismaCalendarFeed,
  Channel as PrismaChannel,
  HolidaysSettings as PrismaHolidaysSettings,
  KudosResetFrequency as PrismaKudosResetFrequency,
  Link as PrismaLink,
  NotificationBlock as PrismaNotificationBlock,
  NotificationSettings as PrismaNotificationSettings,
  NotificationsFrequency as PrismaNotificationsFrequency,
  Prisma,
  PrismaClient,
  SlackStatusChange as PrismaSlackStatusChange,
  Survey as PrismaSurvey,
  SurveyAnswer as PrismaSurveyAnswer,
  SurveyQuestion as PrismaSurveyQuestion,
  SurveyTemplate as PrismaSurveyTemplate,
  SurveyTemplateQuestion as PrismaSurveyTemplateQuestion,
  TimeOffPolicy as PrismaTimeOffPolicy,
  TimeOffPolicyType as PrismaTimeOffPolicyType,
  TimeOffRequest as PrismaTimeOffRequest,
  TimeOffRequestApprover as PrismaTimeOffRequestApprover,
} from "@prisma/client";
import * as Sentry from "@sentry/core";
import { isEqual, mapValues, pickBy, sortBy } from "lodash";

import {
  AccrualsFrequency,
  Announcement,
  Billing,
  CalendarFeed,
  CelebrationSettings,
  Channel,
  Color,
  CustomField,
  Department,
  HolidaysSettings,
  KudosResetFrequency,
  Link,
  Member,
  MemberBotState,
  MemberStatusStash,
  Notification,
  NotificationBlock,
  NotificationBlockType,
  NotificationSettings,
  NotificationsFrequency,
  Policy,
  PolicyFieldType,
  Position,
  PredictedOrgChart,
  PresetPolicyId,
  PublishedNotificationState,
  Reference,
  RootNode,
  ScaleOption,
  SlackMemberToken,
  SlackPlan,
  SlackStatusChange,
  SlackStatusType,
  StripePlan,
  SupportedFeature,
  Survey,
  SurveyAnswer,
  SurveyAnswerValue,
  SurveyQuestion,
  SurveyQuestionType,
  SurveyTemplate,
  SurveyTemplateQuestion,
  Template,
  TextOption,
  TimeOffNotification,
  TimeOffNotificationType,
  TimeOffPolicy,
  TimeOffPolicyType,
  TimeOffRequest,
  TimeOffs,
  TimeOffType,
  Workspace,
  WorkspaceRepository,
  YearStart,
} from "../domain";
import SentrySpanMethods from "../sentry/SentrySpanMethods";

import { defaultNotificationsTemplates } from "./lib/defaultNotificationsTemplates";
import { defaultSurveyTemplates } from "./lib/defaultSurveyTemplates";

/**
 * Note: determines if update operations should be executed before
 * create operations for OrgTreeNodes
 *
 * When an OrgTreeNode is a department and we are updating its existing manager
 * while also creating a new position for a new manager, we must first reset the
 * existing department manager to avoid a unique constraint error
 * on position_managed_departmentId
 *
 * In all other cases, we prefer to create nodes first
 *
 */
function shouldUpdateBeforeCreate(nodes: {
  created: Prisma.OrgTreeNodeUncheckedCreateInput[];
  updated: {
    prev: Prisma.OrgTreeNodeUncheckedCreateInput;
    curr: Prisma.OrgTreeNodeUncheckedCreateInput;
  }[];
  deleted: Prisma.OrgTreeNodeUncheckedCreateInput[];
}): boolean {
  if (nodes.created.length === 0 || nodes.updated.length === 0) {
    return false;
  }

  return nodes.created.some((createdNode) => {
    if (!createdNode.position_managed_departmentId) {
      return false;
    }

    return nodes.updated.some(
      ({ prev, curr }) =>
        prev.position_managed_departmentId ===
          createdNode.position_managed_departmentId &&
        curr.position_managed_departmentId === null
    );
  });
}

const WORKSPACE_INCLUDES = {
  policy: true,
  policyFields: true,
  memberPolicyFields: true,
  pricing: true,
  members: true,
  teams: true,
  orgTreeNodes: true,
  reports: true,
  channels: true,
  timeOffs: {
    include: {
      policies: {
        include: {
          typePolicies: true,
        },
      },
    },
  },
  timeOffTypes: true,
  timeOffRequests: true,
  timeOffRequestApprovers: true,
  Announcements: true,
  links: true,
  kudosSettings: {
    include: {
      values: true,
    },
  },
  surveyTemplates: {
    include: {
      questions: true,
    },
  },
  surveys: {
    include: {
      answers: true,
      questions: true,
      template: true,
    },
  },
  holidays: true,
  holidaysSettings: true,
  slackStatusChanges: true,
  calendarFeeds: true,
  notifications: {
    include: {
      settings: true,
      blocks: true,
    },
  },
  publishedNotifications: true,
  slackBotToken: true,
  slackMemberTokens: true,
  timeOffTypeMemberStates: true,
} satisfies Prisma.WorkspaceInclude;

class PrismaWorkspaceRepository implements WorkspaceRepository {
  private workspaces: Record<Workspace["id"], Workspace | undefined> = {};

  constructor(private client: PrismaClient) {}

  async getWorkspaces(): Promise<Workspace[]> {
    const rawWorkspaces = await this.client.workspace.findMany({
      include: WORKSPACE_INCLUDES,
    });

    const workspaces = rawWorkspaces.map((workspace) =>
      parseWorkspace(workspace)
    );

    return workspaces;
  }

  async getWorkspace(workspaceId: string): Promise<Workspace | null> {
    const rawWorkspace = await this.client.workspace.findFirst({
      include: WORKSPACE_INCLUDES,
      where: {
        id: workspaceId,
      },
    });

    const workspace = rawWorkspace
      ? Sentry.startSpan(
          {
            name: "parseWorkspace",
            attributes: {
              workspaceId,
            },
          },
          () => parseWorkspace(rawWorkspace)
        )
      : null;

    if (workspace) {
      this.workspaces[workspace.id] = workspace;
    }

    return workspace;
  }

  async getWorkspaceByLink(linkId: string): Promise<Workspace | null> {
    const rawWorkspace = await this.client.workspace.findFirst({
      include: WORKSPACE_INCLUDES,
      where: {
        links: {
          some: {
            id: linkId,
          },
        },
      },
    });

    const workspace = rawWorkspace
      ? Sentry.startSpan(
          {
            name: "parseWorkspace",
            attributes: {
              linkId,
            },
          },
          () => parseWorkspace(rawWorkspace)
        )
      : null;

    if (workspace) {
      this.workspaces[workspace.id] = workspace;
    }

    return workspace;
  }

  async getWorkspaceByFeed(feedId: string): Promise<Workspace | null> {
    const rawWorkspace = await this.client.workspace.findFirst({
      include: WORKSPACE_INCLUDES,
      where: {
        calendarFeeds: {
          some: {
            id: feedId,
          },
        },
      },
    });

    const workspace = rawWorkspace
      ? Sentry.startSpan(
          {
            name: "parseWorkspace",
            attributes: {
              feedId,
            },
          },
          () => parseWorkspace(rawWorkspace)
        )
      : null;

    if (workspace) {
      this.workspaces[workspace.id] = workspace;
    }

    return workspace;
  }

  async setWorkspace(workspace: Workspace): Promise<Workspace> {
    const nextWorkspace = workspace;
    const prevWorkspace =
      this.workspaces[workspace.id] ?? (await this.getWorkspace(workspace.id));

    if (!prevWorkspace) {
      const rows = toPrismaRows(workspace);

      await this.client.$transaction([
        this.client.workspace.create({
          data: rows.workspace,
        }),
        this.client.workspacePolicy.create({
          data: rows.workspacePolicy,
        }),
        this.client.policyField.createMany({
          data: rows.workspacePolicyFields,
        }),
        this.client.memberPolicyField.createMany({
          data: rows.memberPolicyFields,
        }),
        this.client.pricing.create({
          data: rows.pricing,
        }),
        this.client.member.createMany({
          data: rows.members,
        }),
        this.client.slackBotToken.createMany({
          data: rows.slackBotTokens,
        }),
        this.client.slackMemberToken.createMany({
          data: rows.slackMemberTokens,
        }),
        this.client.team.createMany({
          data: rows.teams,
        }),
        this.client.orgTreeNode.createMany({
          data: rows.orgTreeNodes,
        }),
        this.client.reports.create({
          data: rows.reports,
        }),
        this.client.timeOffs.create({
          data: rows.timeOffs,
        }),
        this.client.timeOffType.createMany({
          data: rows.timeOffTypes,
        }),
        this.client.timeOffTypeMemberState.createMany({
          data: rows.timeOffTypeMemberStates,
        }),
        this.client.timeOffRequest.createMany({
          data: rows.timeOffRequests,
        }),
        this.client.channel.createMany({
          data: rows.channels,
        }),
        this.client.announcement.createMany({
          data: rows.announcements,
        }),
        this.client.kudosSetting.create({
          data: rows.kudosSetting,
        }),
        this.client.kudosValue.createMany({ data: rows.kudosValues }),
        this.client.link.createMany({
          data: rows.links,
        }),
        this.client.surveyTemplate.createMany({ data: rows.surveys }),
        this.client.survey.createMany({ data: rows.surveys }),
        this.client.surveyAnswer.createMany({ data: rows.surveyAnswers }),
        this.client.surveyQuestion.createMany({ data: rows.surveyQuestions }),
        this.client.surveyTemplateQuestion.createMany({
          data: rows.surveyTemplateQuestions,
        }),
        this.client.timeOffPolicy.createMany({
          data: rows.timeOffPolicies,
        }),
        this.client.timeOffPolicyType.createMany({
          data: rows.timeOffPolicyTypes,
        }),
        this.client.holidaysSettings.createMany({
          data: rows.holidaysSettings,
        }),
        this.client.holidays.createMany({
          data: rows.holidays,
        }),
        this.client.slackStatusChange.createMany({
          data: rows.slackStatusChanges,
        }),
        this.client.calendarFeed.createMany({
          data: rows.calendarFeeds,
        }),
        this.client.notification.createMany({
          data: rows.notifications,
        }),
        this.client.notificationSettings.createMany({
          data: rows.notificationSettings,
        }),
        this.client.notificationBlock.createMany({
          data: rows.notificationBlocks,
        }),
        this.client.publishedNotification.createMany({
          data: rows.publishedNotifications,
        }),
      ]);
    } else {
      if (prevWorkspace === workspace) {
        return workspace;
      }

      const prevRows = toPrismaRows(prevWorkspace);
      const rows = toPrismaRows(workspace);

      const nodes = concile(
        (t) => t.id,
        prevRows.orgTreeNodes,
        rows.orgTreeNodes
      );

      sortNodesUpdates(nodes);

      const announcements = concile(
        (t) => t.id,
        prevRows.announcements,
        rows.announcements
      );
      const channels = concile((t) => t.id, prevRows.channels, rows.channels);
      const members = concile((t) => t.id, prevRows.members, rows.members);
      const teams = concile((t) => t.id, prevRows.teams, rows.teams);
      const policyFields = concile(
        (t) => t.id,
        prevRows.workspacePolicyFields,
        rows.workspacePolicyFields
      );
      const memberPolicyFields = concile(
        (t) => `${t.policyFieldId}_${t.memberId}`,
        prevRows.memberPolicyFields,
        rows.memberPolicyFields
      );
      const timeOffTypes = concile(
        (type) => type.id,
        prevRows.timeOffTypes,
        rows.timeOffTypes
      );
      const timeOffRequests = concile(
        (request) => request.id,
        prevRows.timeOffRequests,
        rows.timeOffRequests
      );
      const timeOffRequestsApprovers = concile(
        (approver) => `${approver.requestId}_${approver.approverId}`,
        prevRows.timeOffRequestsApprovers,
        rows.timeOffRequestsApprovers
      );

      const timeOffTypeMemberStates = concile(
        (state) => `${state.memberId}_${state.typeId}`,
        prevRows.timeOffTypeMemberStates,
        rows.timeOffTypeMemberStates
      );

      const kudosValues = concile(
        (k) => k.id,
        prevRows.kudosValues,
        rows.kudosValues
      );

      const areKudosSettingsEqual = isEqual(
        prevRows.kudosSetting,
        rows.kudosSetting
      );
      const links = concile((t) => t.id, prevRows.links, rows.links);

      const surveys = concile((t) => t.id, prevRows.surveys, rows.surveys);
      const surveyQuestions = concile(
        (t) => t.id,
        prevRows.surveyQuestions,
        rows.surveyQuestions
      );
      const surveyAnswers = concile(
        (t) => t.id,
        prevRows.surveyAnswers,
        rows.surveyAnswers
      );

      const surveyTemplates = concile(
        (t) => t.id,
        prevRows.surveyTemplates,
        rows.surveyTemplates
      );
      const surveyTemplateQuestions = concile(
        (t) => t.id,
        prevRows.surveyTemplateQuestions,
        rows.surveyTemplateQuestions
      );
      const timeOffPolicies = concile(
        (t) => t.id,
        prevRows.timeOffPolicies,
        rows.timeOffPolicies
      );
      const timeOffPolicyTypes = concile(
        (t) => `${t.policyId}_${t.typeId}`,
        prevRows.timeOffPolicyTypes,
        rows.timeOffPolicyTypes
      );

      const publishedNotifications = concile(
        (l) => l.id,
        prevRows.publishedNotifications,
        rows.publishedNotifications
      );

      const slackStatusChanges = concile(
        (c) => `${c.workspaceId}_${c.memberId}_${c.type}_${c.refId}`,
        prevRows.slackStatusChanges,
        rows.slackStatusChanges
      );

      const calendarFeeds = concile(
        (c) => c.id,
        prevRows.calendarFeeds,
        rows.calendarFeeds
      );

      const holidays = concile((t) => t.id, prevRows.holidays, rows.holidays);

      const areHolidaysSettingsEqual = isEqual(
        prevRows.holidaysSettings,
        rows.holidaysSettings
      );

      const isWorkspaceEqual = isEqual(rows.workspace, prevRows.workspace);
      const isWorkspacePolicyEqual = isEqual(
        rows.workspacePolicy,
        prevRows.workspacePolicy
      );
      const isPricingEqual = isEqual(rows.pricing, prevRows.pricing);
      const areReportsEqual = isEqual(rows.reports, prevRows.reports);

      const notifications = concile(
        (t) => t.id,
        prevRows.notifications,
        rows.notifications
      );

      const notificationSettings = concile(
        (t) => t.notificationId,
        prevRows.notificationSettings,
        rows.notificationSettings
      );

      const notificationBlocks = concile(
        (t) => t.id,
        prevRows.notificationBlocks,
        rows.notificationBlocks
      );

      const slackBotTokens = concile(
        (t) => t.workspaceId,
        prevRows.slackBotTokens,
        rows.slackBotTokens
      );

      const slackMemberTokens = concile(
        (t) => t.memberId,
        prevRows.slackMemberTokens,
        rows.slackMemberTokens
      );

      const haveNodesChanged =
        nodes.created.length > 0 ||
        nodes.updated.length > 0 ||
        nodes.deleted.length > 0;

      const haveMembersChanged =
        members.created.length > 0 ||
        members.updated.length > 0 ||
        members.deleted.length > 0;

      const haveTeamsChanged =
        teams.created.length > 0 ||
        teams.updated.length > 0 ||
        teams.deleted.length > 0;

      const haveChannelsChanged =
        channels.created.length > 0 ||
        channels.updated.length > 0 ||
        channels.deleted.length > 0;

      const havePolicyFieldsChanged =
        policyFields.created.length > 0 ||
        policyFields.updated.length > 0 ||
        policyFields.deleted.length > 0;

      const haveMemberPolicyFieldsChanged =
        memberPolicyFields.created.length > 0 ||
        memberPolicyFields.updated.length > 0 ||
        memberPolicyFields.deleted.length > 0;

      const haveAnnouncementsChanged =
        announcements.created.length > 0 ||
        announcements.updated.length > 0 ||
        announcements.deleted.length > 0;

      const haveTimeOffsChanged =
        timeOffTypes.created.length > 0 ||
        timeOffTypes.updated.length > 0 ||
        timeOffTypes.deleted.length > 0 ||
        timeOffRequests.created.length > 0 ||
        timeOffRequests.updated.length > 0 ||
        timeOffRequests.deleted.length > 0 ||
        timeOffRequestsApprovers.created.length > 0 ||
        timeOffRequestsApprovers.updated.length > 0 ||
        timeOffRequestsApprovers.deleted.length > 0;

      const haveTimeOffTypeMemberStatesChanged =
        timeOffTypeMemberStates.created.length > 0 ||
        timeOffTypeMemberStates.updated.length > 0 ||
        timeOffTypeMemberStates.deleted.length > 0;

      const haveKudosValuesChanged =
        kudosValues.created.length > 0 ||
        kudosValues.updated.length > 0 ||
        kudosValues.deleted.length > 0;

      const haveLinksChanged =
        links.created.length > 0 ||
        links.updated.length > 0 ||
        links.deleted.length > 0;

      const haveSurveysChanged =
        surveys.created.length > 0 ||
        surveys.updated.length > 0 ||
        surveys.deleted.length > 0;

      const haveSurveyQuestionsChanged =
        surveyQuestions.created.length > 0 ||
        surveyQuestions.updated.length > 0 ||
        surveyQuestions.deleted.length > 0;

      const haveSurveyAnswersChanged =
        surveyAnswers.created.length > 0 ||
        surveyAnswers.updated.length > 0 ||
        surveyAnswers.deleted.length > 0;

      const haveSurveyTemplatesChanged =
        surveyTemplates.created.length > 0 ||
        surveyTemplates.updated.length > 0 ||
        surveyTemplates.deleted.length > 0;

      const haveSurveyTemplateQuestionsChanged =
        surveyTemplateQuestions.created.length > 0 ||
        surveyTemplateQuestions.updated.length > 0 ||
        surveyTemplateQuestions.deleted.length > 0;

      const haveTimeOffPoliciesChanged =
        timeOffPolicies.created.length > 0 ||
        timeOffPolicies.updated.length > 0 ||
        timeOffPolicies.deleted.length > 0;

      const haveTimeOffPolicyTypesChanged =
        timeOffPolicyTypes.created.length > 0 ||
        timeOffPolicyTypes.updated.length > 0 ||
        timeOffPolicyTypes.deleted.length > 0;

      const haveHolidaysChanged =
        holidays.created.length > 0 ||
        holidays.updated.length > 0 ||
        holidays.deleted.length > 0;

      const haveSlackStatusChangesChanged =
        slackStatusChanges.created.length > 0 ||
        slackStatusChanges.updated.length > 0 ||
        slackStatusChanges.deleted.length > 0;

      const haveNotificationLogsChanged =
        publishedNotifications.created.length > 0 ||
        publishedNotifications.updated.length > 0;

      const haveCalendarFeedsChanged =
        calendarFeeds.created.length > 0 ||
        calendarFeeds.updated.length > 0 ||
        calendarFeeds.deleted.length > 0;

      const haveNotificationsChanged =
        notifications.created.length > 0 ||
        notifications.updated.length > 0 ||
        notifications.deleted.length > 0;

      const haveNotificationSettingsChanged =
        notificationSettings.created.length > 0 ||
        notificationSettings.updated.length > 0 ||
        notificationSettings.deleted.length > 0;

      const haveNotificationBlocksChanged =
        notificationBlocks.created.length > 0 ||
        notificationBlocks.updated.length > 0 ||
        notificationBlocks.deleted.length > 0;

      const haveSlackBotTokensChanged =
        slackBotTokens.created.length > 0 ||
        slackBotTokens.updated.length > 0 ||
        slackBotTokens.deleted.length > 0;

      const haveSlackMemberTokensChanged =
        slackMemberTokens.created.length > 0 ||
        slackMemberTokens.updated.length > 0 ||
        slackMemberTokens.deleted.length > 0;

      if (
        !isWorkspaceEqual ||
        !isWorkspacePolicyEqual ||
        !isPricingEqual ||
        !areReportsEqual ||
        !areKudosSettingsEqual ||
        !areHolidaysSettingsEqual ||
        haveNodesChanged ||
        haveMembersChanged ||
        haveTeamsChanged ||
        haveChannelsChanged ||
        havePolicyFieldsChanged ||
        haveMemberPolicyFieldsChanged ||
        haveAnnouncementsChanged ||
        haveTimeOffsChanged ||
        haveTimeOffTypeMemberStatesChanged ||
        haveKudosValuesChanged ||
        haveLinksChanged ||
        haveSurveysChanged ||
        haveSurveyQuestionsChanged ||
        haveSurveyAnswersChanged ||
        haveSurveyTemplatesChanged ||
        haveSurveyTemplateQuestionsChanged ||
        haveTimeOffPoliciesChanged ||
        haveTimeOffPolicyTypesChanged ||
        haveHolidaysChanged ||
        haveSlackStatusChangesChanged ||
        haveNotificationLogsChanged ||
        haveCalendarFeedsChanged ||
        haveNotificationsChanged ||
        haveNotificationSettingsChanged ||
        haveNotificationBlocksChanged ||
        haveSlackBotTokensChanged ||
        haveSlackMemberTokensChanged
      ) {
        const log: Record<string, unknown> = {
          isWorkspaceEqual: !isWorkspaceEqual,
          isWorkspacePolicyEqual: !isWorkspacePolicyEqual,
          isPricingEqual: !isPricingEqual,
          areReportsEqual: !areReportsEqual,
          areKudosSettingsEqual: !areKudosSettingsEqual,
          areHolidaysSettingsEqual: !areHolidaysSettingsEqual,
          haveNodesChanged,
          haveMembersChanged,
          haveTeamsChanged,
          haveChannelsChanged,
          havePolicyFieldsChanged,
          haveMemberPolicyFieldsChanged,
          haveAnnouncementsChanged,
          haveTimeOffsChanged,
          haveTimeOffTypeMemberStatesChanged,
          haveKudosValuesChanged,
          haveLinksChanged,
          haveSurveysChanged,
          haveSurveyQuestionsChanged,
          haveSurveyAnswersChanged,
          haveSurveyTemplatesChanged,
          haveSurveyTemplateQuestionsChanged,
          haveTimeOffPoliciesChanged,
          haveTimeOffPolicyTypesChanged,
          haveHolidaysChanged,
          haveSlackStatusChangesChanged,
          haveNotificationLogsChanged,
          haveCalendarFeedsChanged,
          haveNotificationsChanged,
          haveNotificationSettingsChanged,
          haveNotificationBlocksChanged,
          haveSlackBotTokensChanged,
          haveSlackMemberTokensChanged,
        };

        for (const [key, value] of Object.entries(log)) {
          if (value === false) {
            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
            delete log[key];
          }
        }

        // eslint-disable-next-line no-console
        console.log("Writing changes to the database...", workspace.id, log);
        rows.workspace.updatedAt = new Date();
        await this.client.$transaction([
          // Workspace
          this.client.workspace.update({
            data: diff(rows.workspace, prevRows.workspace),
            where: {
              id: workspace.id,
            },
            select: null,
          }),

          // Workspace Policy
          this.client.workspacePolicy.update({
            data: diff(rows.workspacePolicy, prevRows.workspacePolicy),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          // Pricing
          this.client.pricing.update({
            data: diff(rows.pricing, prevRows.pricing),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          // Slack tokens
          this.client.slackBotToken.createMany({
            data: slackBotTokens.created,
          }),

          ...slackBotTokens.updated.map(({ curr: token, prev: prevToken }) =>
            this.client.slackBotToken.update({
              data: diff(token, prevToken),
              where: {
                workspaceId: token.workspaceId,
              },
              select: null,
            })
          ),

          this.client.slackBotToken.deleteMany({
            where: {
              OR: slackBotTokens.deleted.map((token) => ({
                AND: {
                  workspaceId: token.workspaceId,
                },
              })),
            },
          }),

          this.client.slackMemberToken.createMany({
            data: slackMemberTokens.created,
          }),

          ...slackMemberTokens.updated.map(({ curr: token, prev: prevToken }) =>
            this.client.slackMemberToken.update({
              data: diff(token, prevToken),
              where: {
                workspaceId_memberId: {
                  memberId: token.memberId,
                  workspaceId: token.workspaceId,
                },
              },
              select: null,
            })
          ),

          this.client.slackMemberToken.deleteMany({
            where: {
              OR: slackMemberTokens.deleted.map((token) => ({
                AND: {
                  memberId: token.memberId,
                  workspaceId: token.workspaceId,
                },
              })),
            },
          }),

          // Reports
          this.client.reports.update({
            data: diff(rows.reports, prevRows.reports),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          // TimeOffs
          this.client.timeOffs.update({
            data: diff(rows.timeOffs, prevRows.timeOffs),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          this.client.timeOffType.createMany({
            data: timeOffTypes.created,
          }),

          ...timeOffTypes.updated.map(({ curr: type, prev: prevType }) =>
            this.client.timeOffType.update({
              data: diff(type, prevType),
              where: {
                workspaceId_id: {
                  workspaceId: workspace.id,
                  id: type.id,
                },
              },
              select: null,
            })
          ),

          this.client.timeOffType.deleteMany({
            where: {
              OR: timeOffTypes.deleted.map((type) => ({
                AND: {
                  id: type.id,
                  workspaceId: workspace.id,
                },
              })),
            },
          }),

          this.client.timeOffRequest.createMany({
            data: timeOffRequests.created,
          }),

          this.client.timeOffRequestApprover.createMany({
            data: timeOffRequestsApprovers.created,
          }),

          ...timeOffRequests.updated.map(
            ({ curr: request, prev: prevRequest }) =>
              this.client.timeOffRequest.update({
                data: diff(request, prevRequest),
                where: {
                  id: request.id,
                },
                select: null,
              })
          ),

          ...timeOffRequestsApprovers.updated.map(
            ({ curr: approver, prev: prevApprover }) =>
              this.client.timeOffRequestApprover.update({
                data: diff(approver, prevApprover),
                where: {
                  requestId_approverId: {
                    requestId: approver.requestId,
                    approverId: approver.approverId,
                  },
                },
                select: null,
              })
          ),

          this.client.timeOffRequestApprover.deleteMany({
            where: {
              OR: timeOffRequestsApprovers.deleted.map((approver) => ({
                requestId: approver.requestId,
                approverId: approver.approverId,
              })),
            },
          }),

          ...timeOffRequests.deleted.map((request) => {
            return this.client.timeOffRequest.update({
              where: {
                id: request.id,
              },
              data: {
                deletedAt: new Date(),
              },
              select: null,
            });
          }),

          // Org Tree Nodes
          this.client.orgTreeNode.deleteMany({
            where: {
              OR: nodes.deleted.map((node) => ({
                AND: {
                  id: node.id,
                  workspaceId: workspace.id,
                },
              })),
            },
          }),

          ...(shouldUpdateBeforeCreate(nodes)
            ? [
                ...nodes.updated.map(({ curr: node, prev: prevNode }) =>
                  this.client.orgTreeNode.update({
                    data: diff(node, prevNode),
                    where: {
                      workspaceId_id: {
                        workspaceId: workspace.id,
                        id: node.id,
                      },
                    },
                    select: null,
                  })
                ),
                this.client.orgTreeNode.createMany({
                  data: nodes.created,
                }),
              ]
            : [
                this.client.orgTreeNode.createMany({
                  data: nodes.created,
                }),
                ...nodes.updated.map(({ curr: node, prev: prevNode }) =>
                  this.client.orgTreeNode.update({
                    data: diff(node, prevNode),
                    where: {
                      workspaceId_id: {
                        workspaceId: workspace.id,
                        id: node.id,
                      },
                    },
                    select: null,
                  })
                ),
              ]),

          // Channels
          this.client.channel.createMany({
            data: channels.created,
            skipDuplicates: true,
          }),

          ...channels.updated.map(({ curr: channel, prev: prevChannel }) =>
            this.client.channel.update({
              data: diff(channel, prevChannel),
              where: {
                id_workspaceId: {
                  id: channel.id,
                  workspaceId: workspace.id,
                },
              },
              select: null,
            })
          ),

          this.client.channel.deleteMany({
            where: {
              OR: channels.deleted.map((channel) => ({
                AND: {
                  id: channel.id,
                  workspaceId: workspace.id,
                },
              })),
            },
          }),

          // Teams
          this.client.team.createMany({
            data: teams.created,
          }),

          ...teams.updated.map(({ curr: team, prev: prevTeam }) =>
            this.client.team.update({
              data: diff(team, prevTeam),
              where: {
                id: team.id,
              },
              select: null,
            })
          ),

          ...teams.deleted.map((team) =>
            this.client.team.delete({
              where: {
                id: team.id,
              },
            })
          ),

          // Policy Fields
          this.client.policyField.createMany({
            data: policyFields.created,
          }),

          ...policyFields.updated.map(({ curr: field, prev: prevField }) =>
            this.client.policyField.update({
              data: diff(field, prevField),
              where: {
                id: field.id,
              },
              select: null,
            })
          ),

          ...policyFields.deleted.map((field) =>
            this.client.policyField.delete({
              where: {
                id: field.id,
              },
            })
          ),

          // Member Policy Fields
          this.client.memberPolicyField.createMany({
            data: memberPolicyFields.created,
          }),

          ...memberPolicyFields.updated.map(
            ({ curr: field, prev: prevField }) =>
              this.client.memberPolicyField.update({
                data: diff(field, prevField),
                where: {
                  policyFieldId_memberId: {
                    policyFieldId: field.policyFieldId,
                    memberId: field.memberId,
                  },
                },
                select: null,
              })
          ),

          ...memberPolicyFields.deleted.map((field) =>
            this.client.memberPolicyField.delete({
              where: {
                policyFieldId_memberId: {
                  policyFieldId: field.policyFieldId,
                  memberId: field.memberId,
                },
              },
            })
          ),

          // Announcements
          this.client.announcement.createMany({
            data: announcements.created,
          }),

          ...announcements.updated.map(
            ({ curr: announcement, prev: prevAnnouncement }) =>
              this.client.announcement.update({
                data: diff(announcement, prevAnnouncement),
                where: {
                  id: announcement.id,
                },
                select: null,
              })
          ),

          ...announcements.deleted.map((announcement) =>
            this.client.announcement.delete({
              where: {
                id: announcement.id,
              },
            })
          ),

          // KudosSettings
          this.client.kudosSetting.update({
            data: diff(rows.kudosSetting, prevRows.kudosSetting),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          // KudosValues
          this.client.kudosValue.createMany({
            data: kudosValues.created,
          }),

          ...kudosValues.updated.map(({ curr: item, prev: prevItem }) =>
            this.client.kudosValue.update({
              data: diff(item, prevItem),
              where: {
                id: item.id,
              },
              select: null,
            })
          ),

          ...kudosValues.deleted.map((item) =>
            this.client.kudosValue.delete({
              where: {
                id: item.id,
              },
            })
          ),

          // Links
          this.client.link.createMany({
            data: links.created,
          }),

          ...links.updated.map(({ curr: link, prev: prevLink }) =>
            this.client.link.update({
              data: diff(link, prevLink),
              where: {
                id: link.id,
              },
              select: null,
            })
          ),

          ...links.deleted.map((link) =>
            this.client.link.delete({
              where: {
                id: link.id,
              },
            })
          ),

          // Surveys && SurveyQuestions && SurveyAnswers
          this.client.survey.createMany({
            data: surveys.created,
          }),
          this.client.surveyQuestion.createMany({
            data: surveyQuestions.created,
          }),
          this.client.surveyAnswer.createMany({
            data: surveyAnswers.created,
          }),

          ...surveyQuestions.updated.map(({ curr: item, prev: prevItem }) =>
            this.client.surveyQuestion.update({
              data: diff(item, prevItem),
              where: {
                id: item.id,
              },
              select: null,
            })
          ),
          ...surveyAnswers.updated.map(({ curr: item, prev: prevItem }) =>
            this.client.surveyAnswer.update({
              data: diff(item, prevItem),
              where: {
                id: item.id,
              },
              select: null,
            })
          ),
          ...surveys.updated.map(({ curr: item, prev: prevItem }) =>
            this.client.survey.update({
              data: diff(item, prevItem),
              where: {
                id: item.id,
              },
              select: null,
            })
          ),

          ...surveyAnswers.deleted.map((item) =>
            this.client.surveyAnswer.delete({
              where: {
                id: item.id,
              },
            })
          ),
          ...surveyQuestions.deleted.map((item) =>
            this.client.surveyQuestion.delete({
              where: {
                id: item.id,
              },
            })
          ),
          ...surveys.deleted.map((item) =>
            this.client.survey.delete({
              where: {
                id: item.id,
              },
            })
          ),

          // SurveyTemplates && SurveyTemplateQuestions
          this.client.surveyTemplate.createMany({
            data: surveyTemplates.created,
          }),
          this.client.surveyTemplateQuestion.createMany({
            data: surveyTemplateQuestions.created,
          }),

          ...surveyTemplateQuestions.updated.map(
            ({ curr: item, prev: prevItem }) =>
              this.client.surveyTemplateQuestion.update({
                data: diff(item, prevItem),
                where: {
                  id: item.id,
                },
              })
          ),
          ...surveyTemplates.updated.map(({ curr: item, prev: prevItem }) =>
            this.client.surveyTemplate.update({
              data: diff(item, prevItem),
              where: {
                id: item.id,
              },
              select: null,
            })
          ),

          ...surveyTemplateQuestions.deleted.map((item) =>
            this.client.surveyTemplateQuestion.delete({
              where: {
                id: item.id,
              },
            })
          ),
          ...surveyTemplates.deleted.map((item) =>
            this.client.surveyTemplate.delete({
              where: {
                id: item.id,
              },
            })
          ),

          // TimeOffPolicies && TimeOffPolicyTypes
          this.client.timeOffPolicy.createMany({
            data: timeOffPolicies.created,
          }),

          ...timeOffPolicies.updated.map(({ curr: policy, prev: prevPolicy }) =>
            this.client.timeOffPolicy.update({
              data: diff(policy, prevPolicy),
              where: {
                id: policy.id,
              },
            })
          ),

          ...timeOffPolicyTypes.deleted.map((policyType) =>
            this.client.timeOffPolicyType.delete({
              where: {
                workspaceId_typeId_policyId: {
                  workspaceId: workspace.id,
                  typeId: policyType.typeId,
                  policyId: policyType.policyId,
                },
              },
            })
          ),

          ...timeOffPolicies.deleted.map((policy) =>
            this.client.timeOffPolicy.delete({
              where: {
                id: policy.id,
              },
            })
          ),

          this.client.timeOffPolicyType.createMany({
            data: timeOffPolicyTypes.created,
          }),

          ...timeOffPolicyTypes.updated.map(
            ({ curr: policyType, prev: prevPolicyType }) =>
              this.client.timeOffPolicyType.update({
                data: diff(policyType, prevPolicyType),
                where: {
                  workspaceId_typeId_policyId: {
                    workspaceId: workspace.id,
                    typeId: policyType.typeId,
                    policyId: policyType.policyId,
                  },
                },
                select: null,
              })
          ),

          this.client.publishedNotification.createMany({
            data: publishedNotifications.created,
          }),

          ...publishedNotifications.updated.map(
            ({ curr: notification, prev: prevNotification }) =>
              this.client.publishedNotification.update({
                data: diff(notification, prevNotification),
                where: {
                  id: notification.id,
                },
                select: null,
              })
          ),

          // HolidaysSettings
          this.client.holidaysSettings.update({
            data: diff(rows.holidaysSettings, prevRows.holidaysSettings),
            where: {
              workspaceId: workspace.id,
            },
            select: null,
          }),

          // Holidays
          this.client.holidays.createMany({
            data: holidays.created,
          }),

          ...holidays.updated.map(({ curr: holiday, prev: prevHoliday }) =>
            this.client.holidays.update({
              data: diff(holiday, prevHoliday),
              where: {
                id: holiday.id,
              },
              select: null,
            })
          ),

          ...holidays.deleted.map((holiday) =>
            this.client.holidays.delete({
              where: {
                id: holiday.id,
              },
            })
          ),

          // SlackStatusChanges
          this.client.slackStatusChange.createMany({
            data: slackStatusChanges.created,
          }),

          ...slackStatusChanges.updated.map(
            ({ curr: change, prev: prevChange }) =>
              this.client.slackStatusChange.update({
                data: diff(change, prevChange),
                where: {
                  workspaceId_memberId_type_refId: {
                    workspaceId: workspace.id,
                    memberId: change.memberId,
                    type: change.type,
                    refId: change.refId,
                  },
                },
                select: null,
              })
          ),

          ...slackStatusChanges.deleted.map((change) =>
            this.client.slackStatusChange.delete({
              where: {
                workspaceId_memberId_type_refId: {
                  workspaceId: workspace.id,
                  memberId: change.memberId,
                  type: change.type,
                  refId: change.refId,
                },
              },
            })
          ),
          // Calendar Feeds
          this.client.calendarFeed.createMany({
            data: calendarFeeds.created,
          }),

          ...calendarFeeds.updated.map(({ curr: feed, prev: prevFeed }) => {
            return this.client.calendarFeed.update({
              data: diff(feed, prevFeed),
              where: {
                id: feed.id,
              },
              select: null,
            });
          }),

          ...calendarFeeds.deleted.map((feed) =>
            this.client.calendarFeed.delete({
              where: {
                id: feed.id,
              },
            })
          ),

          // Notifications
          this.client.notification.createMany({
            data: notifications.created,
          }),

          ...notifications.updated.map(
            ({ curr: notification, prev: prevNotification }) =>
              this.client.notification.update({
                data: diff(notification, prevNotification),
                where: {
                  id: notification.id,
                },
                select: null,
              })
          ),

          /**
           * NOTE: Delete notification related records before
           * deleting a notification, to avoid record
           * to delete does not exist error
           */
          ...notificationSettings.deleted.map((setting) =>
            this.client.notificationSettings.delete({
              where: {
                notificationId: setting.notificationId,
              },
            })
          ),

          ...notificationBlocks.deleted.map((block) =>
            this.client.notificationBlock.delete({
              where: {
                id: block.id,
              },
            })
          ),

          ...notifications.deleted.map((notification) =>
            this.client.notification.delete({
              where: {
                id: notification.id,
              },
            })
          ),

          this.client.notificationSettings.createMany({
            data: notificationSettings.created,
          }),

          ...notificationSettings.updated.map(
            ({ curr: setting, prev: prevSetting }) =>
              this.client.notificationSettings.update({
                data: diff(setting, prevSetting),
                where: {
                  notificationId: setting.notificationId,
                },
                select: null,
              })
          ),

          this.client.notificationBlock.createMany({
            data: notificationBlocks.created,
          }),

          ...notificationBlocks.updated.map(
            ({ curr: block, prev: prevBlock }) =>
              this.client.notificationBlock.update({
                data: diff(block, prevBlock),
                where: {
                  id: block.id,
                },
                select: null,
              })
          ),

          // timeOffTypeMemberState.createMany is located after members create

          ...timeOffTypeMemberStates.updated.map(
            ({ curr: state, prev: prevState }) =>
              this.client.timeOffTypeMemberState.update({
                data: diff(state, prevState),
                where: {
                  workspaceId_memberId_typeId: {
                    workspaceId: workspace.id,
                    memberId: state.memberId,
                    typeId: state.typeId,
                  },
                },
                select: null,
              })
          ),

          this.client.timeOffTypeMemberState.deleteMany({
            where: {
              OR: timeOffTypeMemberStates.deleted.map((state) => ({
                workspaceId: workspace.id,
                memberId: state.memberId,
                typeId: state.typeId,
              })),
            },
          }),

          /**
           * NOTE: it is important to remove members in the end so
           * reassignDeletedMembersData operation works correctly
           */

          // Members
          this.client.member.createMany({
            data: members.created,
          }),

          ...members.updated.map(({ curr: member, prev: prevMember }) =>
            this.client.member.update({
              data: diff(member, prevMember),
              where: {
                id_workspaceId: {
                  id: member.id,
                  workspaceId: workspace.id,
                },
              },
              select: null,
            })
          ),

          ...members.deleted.map((member) =>
            this.client.member.delete({
              where: {
                id_workspaceId: {
                  id: member.id,
                  workspaceId: workspace.id,
                },
              },
            })
          ),

          this.client.timeOffTypeMemberState.createMany({
            data: timeOffTypeMemberStates.created,
          }),
        ]);
      }
    }

    this.workspaces[workspace.id] = nextWorkspace;

    return workspace;
  }
}

interface RawOnboarding {
  completed: boolean;
  finishedAt?: number;
  finishedBy?: Member["id"];
  bookmarkedFeatures?: string[];
}

type RawSurveyTemplate = PrismaSurveyTemplate & {
  questions: PrismaSurveyTemplateQuestion[];
};

type RawSurvey = PrismaSurvey & {
  template: PrismaSurveyTemplate | null;
  answers: PrismaSurveyAnswer[];
  questions: PrismaSurveyQuestion[];
};

type RawSurveyAnswer = PrismaSurveyAnswer;

type RawWorkspace = Prisma.WorkspaceGetPayload<{
  include: typeof WORKSPACE_INCLUDES;
}>;

function parseMembers(workspace: RawWorkspace): Member[] {
  const memberFieldsMap = new Map<string, RawWorkspace["memberPolicyFields"]>();
  const timeOffTypeMemberStatesMap = new Map<
    string,
    RawWorkspace["timeOffTypeMemberStates"]
  >();

  for (const member of workspace.members) {
    memberFieldsMap.set(member.id, []);
    timeOffTypeMemberStatesMap.set(member.id, []);
  }

  for (const field of workspace.memberPolicyFields) {
    memberFieldsMap.get(field.memberId)?.push(field);
  }

  for (const state of workspace.timeOffTypeMemberStates) {
    timeOffTypeMemberStatesMap.get(state.memberId)?.push(state);
  }

  return workspace.members.map((member) => {
    const memberFields = memberFieldsMap.get(member.id)!;
    const timeOffTypeMemberStates = timeOffTypeMemberStatesMap.get(member.id)!;

    return {
      id: member.id,
      isAdmin: member.isAdmin,
      isSlackWorkspaceAdmin: member.isSlackWorkspaceAdmin,
      isSlackWorkspaceOwner: member.isSlackWorkspaceOwner,
      isSlackWorkspacePrimaryOwner: member.isSlackWorkspacePrimaryOwner,
      name: member.name ?? undefined,
      photoUrl: member.photoUrl ?? undefined,
      photo512Url: member.photo512Url ?? undefined,
      photo72Url: member.photo72Url ?? undefined,
      realName: member.realName ?? undefined,
      displayName: member.displayName ?? undefined,
      email: member.email ?? undefined,
      notes: member.notes ?? undefined,
      organicePhone: member.organicePhone ?? undefined,
      birthday: member.birthday ?? undefined,
      hideBirthday: member.hideBirthday,
      updated: member.updated ?? undefined,
      joinedAt: member.joinedAt ?? undefined,
      country: member.country ?? undefined,
      firstClickedProPlanAt: member.firstClickedProPlanAt ?? undefined,
      botState: JSON.parse(member.botState) as MemberBotState,
      status: member.status
        ? (JSON.parse(member.status) as Member["status"])
        : undefined,
      organiceCustomFileds: memberFields.reduce(
        (customFields, field) => ({
          ...customFields,
          [field.policyFieldId]: field.value,
        }),
        {}
      ),
      howManyTimesHomeTabVisited: member.howManyTimesHomeTabVisited,
      timeOffs: timeOffTypeMemberStates.reduce<Member["timeOffs"]>(
        (acc, state) => {
          acc[state.typeId] = {
            balance: state.balance,
            nextResetAt: state.nextResetAt,
          };

          return acc;
        },
        {}
      ),
      timeOffTypePolicyId: member.timeOffTypePolicyId ?? undefined,
      isSlackBillable: member.isSlackBillable,
      timezone: member.timezone,
    };
  });
}

function parseChannel(channel: PrismaChannel): Channel {
  return {
    id: channel.id,
    name: channel.name,
    membersCount: channel.membersCount ?? undefined,
  };
}

function parseAnnouncement(announcement: PrismaAnnouncement): Announcement {
  return {
    id: announcement.id,
    message: announcement.message,
    sentAt: announcement.sentAt,
    type: announcement.type as Announcement["type"],
    memberId: announcement.memberId ?? undefined,
    channelId: announcement.channelId,
  };
}

function parseLink(link: PrismaLink): Link {
  const chartLayout = JSON.parse(link.chartLayout) as {
    clusterHorizontal: boolean;
    dataField: string | null;
  };

  return {
    ...link,
    chartLayout: {
      clusterHorizontal: chartLayout.clusterHorizontal,
      dataField: chartLayout.dataField ?? undefined,
    },
  };
}

interface RawCelebrationSettings
  extends Omit<CelebrationSettings, "birthday" | "anniversary"> {
  birthday: {
    changeSlackStatus: boolean;
    enabled: boolean;
    includeGif: boolean;
    templates?: Template[];
  };
  anniversary: {
    changeSlackStatus: boolean;
    enabled: boolean;
    includeGif: boolean;
    templates?: Template[];
  };
}

function parseNotificationBlocks(
  blocks: PrismaNotificationBlock[]
): Notification["blocks"] {
  return blocks.map((block) => {
    const { type } = block;
    const isValidType = Object.values(NotificationBlockType).includes(
      type as NotificationBlockType
    );

    assert(isValidType, `Unexpected notification block type: ${type}`);

    return {
      id: block.id,
      type: block.type,
      data: JSON.parse(block.data) as NotificationBlock["data"],
    } as NotificationBlock;
  });
}

function parseNotificationSettings(
  settings: PrismaNotificationSettings
): NotificationSettings {
  let frequency: NotificationsFrequency = NotificationsFrequency.Month;

  if (settings.frequency === PrismaNotificationsFrequency.Week) {
    frequency = NotificationsFrequency.Week;
  }

  if (settings.frequency === PrismaNotificationsFrequency.Day) {
    frequency = NotificationsFrequency.Day;
  }

  return {
    frequency,
    day: settings.day ?? undefined,
    time: settings.time,
    timezone: settings.timezone,
    channelId: settings.channelId,
  };
}

function parseNotifications(
  notifications: RawWorkspace["notifications"]
): Notification[] {
  return notifications.map((notification) => {
    assert(notification.settings, "Expected notification settings to exist");

    return {
      id: notification.id,
      title: notification.title,
      isActive: notification.isActive,
      createdById: notification.createdById,
      blocks: parseNotificationBlocks(notification.blocks),
      settings: parseNotificationSettings(notification.settings),
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
    };
  });
}

function parseCelebrationSettings(
  rawAnnouncements: RawWorkspace["announcements"]
): CelebrationSettings {
  if (!rawAnnouncements) {
    return {
      common: {
        timeOfPosting: "9 am",
        timezone: null,
        whenToPostIfHappensOnWeekend: "MONDAY",
      },
      birthday: {
        changeSlackStatus: false,
        enabled: false,
        includeGif: true,
        templates: [],
      },
      anniversary: {
        changeSlackStatus: false,
        enabled: false,
        includeGif: true,
        templates: [],
      },
      reminders: [],
      sentAnnouncements: [],
    };
  }

  const announcements = JSON.parse(rawAnnouncements) as RawCelebrationSettings;

  if (!announcements.birthday.templates) {
    announcements.birthday.templates = [];
  }

  if (!announcements.anniversary.templates) {
    announcements.anniversary.templates = [];
  }

  return announcements as CelebrationSettings;
}

function parseSlackStatusChange(
  slackStatusChange: PrismaSlackStatusChange
): SlackStatusChange {
  return {
    type: slackStatusChange.type as SlackStatusType,
    stashedStatus: slackStatusChange.stashedStatus
      ? (JSON.parse(slackStatusChange.stashedStatus) as MemberStatusStash)
      : null,
    memberId: slackStatusChange.memberId,
    refId: slackStatusChange.refId ? slackStatusChange.refId : null,
  };
}

function parseTimeOffs(workspace: RawWorkspace): TimeOffs {
  const { timeOffs, timeOffTypes, timeOffRequests } = workspace;

  assert(timeOffs, "Expected workspace timeOffs to exist");

  return {
    channelId: timeOffs.channelId ?? undefined,
    requests: parseTimeOffRequests(
      timeOffRequests.filter((request) => !request.deletedAt),
      workspace
    ),
    types: timeOffTypes
      .sort((a, b) => a.order - b.order)
      .map(
        (type): TimeOffType => ({
          id: type.id,
          label: type.label,
          color: type.color as Color,
          emoji: type.emoji,
          slackEmoji: type.slackEmoji,
          hint: type.hint ?? undefined,
        })
      ),
    isEnable: timeOffs.isEnable,
    policies: timeOffs.policies.map((policy) => parseTimeOffPolicy(policy)),
    createDiscussionChannelWhenMultipleApprovers:
      timeOffs.createDiscussionChannelWhenMultipleApprovers,
    changeSlackStatus: timeOffs.changeSlackStatus,
  };
}

function parseTimeOffRequests(
  requests: PrismaTimeOffRequest[],
  rawWorkspace: RawWorkspace
): TimeOffRequest[] {
  const approvers = rawWorkspace.timeOffRequestApprovers;
  const approversByRequestId = approvers.reduce<
    Record<string, PrismaTimeOffRequestApprover[] | undefined>
  >((acc, x) => {
    if (!(x.requestId in acc)) {
      acc[x.requestId] = [];
    }

    acc[x.requestId]!.push(x);

    return acc;
  }, {});

  return requests.map((request) => ({
    id: request.id,
    memberId: request.memberId,
    approversIds: (approversByRequestId[request.id] ?? []).map(
      (x) => x.approverId
    ),
    createdAt: request.createdAt,
    updatedAt: request.updatedAt,
    startDate: request.startDate,
    endDate: request.endDate,
    type: request.typeId,
    comment: request.comment ?? undefined,
    status: request.status as TimeOffRequest["status"],
    rejectReason: request.rejectReason ?? undefined,
    handledBy: request.handledBy ?? undefined,
    notifications: (
      JSON.parse(request.notifications) as TimeOffNotification[]
    ).map((notification) =>
      notification.type === TimeOffNotificationType.TimeOffReminder
        ? {
            ...notification,
            scheduleTime: new Date(notification.scheduleTime!),
          }
        : notification
    ),
  }));
}

function parseWorkspace(rawWorkspace: RawWorkspace): Workspace {
  assert(rawWorkspace.policy, "Expected workspace policy to exist");
  assert(rawWorkspace.reports, "Expected workspace reports to exist");
  assert(rawWorkspace.timeOffs, "Expected workspace time offs to exist");
  assert(
    rawWorkspace.kudosSettings,
    "Expected workspace kudos settings to exist"
  );

  const rawNodes = sortBy(rawWorkspace.orgTreeNodes, "order");

  const nodes = rawNodes.map((node): RootNode | Position | Department => {
    if (node.type === "position") {
      assert(
        node.position_title !== null,
        "Expected 'position_title' to be defined"
      );
      assert(node.parentId !== null, "Expected 'parentId' to be defined");
      assert(node.timestamp !== null, "Expected 'timestamp' to be defined");

      return {
        id: node.id,
        memberId: node.position_memberId,
        teamIds: node.position_teamIds,
        managedDepartmentId: node.position_managed_departmentId,
        managerTeamIds: node.position_managed_teamIds,
        title: node.position_title,
        reference: node.position_reference
          ? (JSON.parse(node.position_reference) as Position["reference"])
          : null,
        withoutManagerManual: node.position_withoutManagerManual,
        parentId: node.parentId,
        timestamp: node.timestamp,
        subordinates: [],
        type: "position",
      };
    }

    if (node.type === "department") {
      assert(
        node.department_color !== null,
        "Expected 'department_color' to be defined"
      );
      assert(
        node.department_title !== null,
        "Expected 'department_title' to be defined"
      );
      assert(node.parentId !== null, "Expected 'parentId' to be defined");
      assert(node.timestamp !== null, "Expected 'timestamp' to be defined");

      return {
        id: node.id,
        color: node.department_color as Color,
        title: node.department_title,
        parentId: node.parentId,
        managerId: node.department_managerId,
        timestamp: node.timestamp,
        subordinates: [],
        type: "department",
      };
    }

    return {
      id: node.id,
      subordinates: [],
      type: "root",
    };
  });

  const nodesHash = new Map(nodes.map((node) => [node.id, node]));

  for (const node of nodes) {
    if (node.type === "position" || node.type === "department") {
      const parent = nodesHash.get(node.parentId);

      assert(parent, "Expected parent to exist");

      if (parent.type === "root") {
        parent.subordinates.push(node);
      } else {
        parent.subordinates.push(node);
      }
    }
  }

  const rootNode = nodes.find((node): node is RootNode => node.type === "root");

  assert(rootNode, "Expected root node to exist");

  const onboarding = JSON.parse(rawWorkspace.onboarding) as RawOnboarding;

  const kudosValues = rawWorkspace.kudosSettings.values.map((rawValue) => ({
    id: rawValue.id,
    emoji: rawValue.emoji,
    title: rawValue.title,
    description: rawValue.description ?? undefined,
  }));

  const kudosChannel: PrismaChannel | undefined = rawWorkspace.channels.find(
    (channel) => channel.id === rawWorkspace.kudosSettings?.channelId
  );

  const timeOffs = parseTimeOffs(rawWorkspace);

  let kudosResetFrequency: KudosResetFrequency = KudosResetFrequency.Week;

  if (
    rawWorkspace.kudosSettings.resetFrequency ===
    PrismaKudosResetFrequency.Month
  ) {
    kudosResetFrequency = KudosResetFrequency.Month;
  }

  const chartLayout = JSON.parse(rawWorkspace.chartLayout) as {
    clusterHorizontal: boolean;
    dataField: string | null;
  };

  return {
    id: rawWorkspace.id,
    name: rawWorkspace.name,
    url: rawWorkspace.url,
    iconUrl: rawWorkspace.iconUrl ?? undefined,
    members: parseMembers(rawWorkspace),
    teams: rawWorkspace.teams.map((team) => ({
      createAt: team.createAt,
      updateAt: team.updateAt,
      id: team.id,
      label: team.label,
      color: team.color as Color,
      managerId: team.managerId,
    })),
    policy: {
      [PresetPolicyId.MANAGER]: {
        slackFieldId: rawWorkspace.policy.managerSlackFieldId ?? null,
      },
      [PresetPolicyId.JOB_TITLE]: {
        slackFieldId: rawWorkspace.policy.jobTitleSlackFieldId ?? null,
      },
      [PresetPolicyId.PHONE]: {
        slackFieldId: rawWorkspace.policy.phoneSlackFieldId ?? null,
        required: rawWorkspace.policy.phoneRequired,
      },
      [PresetPolicyId.BIRTHDAY]: {
        slackFieldId: rawWorkspace.policy.birthdaySlackFieldId ?? null,
        required: rawWorkspace.policy.birthdayRequired,
      },
      [PresetPolicyId.ANNIVERSARY]: {
        slackFieldId: rawWorkspace.policy.anniversarySlackFieldId ?? null,
        required: rawWorkspace.policy.anniversaryRequired,
      },
      [PresetPolicyId.PHOTO_URL]: {
        required: rawWorkspace.policy.photoUrlRequired,
      },
      [PresetPolicyId.DEPARTMENT]: {
        slackFieldId: rawWorkspace.policy.departmentSlackFieldId ?? null,
      },
      [PresetPolicyId.TEAMS]: {
        slackFieldId: rawWorkspace.policy.teamsSlackFieldId ?? null,
      },
      [PresetPolicyId.COUNTRY]: {
        required: rawWorkspace.policy.countryRequired,
        slackFieldId: rawWorkspace.policy.countrySlackFieldId ?? null,
      },

      customPolicyFields: rawWorkspace.policyFields.map((field) => ({
        id: field.id,
        label: field.label,
        type: field.type as PolicyFieldType,
        required: field.required,
        publiclyAvailable: field.publiclyAvailable,
        order: field.order,
        slackFieldId: field.slackFieldId ?? null,
      })),
      notifications: JSON.parse(
        rawWorkspace.policy.notifications
      ) as Policy["notifications"],
      wasInitiallySyncedWithSlack:
        rawWorkspace.policy.wasInitiallySyncedWithSlack,
    },
    billing: parseBilling(rawWorkspace),
    customFields: JSON.parse(rawWorkspace.customFields) as CustomField[],
    /**
     * NOTE: old clients will miss "bookmarkedFeatures" field
     * to avoid creating a migration we will add these fields in a lazy way
     * whenever they update a workspace next time the fields will be added
     */
    onboarding: {
      ...onboarding,
      bookmarkedFeatures: (onboarding.bookmarkedFeatures ??
        []) as SupportedFeature[],
    },
    orgTree: {
      rootNode,
    },
    predictedOrgTree: JSON.parse(
      rawWorkspace.predictedOrgTree
    ) as PredictedOrgChart,
    slackBotToken: rawWorkspace.slackBotToken ?? undefined,
    slackMemberTokens: rawWorkspace.slackMemberTokens.reduce<
      Record<Member["id"], SlackMemberToken>
    >((hash, token) => {
      hash[token.memberId] = token;

      return hash;
    }, {}),
    slackPlan: JSON.parse(rawWorkspace.slackPlan) as SlackPlan,
    installedAt: rawWorkspace.installedAt ?? undefined,
    installedBy: rawWorkspace.installedBy ?? undefined,
    wasDeleted: rawWorkspace.wasDeleted,
    channels: rawWorkspace.channels.map(parseChannel),
    reference: JSON.parse(rawWorkspace.reference) as Reference,
    celebrationSettings: parseCelebrationSettings(rawWorkspace.announcements),
    reports: {
      enabled: rawWorkspace.reports.enabled,
    },
    timeOffs,
    slackIntegration: rawWorkspace.slackIntegration,
    announcements: rawWorkspace.Announcements.map(parseAnnouncement),
    chartLayout: {
      clusterHorizontal: chartLayout.clusterHorizontal,
      dataField: chartLayout.dataField ?? undefined,
    },
    kudosSettings: {
      enable: rawWorkspace.kudosSettings.enable,
      kudosLimit: rawWorkspace.kudosSettings.kudosLimit,
      channel: kudosChannel
        ? {
            ...kudosChannel,
            membersCount: kudosChannel.membersCount ?? 0,
          }
        : undefined,
      values: kudosValues,
      resetFrequency: kudosResetFrequency,
      resetDay: rawWorkspace.kudosSettings.resetDay,
      resetTime: rawWorkspace.kudosSettings.resetTime,
      resetTimezone: rawWorkspace.kudosSettings.resetTimezone,
    },
    links: rawWorkspace.links.map(parseLink),
    surveyTemplates: [
      ...rawWorkspace.surveyTemplates.map((rawTemplate) =>
        parseSurveyTemplate(rawTemplate, rawWorkspace)
      ),
      ...getDefaultSurveyTemplates(rawWorkspace),
    ],
    surveys: rawWorkspace.surveys.map((s) => parseSurvey(s, rawWorkspace)),
    holidays: rawWorkspace.holidays,
    holidaysSettings: formatHolidaysSettings(
      rawWorkspace.holidaysSettings,
      rawWorkspace.channels
    ),
    slackStatusChanges: rawWorkspace.slackStatusChanges.map(
      parseSlackStatusChange
    ),

    softDeleted: {
      requests: parseTimeOffRequests(
        rawWorkspace.timeOffRequests.filter((request) => !!request.deletedAt),
        rawWorkspace
      ),
    },
    calendarFeeds: rawWorkspace.calendarFeeds.map((feed) =>
      parseCalendarFeed(feed)
    ),

    updatedAt: rawWorkspace.updatedAt ?? undefined,
    notifications: parseNotifications(rawWorkspace.notifications),
    publishedNotifications: rawWorkspace.publishedNotifications.map(
      (publishedNotification) => {
        const parsedState = JSON.parse(publishedNotification.state) as Omit<
          PublishedNotificationState,
          "start" | "end"
        > & {
          start: string;
          end: string;
        };

        return {
          id: publishedNotification.id,
          messageTS: publishedNotification.messageTS,
          sentAt: publishedNotification.sentAt,
          channelId: publishedNotification.channelId,
          state: {
            ...parsedState,
            start: new Date(parsedState.start),
            end: new Date(parsedState.end),
          },
          notificationId: publishedNotification.notificationId,
        };
      }
    ),
    notificationTemplates: defaultNotificationsTemplates,
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isValidFeedFilters(filters: any): filters is CalendarFeed["filters"] {
  if (!filters || typeof filters !== "object") {
    return false;
  }
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  const keys = Object.keys(filters);

  return (
    keys.includes("managers") &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    Array.isArray(filters.managers) &&
    keys.includes("departments") &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    Array.isArray(filters.departments) &&
    keys.includes("teams") &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    Array.isArray(filters.teams) &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    keys.includes("countries") &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    Array.isArray(filters.countries) &&
    keys.includes("types") &&
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    Array.isArray(filters.types)
  );
}

function parseBilling(rawWorkspace: RawWorkspace): Billing {
  assert(rawWorkspace.pricing, "Expected workspace pricing to exist");

  return {
    subscription:
      rawWorkspace.pricing.customerId && rawWorkspace.pricing.subscriptionId
        ? {
            ok: rawWorkspace.pricing.status === "ok",
            customerId: rawWorkspace.pricing.customerId,
            subscriptionId: rawWorkspace.pricing.subscriptionId,
            plan: rawWorkspace.pricing.plan as StripePlan,
            type: "stripe",
          }
        : rawWorkspace.pricing.appSumoCode &&
          rawWorkspace.pricing.appSumoEndDate
        ? {
            ok: rawWorkspace.pricing.status === "ok",
            endsAt: rawWorkspace.pricing.appSumoEndDate,
            code: rawWorkspace.pricing.appSumoCode,
            type: "appsumo",
          }
        : {
            ok: rawWorkspace.pricing.status === "ok",
            endsAt: new Date(rawWorkspace.pricing.trialEndDate ?? 0),
            type: "trial",
          },
  };
}

function parseCalendarFeed(feed: PrismaCalendarFeed): CalendarFeed {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const filters = JSON.parse(feed.filters);

  assert(isValidFeedFilters(filters), "Invalid feed filters");

  return {
    id: feed.id,
    title: feed.title,
    memberId: feed.memberId,
    filters,
  };
}

function parseTimeOffPolicy(
  policy: PrismaTimeOffPolicy & {
    typePolicies: PrismaTimeOffPolicyType[];
  }
): TimeOffPolicy {
  return {
    id: policy.id,
    title: policy.title,
    isDefault: policy.isDefault,
    workDays: policy.workDays,
    includedWeekendDays: policy.includedWeekendDays,
    notifyAccruals: policy.notifyAccruals,
    typePolicies: policy.typePolicies.map(
      (typePolicy): TimeOffPolicyType => ({
        typeId: typePolicy.typeId,
        onStartQuota: typePolicy.onStartQuota,
        rollOverToNextYear: typePolicy.rollOverToNextYear,
        yearStart: typePolicy.yearStart as YearStart,
        nextAccruals: typePolicy.nextAccruals,
        accrualsQuota: typePolicy.accrualsQuota,
        accuralsFrequency: typePolicy.accuralsFrequency as AccrualsFrequency,
        maxCapacity: typePolicy.maxCapacity,
      })
    ),
  };
}

function parseSurveyQuestion(
  rawQuestion: PrismaSurveyQuestion
): SurveyQuestion {
  const parsedQuestion = {
    id: rawQuestion.id,
    title: rawQuestion.title,
    type: rawQuestion.type as SurveyQuestion["type"],
    required: rawQuestion.required,

    singleOptions:
      rawQuestion.singleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.singleOptions) as TextOption[]),
    multipleOptions:
      rawQuestion.multipleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.multipleOptions) as TextOption[]),
    scaleOptions:
      rawQuestion.scaleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.scaleOptions) as ScaleOption[]).map(
            parseScaleOption
          ),
  };

  return parsedQuestion;
}

function parseSurveyTemplateQuestion(
  rawQuestion: PrismaSurveyTemplateQuestion
): SurveyTemplateQuestion {
  const parsedQuestion = {
    id: rawQuestion.id,
    title: rawQuestion.title,
    type: rawQuestion.type as SurveyQuestion["type"],
    required: rawQuestion.required,

    singleOptions:
      rawQuestion.singleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.singleOptions) as TextOption[]),
    multipleOptions:
      rawQuestion.multipleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.multipleOptions) as TextOption[]),
    scaleOptions:
      rawQuestion.scaleOptions === null
        ? undefined
        : (JSON.parse(rawQuestion.scaleOptions) as ScaleOption[]).map(
            parseScaleOption
          ),
  };

  return parsedQuestion;
}

function parseSurveyAnswer(rawSurveyAnswer: RawSurveyAnswer): SurveyAnswer {
  const value = rawSurveyAnswer.value
    ? (JSON.parse(rawSurveyAnswer.value) as SurveyAnswerValue)
    : "";

  const parsedAnswer = {
    id: rawSurveyAnswer.id,
    questionId: rawSurveyAnswer.questionId,
    responderId: rawSurveyAnswer.responderId,
    value,
  };

  return parsedAnswer;
}

function parseSurvey(rawSurvey: RawSurvey, rawWorkspace: RawWorkspace): Survey {
  const parsedSurvey = {
    id: rawSurvey.id,
    title: rawSurvey.title,
    startMessagePermalink: rawSurvey.startMessagePermalink,
    startMessageTS: rawSurvey.startMessageTS,
    templateId: rawSurvey.templateId,
    template: rawSurvey.template
      ? parseSurveyTemplate(rawSurvey.template, rawWorkspace)
      : null,
    defaultTemplateId: rawSurvey.defaultTemplateId,
    isAnonymous: rawSurvey.isAnonymous,
    status: rawSurvey.status as Survey["status"],
    channelId: rawSurvey.channelId,
    participantsCount: rawSurvey.participantsCount,
    answers: rawSurvey.answers.map((a) => parseSurveyAnswer(a)),
    questions: rawSurvey.questions.map(parseSurveyQuestion),
    createdAt: rawSurvey.createdAt,
    updatedAt: rawSurvey.updatedAt,
    createdById: rawSurvey.createdById,
  };

  return parsedSurvey;
}

function parseSurveyTemplate(
  rawTemplate: RawSurveyTemplate | PrismaSurveyTemplate,
  rawWorkspace: RawWorkspace
): SurveyTemplate {
  const surveys = rawWorkspace.surveys.filter(
    (s) => s.template?.id === rawTemplate.id
  );

  const templateQuestions =
    "questions" in rawTemplate
      ? sortBy(rawTemplate.questions, "order").map(parseSurveyTemplateQuestion)
      : [];

  const template = {
    id: rawTemplate.id,
    title: rawTemplate.title,
    questions: templateQuestions,
    createdById: rawTemplate.createdById,
    createdAt: rawTemplate.createdAt,
    updatedAt: rawTemplate.updatedAt,
    isDefault: false,
    usageCount: surveys.length,
  };

  return template;
}

function formatHolidaysSettings(
  settings: PrismaHolidaysSettings | null,
  channels: PrismaChannel[]
): HolidaysSettings {
  let channel: Channel | null = null;

  if (settings?.channelId) {
    const prismaChannel = channels.find(({ id }) => id === settings.channelId);

    if (prismaChannel) {
      channel = parseChannel(prismaChannel);
    }
  }

  return {
    createdAt: settings?.createdAt ?? new Date(),
    updatedAt: settings?.updatedAt ?? new Date(),
    countries: settings?.countries.sort() ?? [],
    channel,
  };
}

interface PrismaRows {
  workspace: Prisma.WorkspaceUncheckedCreateInput;
  workspacePolicy: Prisma.WorkspacePolicyUncheckedCreateInput;
  workspacePolicyFields: Prisma.PolicyFieldUncheckedCreateInput[];
  memberPolicyFields: Prisma.MemberPolicyFieldUncheckedCreateInput[];
  reports: Prisma.ReportsUncheckedCreateInput;
  pricing: Prisma.PricingUncheckedCreateInput;
  members: Prisma.MemberUncheckedCreateInput[];
  teams: Prisma.TeamUncheckedCreateInput[];
  orgTreeNodes: Prisma.OrgTreeNodeUncheckedCreateInput[];
  channels: Prisma.ChannelUncheckedCreateInput[];
  timeOffs: Prisma.TimeOffsUncheckedCreateInput;
  timeOffTypes: Prisma.TimeOffTypeUncheckedCreateInput[];
  timeOffRequests: Prisma.TimeOffRequestUncheckedCreateInput[];
  timeOffRequestsApprovers: Prisma.TimeOffRequestApproverUncheckedCreateInput[];
  announcements: Prisma.AnnouncementUncheckedCreateInput[];
  kudosSetting: Prisma.KudosSettingUncheckedCreateInput;
  links: Prisma.LinkUncheckedCreateInput[];
  surveyTemplates: Prisma.SurveyTemplateUncheckedCreateInput[];
  surveys: Prisma.SurveyUncheckedCreateInput[];
  surveyAnswers: Prisma.SurveyAnswerUncheckedCreateInput[];
  surveyQuestions: Prisma.SurveyQuestionUncheckedCreateInput[];
  surveyTemplateQuestions: Prisma.SurveyTemplateQuestionUncheckedCreateInput[];
  timeOffPolicies: Prisma.TimeOffPolicyUncheckedCreateInput[];
  timeOffPolicyTypes: Prisma.TimeOffPolicyTypeUncheckedCreateInput[];
  timeOffTypeMemberStates: Prisma.TimeOffTypeMemberStateUncheckedCreateInput[];
  holidaysSettings: Prisma.HolidaysSettingsUncheckedCreateInput;
  holidays: Prisma.HolidaysUncheckedCreateInput[];
  slackStatusChanges: Prisma.SlackStatusChangeUncheckedCreateInput[];
  kudosValues: Prisma.KudosValueUncheckedCreateInput[];
  calendarFeeds: Prisma.CalendarFeedUncheckedCreateInput[];
  notifications: Prisma.NotificationUncheckedCreateInput[];
  notificationSettings: Prisma.NotificationSettingsUncheckedCreateInput[];
  notificationBlocks: Prisma.NotificationBlockUncheckedCreateInput[];
  publishedNotifications: Prisma.PublishedNotificationUncheckedCreateInput[];
  slackBotTokens: Prisma.SlackBotTokenUncheckedCreateInput[];
  slackMemberTokens: Prisma.SlackMemberTokenUncheckedCreateInput[];
}

function toPrismaRows(workspace: Workspace): PrismaRows {
  const workspacePatch: Prisma.WorkspaceUncheckedCreateInput = {
    id: workspace.id,
    name: workspace.name,
    url: workspace.url,
    iconUrl: workspace.iconUrl,
    chartLayout: JSON.stringify(workspace.chartLayout),
    customFields: JSON.stringify(workspace.customFields),
    predictedOrgTree: JSON.stringify(workspace.predictedOrgTree),
    onboarding: JSON.stringify(workspace.onboarding),
    slackPlan: JSON.stringify(workspace.slackPlan),
    installedAt: workspace.installedAt,
    installedBy: workspace.installedBy,
    wasDeleted: workspace.wasDeleted,
    reference: JSON.stringify(workspace.reference),
    announcements: JSON.stringify(workspace.celebrationSettings),
    slackIntegration: workspace.slackIntegration,
  };

  const workspacePolicyPatch: Prisma.WorkspacePolicyUncheckedCreateInput = {
    phoneRequired: workspace.policy[PresetPolicyId.PHONE].required,
    birthdayRequired: workspace.policy[PresetPolicyId.BIRTHDAY].required,
    anniversaryRequired: workspace.policy[PresetPolicyId.ANNIVERSARY].required,
    countryRequired: workspace.policy[PresetPolicyId.COUNTRY].required,
    photoUrlRequired: workspace.policy[PresetPolicyId.PHOTO_URL].required,
    managerSlackFieldId: workspace.policy[PresetPolicyId.MANAGER].slackFieldId,
    jobTitleSlackFieldId:
      workspace.policy[PresetPolicyId.JOB_TITLE].slackFieldId,
    phoneSlackFieldId: workspace.policy[PresetPolicyId.PHONE].slackFieldId,
    birthdaySlackFieldId:
      workspace.policy[PresetPolicyId.BIRTHDAY].slackFieldId,
    anniversarySlackFieldId:
      workspace.policy[PresetPolicyId.ANNIVERSARY].slackFieldId,
    departmentSlackFieldId:
      workspace.policy[PresetPolicyId.DEPARTMENT].slackFieldId,
    teamsSlackFieldId: workspace.policy[PresetPolicyId.TEAMS].slackFieldId,
    countrySlackFieldId: workspace.policy[PresetPolicyId.COUNTRY].slackFieldId,
    notifications: JSON.stringify(workspace.policy.notifications),
    wasInitiallySyncedWithSlack: workspace.policy.wasInitiallySyncedWithSlack,
    workspaceId: workspace.id,
  };
  const workspacePolicyFieldsPatch = workspace.policy.customPolicyFields.map(
    (field): Prisma.PolicyFieldUncheckedCreateInput => ({
      id: field.id,
      label: field.label,
      type: field.type,
      required: field.required,
      publiclyAvailable: field.publiclyAvailable,
      order: field.order,
      slackFieldId: field.slackFieldId,
      workspaceId: workspace.id,
    })
  );
  const memberPolicyFieldsPatch = workspace.members.reduce<
    Prisma.MemberPolicyFieldUncheckedCreateInput[]
  >((memberPolicyFields, member) => {
    const policyFields = Object.entries(member.organiceCustomFileds).map(
      ([
        policyFieldId,
        value,
      ]): Prisma.MemberPolicyFieldUncheckedCreateInput => ({
        policyFieldId,
        value,
        workspaceId: workspace.id,
        memberId: member.id,
      })
    );

    return [...memberPolicyFields, ...policyFields];
  }, []);
  const pricingPatch: Prisma.PricingUncheckedCreateInput =
    workspace.billing.subscription.type === "stripe"
      ? {
          customerId: workspace.billing.subscription.customerId,
          subscriptionId: workspace.billing.subscription.subscriptionId,
          plan: workspace.billing.subscription.plan,
          limits: JSON.stringify({ free: 0, pro: 0, enterprise: 0 }),
          status: workspace.billing.subscription.ok ? "ok" : "past_due",
          workspaceId: workspace.id,
          trialEndDate: null,
          appSumoCode: null,
          appSumoEndDate: null,
        }
      : workspace.billing.subscription.type === "appsumo"
      ? {
          customerId: null,
          subscriptionId: null,
          plan: "pro",
          limits: JSON.stringify({ free: 0, pro: 0, enterprise: 0 }),
          status: workspace.billing.subscription.ok
            ? "ok"
            : "appsumo-suspended",
          workspaceId: workspace.id,
          trialEndDate: null,
          appSumoCode: workspace.billing.subscription.code,
          appSumoEndDate: workspace.billing.subscription.endsAt,
        }
      : {
          customerId: null,
          subscriptionId: null,
          plan: "free",
          limits: JSON.stringify({ free: 0, pro: 0, enterprise: 0 }),
          status: workspace.billing.subscription.ok ? "ok" : "trial-suspended",
          workspaceId: workspace.id,
          trialEndDate: workspace.billing.subscription.endsAt.valueOf(),
          appSumoCode: null,
          appSumoEndDate: null,
        };

  const membersPatch = workspace.members.map(
    (member): Prisma.MemberUncheckedCreateInput => ({
      id: member.id,
      isAdmin: member.isAdmin,
      isSlackWorkspaceAdmin: member.isSlackWorkspaceAdmin,
      isSlackWorkspaceOwner: member.isSlackWorkspaceOwner,
      isSlackWorkspacePrimaryOwner: member.isSlackWorkspacePrimaryOwner,
      workspaceId: workspace.id,
      name: member.name,
      photoUrl: member.photoUrl ?? null,
      photo512Url: member.photo512Url,
      photo72Url: member.photo72Url,
      realName: member.realName,
      displayName: member.displayName,
      email: member.email,
      organicePhone: member.organicePhone,
      birthday: member.birthday,
      hideBirthday: member.hideBirthday,
      notes: member.notes,
      updated: member.updated ? new Date(member.updated) : null,
      joinedAt: member.joinedAt ?? null,
      country: member.country ?? null,
      firstClickedProPlanAt: member.firstClickedProPlanAt,
      botState: JSON.stringify(member.botState),
      status: JSON.stringify(member.status),
      howManyTimesHomeTabVisited: member.howManyTimesHomeTabVisited,
      timeOffTypePolicyId: member.timeOffTypePolicyId ?? null,
      isSlackBillable: member.isSlackBillable,
      timeOffBalance: JSON.stringify(
        mapValues(member.timeOffs, (t) => t?.balance)
      ),
      timeOffNextReset: Object.values(member.timeOffs)[0]?.nextResetAt,
      timezone: member.timezone,
    })
  );

  let kudosResetFrequency: PrismaNotificationsFrequency =
    PrismaNotificationsFrequency.Week;

  if (workspace.kudosSettings.resetFrequency === KudosResetFrequency.Month) {
    kudosResetFrequency = PrismaNotificationsFrequency.Month;
  }

  const kudosSettingPatch: Prisma.KudosSettingUncheckedCreateInput = {
    enable: workspace.kudosSettings.enable,
    workspaceId: workspace.id,
    channelId: workspace.kudosSettings.channel?.id ?? null,
    kudosLimit: workspace.kudosSettings.kudosLimit,
    resetFrequency: kudosResetFrequency,
    resetDay: workspace.kudosSettings.resetDay,
    resetTime: workspace.kudosSettings.resetTime,
    resetTimezone: workspace.kudosSettings.resetTimezone,
  };

  const kudosValuesPatch: Prisma.KudosValueUncheckedCreateInput[] =
    workspace.kudosSettings.values.map((item) => ({
      id: item.id,
      emoji: item.emoji,
      title: item.title,
      description: item.description,
      workspaceId: workspace.id,
    }));

  const channelsPatch = workspace.channels.map(
    (channel): Prisma.ChannelUncheckedCreateInput => ({
      id: channel.id,
      name: channel.name,
      workspaceId: workspace.id,
      membersCount: channel.membersCount,
    })
  );

  const teamsPatch = workspace.teams.map(
    (team): Prisma.TeamUncheckedCreateInput => ({
      id: team.id,
      label: team.label,
      color: team.color,
      managerId: team.managerId,
      workspaceId: workspace.id,
    })
  );

  const reportsPatch: Prisma.ReportsUncheckedCreateInput = {
    enabled: workspace.reports.enabled,
    workspaceId: workspace.id,
  };

  const timeOffsPatch: Prisma.TimeOffsUncheckedCreateInput = {
    workspaceId: workspace.id,
    channelId: workspace.timeOffs.channelId,
    isEnable: workspace.timeOffs.isEnable,
    createDiscussionChannelWhenMultipleApprovers:
      workspace.timeOffs.createDiscussionChannelWhenMultipleApprovers,
    changeSlackStatus: workspace.timeOffs.changeSlackStatus,
  };

  const timeOffTypesPatch = workspace.timeOffs.types.map(
    (type, index): Prisma.TimeOffTypeUncheckedCreateInput => ({
      workspaceId: workspace.id,
      id: type.id,
      label: type.label,
      color: type.color,
      emoji: type.emoji,
      slackEmoji: type.slackEmoji,
      hint: type.hint ?? null,
      order: index + 1,
    })
  );

  const timeOffTypeMemberStatesPatch = workspace.members.flatMap((member) =>
    Object.entries(member.timeOffs).map(
      ([typeId, state]): Prisma.TimeOffTypeMemberStateUncheckedCreateInput => ({
        balance: state?.balance ?? 0,
        nextResetAt: state?.nextResetAt,
        memberId: member.id,
        typeId,
        workspaceId: workspace.id,
      })
    )
  );

  const timeOffRequestsPatch = workspace.timeOffs.requests.map(
    (request): Prisma.TimeOffRequestUncheckedCreateInput => ({
      workspaceId: workspace.id,
      id: request.id,
      memberId: request.memberId,
      typeId: request.type,
      startDate: request.startDate,
      endDate: request.endDate,
      comment: request.comment ?? null,
      status: request.status,
      rejectReason: request.rejectReason,
      notifications: JSON.stringify(request.notifications),
      handledBy: request.handledBy ?? null,
      createdAt: request.createdAt,
      updatedAt: request.updatedAt,
    })
  );

  const timeOffRequestsApproversPatch = workspace.timeOffs.requests.flatMap(
    (request) =>
      request.approversIds.map(
        (approverId): Prisma.TimeOffRequestApproverUncheckedCreateInput => ({
          workspaceId: workspace.id,
          requestId: request.id,
          approverId,
        })
      )
  );

  const announcementsPatch = workspace.announcements.map(
    (announcement): Prisma.AnnouncementUncheckedCreateInput => ({
      id: announcement.id,
      message: announcement.message,
      sentAt: announcement.sentAt,
      type: announcement.type,
      memberId: announcement.memberId,
      workspaceId: workspace.id,
      channelId: announcement.channelId,
    })
  );

  const { surveyTemplatesPatch, surveyTemplateQuestionsPatch } =
    getSurveyTemplatePatches(workspace);

  const { surveysPatch, surveyAnswersPatch, surveyQuestionsPatch } =
    getSurveyPatches(workspace);

  const orgTreeNodes: Prisma.OrgTreeNodeUncheckedCreateInput[] = [];

  function traverseOrgTree(
    node: RootNode | Position | Department,
    order?: number
  ): void {
    if (node.type === "position") {
      orgTreeNodes.push({
        id: node.id,
        position_memberId: node.memberId,
        position_teamIds: node.teamIds,
        position_managed_departmentId: node.managedDepartmentId,
        position_managed_teamIds: node.managerTeamIds,
        position_title: node.title,
        parentId: node.parentId,
        position_withoutManagerManual: node.withoutManagerManual,
        timestamp: node.timestamp,
        type: node.type,
        order,
        workspaceId: workspace.id,
        position_reference: JSON.stringify(node.reference),
      });
    } else if (node.type === "department") {
      orgTreeNodes.push({
        id: node.id,
        department_color: node.color,
        department_managerId: node.managerId,
        department_title: node.title,
        parentId: node.parentId,
        timestamp: node.timestamp,
        type: node.type,
        order,
        workspaceId: workspace.id,
      });
    } else {
      orgTreeNodes.push({
        id: node.id,
        type: node.type,
        workspaceId: workspace.id,
      });
    }

    node.subordinates.forEach((sub, idx) => traverseOrgTree(sub, idx));
  }

  traverseOrgTree(workspace.orgTree.rootNode);

  const linksPatch = workspace.links.map(
    (link): Prisma.LinkUncheckedCreateInput => ({
      ...link,
      workspaceId: workspace.id,
      chartLayout: JSON.stringify(link.chartLayout),
    })
  );

  const timeOffPoliciesPatch = getTimeOffPoliciesPatch(workspace);
  const timeOffPolicyTypesPatch = getTimeOffPolicyTypesPatch(workspace);

  const holidaysSettingsPatch: Prisma.HolidaysSettingsUncheckedCreateInput = {
    workspaceId: workspace.id,
    updatedAt: workspace.holidaysSettings.updatedAt,
    createdAt: workspace.holidaysSettings.createdAt,
    countries: workspace.holidaysSettings.countries,
    channelId: workspace.holidaysSettings.channel?.id,
  };

  const holidaysPatch: Prisma.HolidaysUncheckedCreateInput[] =
    workspace.holidays.map((holiday) => ({
      ...holiday,
      workspaceId: workspace.id,
    }));

  const slackStatusChangesPatch = getSlackStatusChangesPatch(workspace);

  const calendarFeedsPatch: Prisma.CalendarFeedUncheckedCreateInput[] =
    workspace.calendarFeeds.map((feed) => ({
      id: feed.id,
      title: feed.title,
      memberId: feed.memberId ?? null,
      filters: JSON.stringify(feed.filters),
      workspaceId: workspace.id,
    }));

  const {
    notificationsPatch,
    notificationsSettingsPatch,
    notificationsBlocksPatch,
  } = getNotificationsPatch(workspace);

  const notificationLogsPatch = workspace.publishedNotifications.map((log) => ({
    id: log.id,
    sentAt: log.sentAt,
    messageTS: log.messageTS,
    workspaceId: workspace.id,
    channelId: log.channelId,
    state: JSON.stringify(log.state),
    notificationId: log.notificationId,
  }));

  const slackBotTokensPatch: Prisma.SlackBotTokenUncheckedCreateInput[] =
    workspace.slackBotToken
      ? [
          {
            botId: workspace.slackBotToken.botId,
            botMemberId: workspace.slackBotToken.botMemberId,
            scopes: workspace.slackBotToken.scopes,
            token: workspace.slackBotToken.token,
            workspaceId: workspace.id,
          },
        ]
      : [];

  const slackMemberTokensPatch: Prisma.SlackMemberTokenUncheckedCreateInput[] =
    Object.values(workspace.slackMemberTokens)
      .filter((x): x is SlackMemberToken => x != null)
      .map((token) => ({
        memberId: token.memberId,
        scopes: token.scopes,
        token: token.token,
        workspaceId: workspace.id,
      }));

  return {
    workspace: workspacePatch,
    workspacePolicy: workspacePolicyPatch,
    workspacePolicyFields: workspacePolicyFieldsPatch,
    memberPolicyFields: memberPolicyFieldsPatch,
    pricing: pricingPatch,
    members: membersPatch,
    teams: teamsPatch,
    reports: reportsPatch,
    orgTreeNodes,
    channels: channelsPatch,
    timeOffs: timeOffsPatch,
    timeOffTypes: timeOffTypesPatch,
    timeOffRequests: timeOffRequestsPatch,
    timeOffRequestsApprovers: timeOffRequestsApproversPatch,
    timeOffTypeMemberStates: timeOffTypeMemberStatesPatch,
    announcements: announcementsPatch,
    kudosSetting: kudosSettingPatch,
    kudosValues: kudosValuesPatch,
    links: linksPatch,
    surveyTemplates: surveyTemplatesPatch,
    surveyTemplateQuestions: surveyTemplateQuestionsPatch,
    surveys: surveysPatch,
    surveyAnswers: surveyAnswersPatch,
    surveyQuestions: surveyQuestionsPatch,
    timeOffPolicies: timeOffPoliciesPatch,
    timeOffPolicyTypes: timeOffPolicyTypesPatch,
    holidaysSettings: holidaysSettingsPatch,
    holidays: holidaysPatch,
    slackStatusChanges: slackStatusChangesPatch,
    calendarFeeds: calendarFeedsPatch,
    notifications: notificationsPatch,
    notificationSettings: notificationsSettingsPatch,
    notificationBlocks: notificationsBlocksPatch,
    publishedNotifications: notificationLogsPatch,
    slackBotTokens: slackBotTokensPatch,
    slackMemberTokens: slackMemberTokensPatch,
  };
}

function getNotificationsPatch(workspace: Workspace): {
  notificationsPatch: Prisma.NotificationUncheckedCreateInput[];
  notificationsSettingsPatch: Prisma.NotificationSettingsUncheckedCreateInput[];
  notificationsBlocksPatch: Prisma.NotificationBlockUncheckedCreateInput[];
} {
  const notificationsPatch: Prisma.NotificationUncheckedCreateInput[] = [];
  const notificationsSettingsPatch: Prisma.NotificationSettingsUncheckedCreateInput[] =
    [];
  const notificationsBlocksPatch: Prisma.NotificationBlockUncheckedCreateInput[] =
    [];

  workspace.notifications.forEach((notification) => {
    const notificationInput: Prisma.NotificationUncheckedCreateInput = {
      id: notification.id,
      title: notification.title,
      isActive: notification.isActive,
      workspaceId: workspace.id,
      createdById: notification.createdById,
    };
    const { settings } = notification;

    let frequency: PrismaNotificationsFrequency =
      PrismaNotificationsFrequency.Month;

    if (settings.frequency === NotificationsFrequency.Week) {
      frequency = PrismaNotificationsFrequency.Week;
    }

    if (settings.frequency === NotificationsFrequency.Day) {
      frequency = PrismaNotificationsFrequency.Day;
    }

    const settingsInput: Prisma.NotificationSettingsUncheckedCreateInput = {
      notificationId: notification.id,
      frequency,
      day: settings.day,
      time: settings.time,
      timezone: settings.timezone,
      channelId: settings.channelId,
    };

    const blocksInputs: Prisma.NotificationBlockUncheckedCreateInput[] =
      notification.blocks.map((block) => ({
        id: block.id,
        notificationId: notification.id,
        type: block.type,
        data: JSON.stringify(block.data),
      }));

    notificationsPatch.push(notificationInput);
    notificationsSettingsPatch.push(settingsInput);
    notificationsBlocksPatch.push(...blocksInputs);
  });

  return {
    notificationsPatch,
    notificationsSettingsPatch,
    notificationsBlocksPatch,
  };
}

function getSlackStatusChangesPatch(
  workspace: Workspace
): Prisma.SlackStatusChangeUncheckedCreateInput[] {
  const changes = workspace.slackStatusChanges;

  return changes.map((change) => ({
    type: change.type,
    stashedStatus: change.stashedStatus
      ? JSON.stringify(change.stashedStatus)
      : "",
    refId: change.refId ?? "",
    memberId: change.memberId,
    workspaceId: workspace.id,
  }));
}

function getTimeOffPoliciesPatch(
  workspace: Workspace
): Prisma.TimeOffPolicyUncheckedCreateInput[] {
  const policies = workspace.timeOffs.policies;

  return policies.map((policy) => ({
    id: policy.id,
    title: policy.title,
    workspaceId: workspace.id,
    isDefault: policy.isDefault,
    yearStart: policy.typePolicies[0]?.yearStart ?? YearStart.Calendar,
    workDays: policy.workDays,
    includedWeekendDays: policy.includedWeekendDays,
    notifyAccruals: policy.notifyAccruals,
  }));
}

function getTimeOffPolicyTypesPatch(
  workspace: Workspace
): Prisma.TimeOffPolicyTypeUncheckedCreateInput[] {
  const policies = workspace.timeOffs.policies;

  return policies.reduce<Prisma.TimeOffPolicyTypeUncheckedCreateInput[]>(
    (acc, policy) => {
      policy.typePolicies.forEach((typePolicy) => {
        acc.push({
          policyId: policy.id,
          workspaceId: workspace.id,
          typeId: typePolicy.typeId,
          onStartQuota: typePolicy.onStartQuota,
          rollOverToNextYear: typePolicy.rollOverToNextYear,
          yearStart: typePolicy.yearStart,
          accrualsQuota: typePolicy.accrualsQuota,
          accuralsFrequency: typePolicy.accuralsFrequency,
          nextAccruals: typePolicy.nextAccruals,
          maxCapacity: typePolicy.maxCapacity,
        });
      });

      return acc;
    },
    []
  );
}

function concile<T, K>(
  id: (t: T) => K,
  prev: T[],
  curr: T[]
): { created: T[]; updated: { prev: T; curr: T }[]; deleted: T[] } {
  const prevMap = new Map(prev.map((p) => [id(p), p]));
  const currMap = new Map(curr.map((p) => [id(p), p]));

  return {
    created: curr.filter((value) => !prevMap.has(id(value))),
    updated: curr
      .filter(
        (value) =>
          prevMap.has(id(value)) && !isEqual(value, prevMap.get(id(value))!)
      )
      .map((value) => ({ curr: value, prev: prevMap.get(id(value))! })),
    deleted: prev.filter((value) => !currMap.has(id(value))),
  };
}

function diff<T extends Record<string, unknown>>(a: T, b: T): Partial<T> {
  return pickBy(a, (value, key) => !isEqual(value, b[key])) as T;
}

function getSurveyTemplatePatches(workspace: Workspace): {
  surveyTemplatesPatch: Prisma.SurveyTemplateUncheckedCreateInput[];
  surveyTemplateQuestionsPatch: Prisma.SurveyTemplateQuestionUncheckedCreateInput[];
} {
  const templates: Prisma.SurveyTemplateUncheckedCreateInput[] = [];
  let questions: Prisma.SurveyTemplateQuestionUncheckedCreateInput[] = [];

  workspace.surveyTemplates
    .filter((t) => !t.isDefault)
    .forEach((template) => {
      const templateInput = {
        id: template.id,
        title: template.title,
        workspaceId: workspace.id,
        createdById: template.createdById,
        createdAt: template.createdAt ?? new Date(),
      };

      templates.push(templateInput);

      const questionsInput = template.questions.map((question, index) => {
        return {
          id: question.id,
          templateId: template.id,
          title: question.title,
          required: question.required,
          type: question.type,
          order: index,
          scaleOptions: JSON.stringify(question.scaleOptions),
          singleOptions: JSON.stringify(question.singleOptions),
          multipleOptions: JSON.stringify(question.multipleOptions),
        };
      });

      questions = questions.concat(questionsInput);
    });

  return {
    surveyTemplatesPatch: templates,
    surveyTemplateQuestionsPatch: questions,
  };
}

function getSurveyPatches(workspace: Workspace): {
  surveysPatch: Prisma.SurveyUncheckedCreateInput[];
  surveyAnswersPatch: Prisma.SurveyAnswerUncheckedCreateInput[];
  surveyQuestionsPatch: Prisma.SurveyQuestionUncheckedCreateInput[];
} {
  const surveys: Prisma.SurveyUncheckedCreateInput[] = [];
  let surveyAnswers: Prisma.SurveyAnswerUncheckedCreateInput[] = [];
  let surveyQuestions: Prisma.SurveyQuestionUncheckedCreateInput[] = [];

  workspace.surveys.forEach((survey) => {
    const surveyChannel = workspace.channels.find(
      (ch) => ch.id === survey.channelId
    );
    const surveyInput = {
      id: survey.id,
      title: survey.title,
      startMessagePermalink: survey.startMessagePermalink,
      startMessageTS: survey.startMessageTS,
      status: survey.status,
      isAnonymous: survey.isAnonymous,
      workspaceId: workspace.id,
      channelId: survey.channelId,
      participantsCount:
        surveyChannel?.membersCount ?? survey.participantsCount,
      templateId: survey.template?.id ?? undefined,
      defaultTemplateId: survey.defaultTemplateId ?? undefined,
      createdById: survey.createdById,
    };

    surveys.push(surveyInput);

    const answersInput = survey.answers.map((answer) => {
      return {
        id: answer.id,
        surveyId: survey.id,
        value: JSON.stringify(answer.value),
        workspaceId: workspace.id,
        responderId: answer.responderId,
        questionId: answer.questionId,
      };
    });

    surveyAnswers = surveyAnswers.concat(answersInput);

    const questionsInput = survey.questions.map((question) => {
      return {
        id: question.id,
        surveyId: survey.id,
        title: question.title,
        required: question.required,
        type: question.type,
        scaleOptions: JSON.stringify(question.scaleOptions),
        singleOptions: JSON.stringify(question.singleOptions),
        multipleOptions: JSON.stringify(question.multipleOptions),
      };
    });

    surveyQuestions = surveyQuestions.concat(questionsInput);
  });

  return {
    surveysPatch: surveys,
    surveyAnswersPatch: surveyAnswers,
    surveyQuestionsPatch: surveyQuestions,
  };
}

export function getDefaultSurveyTemplates(
  rawWorkspace: RawWorkspace
): SurveyTemplate[] {
  const defaultTemplates: SurveyTemplate[] = defaultSurveyTemplates.map(
    (defaultTemplate) => {
      const surveys = rawWorkspace.surveys.filter(
        (s) => s.defaultTemplateId === defaultTemplate.id
      );

      return {
        ...defaultTemplate,
        isDefault: true,
        createdById: null,
        createdAt: new Date("11-03-1994"),
        updatedAt: new Date("11-03-1994"),
        usageCount: surveys.length,
        questions: defaultTemplate.questions.map((q) => {
          const question = q as typeof q &
            Pick<
              SurveyTemplateQuestion,
              "scaleOptions" | "singleOptions" | "multipleOptions"
            >;
          const type = question.singleOptions
            ? SurveyQuestionType.SingleOption
            : question.scaleOptions
            ? SurveyQuestionType.Scale
            : question.multipleOptions
            ? SurveyQuestionType.MultipleOptions
            : SurveyQuestionType.Text;

          return {
            ...q,
            required: false,
            type,
            scaleOptions: question.scaleOptions?.map((o) => ({
              ...parseScaleOption(o),
              id: crypto.randomUUID(),
            })),
            singleOptions: question.singleOptions?.map((o) => ({
              ...o,
              id: crypto.randomUUID(),
            })),
            multipleOptions: question.multipleOptions?.map((o) => ({
              ...o,
              id: crypto.randomUUID(),
            })),
          };
        }),
      };
    }
  );

  return defaultTemplates;
}

function parseScaleOption(scaleOption: ScaleOption): ScaleOption {
  const labels: Record<string, string> = {
    "1": "Strongly Disagree",
    "2": "Disagree",
    "3": "Neutral",
    "4": "Agree",
    "5": "Strongly Agree",
  };
  const label = labels[scaleOption.value.toString()];

  return { ...scaleOption, label };
}

/**
 * NOTE: as we might assign a member from one position to another
 * within 1 transaction we must change the order so that a position
 * that loses a member is updated first
 *
 * the same happens with managed department
 */
function sortNodesUpdates<
  T extends Prisma.OrgTreeNodeUncheckedCreateInput
>(nodes: {
  created: T[];
  updated: { prev: T; curr: T }[];
  deleted: T[];
}): void {
  nodes.updated.sort((a, b) => {
    const aLostMember = !a.curr.position_memberId && a.prev.position_memberId;
    const bLostMember = !b.curr.position_memberId && b.prev.position_memberId;

    if (aLostMember && a.prev.position_memberId === b.curr.position_memberId) {
      return -1;
    }

    if (bLostMember && b.prev.position_memberId === a.curr.position_memberId) {
      return 1;
    }

    const aLostDepartment =
      !a.curr.position_managed_departmentId &&
      a.prev.position_managed_departmentId;

    const bLostDepartment =
      !b.curr.position_managed_departmentId &&
      b.prev.position_managed_departmentId;

    if (
      aLostDepartment &&
      a.prev.position_managed_departmentId ===
        b.curr.position_managed_departmentId
    ) {
      return -1;
    }

    if (
      bLostDepartment &&
      b.prev.position_managed_departmentId ===
        a.curr.position_managed_departmentId
    ) {
      return 1;
    }

    return 0;
  });
}

export default SentrySpanMethods(PrismaWorkspaceRepository);
