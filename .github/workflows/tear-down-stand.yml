name: Tear Down Stand
run-name: Tear Down Stand ${{ github.head_ref }}.organice.app
on:
  pull_request:
    types:
      - closed
jobs:
  stand_tear_down:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
      - uses: actions/cache@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./organice-core/node_modules
            ./organice-admin-site/node_modules
            ./organice-background-worker/node_modules
            ./organice-slack-bot/node_modules
          key: modules-${{ hashFiles('package-lock.json') }}-${{ hashFiles('.nvmrc') }}
      - run: npm ci --ignore-scripts
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run generate
      - run: printf "stacks=%s" "$(pulumi stack ls --json | python3 -c 'import json,sys;obj=json.load(sys.stdin);print(json.dumps(obj))')" >> "$GITHUB_OUTPUT"
        id: pulumi-stacks
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
      - run: echo "stand_slug=$(npm run slugify --silent -- ${{ github.head_ref }})" >> "$GITHUB_OUTPUT"
        id: stand_slug
      - uses: pulumi/actions@v4
        if: contains(fromJSON(steps.pulumi-stacks.outputs.stacks).*.name, steps.stand_slug.outputs.stand_slug) && steps.stand_slug.outputs.stand_slug != 'production'
        with:
          command: destroy
          stack-name: ${{ steps.stand_slug.outputs.stand_slug }}
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          GIT_BRANCH: ${{ github.head_ref }}
      - run: npm run delete-stand-configuration
        if: contains(fromJSON(steps.pulumi-stacks.outputs.stacks).*.name, steps.stand_slug.outputs.stand_slug) && steps.stand_slug.outputs.stand_slug != 'production'
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
          STAND_SLUG: ${{ steps.stand_slug.outputs.stand_slug }}
