name: Deploy Production
run-name: Deploy to production by @${{ github.actor }}
on: workflow_dispatch
jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
      - uses: actions/cache@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./organice-core/node_modules
            ./organice-admin-site/node_modules
            ./organice-background-worker/node_modules
            ./organice-slack-bot/node_modules
          key: modules-${{ hashFiles('package-lock.json') }}-${{ hashFiles('.nvmrc') }}
      - run: npm ci --ignore-scripts
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run generate
      - uses: docker/setup-buildx-action@v1
        with:
          install: true
          driver: docker
          buildkitd-flags: --debug
      - uses: pulumi/actions@v3
        with:
          command: up
          stack-name: production
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          GIT_BRANCH: ${{ github.ref_name }}
