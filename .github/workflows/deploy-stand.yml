name: Deploy Stand
run-name: Deploy Stand ${{ github.ref_name }}.stand-do.organice.app
on:
  workflow_dispatch:
    inputs:
      slack_config_access_token:
        description: "Slack Config Access Token (api.slack.com/apps)"
        required: true
      force_stripe_webhook_signing_secret:
        description: "Stripe webhook signing secret"
      force_trial_duration_in_minutes:
        description: "Trial duration (in minutes)"
      force_notification_period_in_minutes:
        description: "Notification period (in minutes)"
        default: "1"
      force_intercom_onboarding_follow_up_delay_in_minutes:
        description: "Intercom onboarding follow up delay (in minutes)"
        default: "5"
      force_pricing_message_delay_in_minutes:
        description: "Intercom pricing message delay (in minutes)"
        default: "10"
      force_slack_test_server:
        description: "Run Slack test server"
        default: false
        type: "boolean"
jobs:
  deploy:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    environment:
      name: ${{ github.ref_name }}
      url: ${{ steps.pulumi_deploy.outputs.adminSiteUrl }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"
      - uses: actions/cache@v4
        id: cache
        with:
          path: |
            ./node_modules
            ./organice-core/node_modules
            ./organice-admin-site/node_modules
            ./organice-background-worker/node_modules
            ./organice-slack-bot/node_modules
          key: modules-${{ hashFiles('package-lock.json') }}-${{ hashFiles('.nvmrc') }}
      - run: npm ci --ignore-scripts
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run generate
      - uses: docker/setup-buildx-action@v1
        with:
          install: true
          driver: docker
          buildkitd-flags: --debug
      - uses: pulumi/actions@v4
      - run: echo "stand_slug=$(npm run slugify --silent -- ${{ github.ref_name }})" >> "$GITHUB_OUTPUT"
        id: stand_slug
      - run: npm run generate-stand-configuration
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
          SLACK_CONFIG_ACCESS_TOKEN: ${{ github.event.inputs.slack_config_access_token }}
          STAND_SLUG: ${{ steps.stand_slug.outputs.stand_slug }}
      - uses: pulumi/actions@v4
        id: pulumi_deploy
        with:
          command: up
          stack-name: ${{ steps.stand_slug.outputs.stand_slug }}
          upsert: true
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          GIT_BRANCH: ${{ github.ref_name }}
          FORCE_STRIPE_WEBHOOK_SIGNING_SECRET: ${{ github.event.inputs.force_stripe_webhook_signing_secret }}
          FORCE_TRIAL_DURATION_IN_MINUTES: ${{ github.event.inputs.force_trial_duration_in_minutes }}
          FORCE_PRICING_MESSAGE_DELAY_IN_MINUTES: ${{ github.event.inputs.force_pricing_message_delay_in_minutes }}
          FORCE_NOTIFICATION_PERIOD_IN_MINUTES: ${{ github.event.inputs.force_notification_period_in_minutes }}
          FORCE_SLACK_TEST_SERVER: ${{ github.event.inputs.force_slack_test_server == 'true' && '1' || '' }}
          FORCE_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES: ${{ github.event.inputs.force_intercom_onboarding_follow_up_delay_in_minutes }}
