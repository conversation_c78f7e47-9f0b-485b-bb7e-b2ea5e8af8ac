import { once, isEqual } from "lodash";
import {
  createContext,
  useMemo,
  useContext,
  useState,
  ReactNode,
  ReactElement,
  useEffect,
  useCallback,
} from "react";
import {
  useReactFlow,
  Node as FlowNode,
  Edge,
  XYPosition,
  Rect,
} from "reactflow";

import {
  getDimensionForNode,
  IntersectionSide,
} from "../components/OrgChart/utils";
import {
  RootNode as DataRootNode,
  Node as DataNode,
} from "../components/Tree/types";
import { EdgeData } from "../helpers/buildTree";
import { LoadNodeWithSubordinatesFunc } from "../hooks/useAllKnownNodes";
import { TreeState, TreeActions } from "../hooks/useTree";

interface OrgChartContext<TNode extends DataNode, TRoot extends DataRootNode> {
  treeState: TreeState;
  treeActions: TreeActions<TNode>;
  loadSubordinates: LoadNodeWithSubordinatesFunc;
  chartState: ChartState | null;
  getActivePath: (nodeId: string) => string[];
  registerNewNode: (targetNodeId: string) => void;
  unregisterNewNode: () => void;
  registerAffectedNodes: (nodes: string[], ms?: number) => void;
  unregisterAffectedNodes: () => void;
  calcualteOutgoingEdges: (
    nodes: FlowNode<TNode | TRoot>[],
    edges: Edge<EdgeData>[]
  ) => void;
  setDraggingNode: (node: DraggingNode | null) => void;
  onNewNodeOpen?: () => void;
  onTeamClick?: (teamId: string) => void;
  dataField?: DataFieldWithType;
}

const createStateContext = once(<
  TNode extends DataNode,
  TRoot extends DataRootNode
>() => createContext({} as OrgChartContext<TNode, TRoot>));

interface OutgoingEdge {
  id: string;
  sourceHandler: {
    x: number;
    y: number;
  };
  targetHandler: {
    x: number;
    y: number;
  };
}
interface OutgoingEdges {
  id: string;
  sourceHandler: {
    x: number;
    y: number;
  };
  targetHandler: {
    x: number;
    y: number;
  };
  edges: OutgoingEdge[];
}

interface DraggingNode {
  node: string;
  nodeParent: string | null;
  subTree: string[];
  intersectedNode: string | null;
  intersectedSide: IntersectionSide | null;
  rect: Rect | null;
  position?: XYPosition;
}

interface ChartState {
  outgoingEdges: OutgoingEdges[];
  activePath: string[];
  newNodeFor: string | null;
  /**
   * NOTE: we use this field to better fitView the chart
   */
  affectedNodes: string[];
  draggingNode: DraggingNode | null;
}

export interface DataField {
  id: string;
  label: string;
}

export interface DataFieldWithType extends DataField {
  type: string;
}

const emptyChartState = {
  activePath: [],
  newNodeFor: null,
  outgoingEdges: [],
  affectedNodes: [],
  draggingNode: null,
};

export const OrgChartProvider = <
  TNode extends DataNode,
  TRoot extends DataRootNode
>({
  children,
  nodeToActivePath,
  treeState,
  treeActions,
  loadSubordinates,
  onNewNodeOpen,
  onTeamClick,
  dataField,
}: {
  children?: ReactNode;
  nodeToActivePath: Record<string, string[] | undefined>;
  treeState: TreeState;
  treeActions: TreeActions<TNode>;
  loadSubordinates: LoadNodeWithSubordinatesFunc;
  onNewNodeOpen?: () => void;
  onTeamClick?: (teamId: string) => void;
  dataField?: DataFieldWithType;
}): ReactElement | null => {
  const OrgChartContext = createStateContext<TNode, TRoot>();
  const [chartState, setChartState] = useState<ChartState | null>(null);
  const { getNodes } = useReactFlow();

  const getActivePath = useCallback(
    (nodeId: string): string[] => {
      return nodeToActivePath[nodeId] ?? [];
    },
    [nodeToActivePath]
  );

  const registerNewNode = useCallback((targetNodeId: string) => {
    setChartState((state) => ({
      ...(state ?? emptyChartState),
      newNodeFor: targetNodeId,
    }));
  }, []);

  const unregisterNewNode = useCallback(() => {
    setChartState((state) => ({
      ...(state ?? emptyChartState),
      newNodeFor: null,
    }));
  }, []);

  const registerAffectedNodes = useCallback((nodes: string[], ms?: number) => {
    /**
     * NOTE: reposition with a short delay to make sure
     * that affectedNode got updated coordinates
     */
    setTimeout(() => {
      setChartState((state) => ({
        ...(state ?? emptyChartState),
        affectedNodes: nodes,
      }));
    }, ms ?? 0);
  }, []);

  const unregisterAffectedNodes = useCallback(() => {
    setChartState((state) => ({
      ...(state ?? emptyChartState),
      affectedNodes: [],
    }));
  }, []);

  const calcualteOutgoingEdges = useCallback(
    (nodes: FlowNode<TNode | TRoot>[], edges: Edge<EdgeData>[]) => {
      const nodeHandlersPosition = nodes.map((node) => {
        const { position } = node;
        const { x, y } = position;
        const { width, height } = getDimensionForNode(node.type);

        /**
         * NOTE: we assume that target (incoming) handler is always on top
         * in the and source (outgoing) handler is always on bottom in the middle
         */
        const sourceHandler = { x: x + width / 2, y: y + height };
        const targetHandler = { x: x + width / 2, y };

        return {
          id: node.id,
          sourceHandler,
          targetHandler,
        };
      });
      const outgoingEdges = edges.map((edge) => ({
        id: edge.id,
        sourceHandler: nodeHandlersPosition.find((h) => h.id === edge.source)
          ?.sourceHandler ?? {
          x: 0,
          y: 0,
        },
        targetHandler: nodeHandlersPosition.find((h) => h.id === edge.target)
          ?.targetHandler ?? {
          x: 0,
          y: 0,
        },
        edges: edges
          .filter((e) => e.source === edge.source && e.id !== edge.id)
          .map((e) => ({
            id: e.id,
            sourceHandler: nodeHandlersPosition.find((h) => h.id === e.source)
              ?.sourceHandler ?? {
              x: 0,
              y: 0,
            },
            targetHandler: nodeHandlersPosition.find((h) => h.id === e.target)
              ?.targetHandler ?? {
              x: 0,
              y: 0,
            },
          })),
      }));

      if (!isEqual(chartState?.outgoingEdges, outgoingEdges)) {
        setChartState((state) => ({
          ...(state ?? emptyChartState),
          outgoingEdges,
        }));
      }
    },
    [chartState?.outgoingEdges]
  );

  useEffect(() => {
    const nodes = getNodes();
    const activePath: string[] = [];

    let node = nodes.find((n) => n.id === treeState.currentNodeId);

    if (node) {
      activePath.push(node.id);

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      while (node?.data.parentId) {
        // eslint-disable-next-line @typescript-eslint/no-loop-func, @typescript-eslint/no-unsafe-member-access
        node = nodes.find((n) => n.id === node?.data.parentId);

        if (node) {
          activePath.push(node.id);
        }
      }
    }

    setChartState((state) => ({
      ...(state ?? emptyChartState),
      activePath,
    }));
  }, [treeState.currentNodeId, getNodes]);

  const setDraggingNode = useCallback((node: DraggingNode | null) => {
    setChartState((state) => ({
      ...(state ?? emptyChartState),
      draggingNode: node,
    }));
  }, []);

  const context = useMemo(() => {
    return {
      treeState,
      treeActions,
      loadSubordinates,
      getActivePath,
      chartState,
      registerNewNode,
      unregisterNewNode,
      calcualteOutgoingEdges,
      registerAffectedNodes,
      unregisterAffectedNodes,
      setDraggingNode,
      onNewNodeOpen,
      onTeamClick,
      dataField,
    };
  }, [
    treeState,
    treeActions,
    loadSubordinates,
    getActivePath,
    chartState,
    registerNewNode,
    unregisterNewNode,
    calcualteOutgoingEdges,
    registerAffectedNodes,
    unregisterAffectedNodes,
    setDraggingNode,
    onNewNodeOpen,
    onTeamClick,
    dataField,
  ]);

  return (
    <OrgChartContext.Provider value={context}>
      {children}
    </OrgChartContext.Provider>
  );
};

export const useOrgChartContext = <
  TNode extends DataNode,
  TRoot extends DataRootNode
>(): OrgChartContext<TNode, TRoot> =>
  useContext<OrgChartContext<TNode, TRoot>>(createStateContext<TNode, TRoot>());
