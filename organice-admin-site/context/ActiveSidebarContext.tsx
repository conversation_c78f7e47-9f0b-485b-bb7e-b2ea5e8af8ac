import { AnimatePresence } from "framer-motion";
import { useRouter } from "next/router";
import React, {
  useState,
  useContext,
  createContext,
  Context,
  useMemo,
  useEffect,
  useRef,
  useCallback,
} from "react";

import RemovalModal from "../components/RemovalModal";
import EditableDepartmentSidebar, {
  DepartmentSidebarData,
} from "../components/Sidebar/DepartmentSidebar/EditableDepartmentSidebar";
import EditableMemberSidebar, {
  SidebarTab,
} from "../components/Sidebar/MemberSidebar/EditableMemberSidebar";
import EditablePositionSidebar from "../components/Sidebar/PositionSidebar/EditablePositionSidebar";
import EditableTeamSidebar, {
  TeamSidebarData,
} from "../components/Sidebar/TeamSidebar/EditableTeamSidebar";
import { EditableFlatPosition } from "../hooks/useFlattenPosition";
import { getRemoveConfirmText } from "../hooks/useRemoveNodeFromSidebar";

interface BaseSidebar {
  onOpen?: () => void;
  onClose?: () => void;
}

interface MemberSidebar extends BaseSidebar {
  type: "member";
  id: string;
  positionId?: string;
  tabs: SidebarTab[];
  mode: "edit" | "read";
  setExpandedIds?: (ids: string[]) => void;
  showGoToOrgChartBtn?: boolean;
  removeNodeHandler?: () => Promise<void>;
}

interface PositionSidebar extends BaseSidebar {
  type: "position";
  id: string;
  mode: "edit" | "read";
  setExpandedIds?: (ids: string[]) => void;
  showGoToOrgChartBtn?: boolean;
  removeNodeHandler: () => Promise<void>;
}

interface DepartmentSidebar extends BaseSidebar {
  type: "department";
  data?: DepartmentSidebarData;
  removeNodeHandler?: () => Promise<void>;
}

interface TeamSidebar extends BaseSidebar {
  type: "team";
  data?: TeamSidebarData;
}

export type ActiveSidebar =
  | MemberSidebar
  | PositionSidebar
  | DepartmentSidebar
  | TeamSidebar;

const ActiveSidebarContext = createContext<{
  activeSidebar: ActiveSidebar | null;
  openSidebar: (sidebar: ActiveSidebar) => void;
  closeSidebar: (sidebar: ActiveSidebar) => void;
}>({
  activeSidebar: null,
  openSidebar: () => null,
  closeSidebar: () => null,
});

export const ActiveSidebarProvider: React.FC<{
  children: React.ReactElement;
}> = ({ children }) => {
  const router = useRouter();
  const [activeSidebar, setActiveSidebar] = useState<ActiveSidebar | null>(
    null
  );
  const [isRemovalModalOpen, setIsRemovalModalOpen] = useState(false);
  const [loadedPosition, setLoadedPosition] =
    useState<EditableFlatPosition | null>(null);

  const onPositionDataLoaded = useCallback(
    (position: EditableFlatPosition) => {
      if (!loadedPosition) {
        setLoadedPosition(position);
      }
    },
    [loadedPosition]
  );

  const canRemoveMemberPosition = useMemo(() => {
    return (
      activeSidebar?.type === "member" &&
      activeSidebar.removeNodeHandler &&
      activeSidebar.positionId
    );
  }, [activeSidebar]);

  const page = useRef<string | null>(null);

  const context = useMemo(() => {
    return {
      activeSidebar,
      openSidebar: (sidebar: ActiveSidebar): void => {
        if (activeSidebar) {
          if (activeSidebar.onClose) {
            activeSidebar.onClose();
          }
        }
        setActiveSidebar(sidebar);

        if (sidebar.onOpen) {
          sidebar.onOpen();
        }
        page.current = router.pathname;
      },
      closeSidebar: (sidebar: ActiveSidebar): void => {
        if (sidebar === activeSidebar) {
          if (activeSidebar.onClose) {
            activeSidebar.onClose();
          }
        }
        setActiveSidebar(null);
      },
    };
  }, [activeSidebar, router.pathname]);

  const removeModalText = useMemo(() => {
    if (activeSidebar?.type === "member") {
      return getRemoveConfirmText({
        type: "member",
      });
    }

    if (activeSidebar?.type === "position") {
      return getRemoveConfirmText({
        type: "position",
        hasReferrals: loadedPosition?.reference.isIncludeToReferrals ?? false,
      });
    }

    return getRemoveConfirmText({
      type: "department",
    });
  }, [activeSidebar?.type, loadedPosition]);

  useEffect(() => {
    /**
     * NOTE: close sidebar if we leave the route
     */
    const onPageLeave = (): void => {
      if (page.current && page.current !== router.pathname && activeSidebar) {
        setActiveSidebar(null);
      }
    };

    router.events.on("routeChangeComplete", onPageLeave);

    return () => {
      /**
       * NOTE: unsubscribe from the event with a little delay
       * so that onPageLeave can be called for 1 more time
       */
      setTimeout(() => router.events.off("routeChangeComplete", onPageLeave));
    };
  }, [activeSidebar, router]);

  const onRemoveModalSubmit = useCallback(() => {
    if (
      !activeSidebar ||
      !("removeNodeHandler" in activeSidebar) ||
      !activeSidebar.removeNodeHandler
    ) {
      return;
    }
    setIsRemovalModalOpen(false);

    activeSidebar
      .removeNodeHandler()
      .then(() => context.closeSidebar(activeSidebar))
      .catch(() => {});
  }, [activeSidebar, context]);

  return (
    <ActiveSidebarContext.Provider value={context}>
      {children}

      <AnimatePresence initial={false}>
        {isRemovalModalOpen &&
        activeSidebar &&
        removeModalText &&
        "removeNodeHandler" in activeSidebar &&
        activeSidebar.removeNodeHandler ? (
          <RemovalModal
            title={removeModalText}
            onSubmit={onRemoveModalSubmit}
            onClose={() => setIsRemovalModalOpen(false)}
          />
        ) : null}
      </AnimatePresence>
      <AnimatePresence mode="wait">
        {activeSidebar?.type === "position" ? (
          <EditablePositionSidebar
            key={activeSidebar.id}
            id={activeSidebar.id}
            mode={activeSidebar.mode}
            closeSidebar={() => context.closeSidebar(activeSidebar)}
            setExpandedIds={activeSidebar.setExpandedIds}
            removeNode={() => setIsRemovalModalOpen(true)}
            showGoToOrgChartBtn={activeSidebar.showGoToOrgChartBtn}
            onPositionDataLoaded={onPositionDataLoaded}
          />
        ) : null}
        {activeSidebar?.type === "member" ? (
          <EditableMemberSidebar
            key={activeSidebar.id}
            id={activeSidebar.id}
            tabs={activeSidebar.tabs}
            mode={activeSidebar.mode}
            setExpandedIds={activeSidebar.setExpandedIds}
            closeSidebar={() => context.closeSidebar(activeSidebar)}
            removeNode={
              canRemoveMemberPosition
                ? () => setIsRemovalModalOpen(true)
                : undefined
            }
            showGoToOrgChartBtn={activeSidebar.showGoToOrgChartBtn}
            changeTab={(tabId: string) => {
              setActiveSidebar((prevSidebar) => {
                if (prevSidebar?.type !== "member") {
                  return prevSidebar;
                }

                return {
                  ...prevSidebar,
                  tabs: prevSidebar.tabs.map<SidebarTab>((tab) => {
                    return {
                      ...tab,
                      isActive: tab.id === tabId,
                    };
                  }),
                };
              });
            }}
          />
        ) : null}
        {activeSidebar?.type === "department" ? (
          <EditableDepartmentSidebar
            key={activeSidebar.data?.id ?? "new-department"}
            departmentData={activeSidebar.data}
            removeNode={() => setIsRemovalModalOpen(true)}
            closeSidebar={() => context.closeSidebar(activeSidebar)}
          />
        ) : null}
        {activeSidebar?.type === "team" ? (
          <EditableTeamSidebar
            key={activeSidebar.data?.id ?? "new-team"}
            teamData={activeSidebar.data}
            closeSidebar={() => {
              context.closeSidebar(activeSidebar);
            }}
          />
        ) : null}
      </AnimatePresence>
    </ActiveSidebarContext.Provider>
  );
};

export const useActiveSidebarContext = (): {
  activeSidebar: ActiveSidebar | null;
  openSidebar: (sidebar: ActiveSidebar) => void;
  closeSidebar: (sidebar: ActiveSidebar) => void;
} =>
  useContext(
    ActiveSidebarContext as Context<{
      activeSidebar: ActiveSidebar | null;
      openSidebar: (sidebar: ActiveSidebar) => void;
      closeSidebar: (sidebar: ActiveSidebar) => void;
    }>
  );
