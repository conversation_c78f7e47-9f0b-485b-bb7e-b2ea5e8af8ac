/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import React, {
  useState,
  useContext,
  createContext,
  useMemo,
  Dispatch,
  SetStateAction,
  Context,
} from "react";

type PageData<T> = T;

const PageContext = createContext<{
  data: PageData<unknown> | null;
  setData: Dispatch<SetStateAction<PageData<unknown> | null>>;
}>({
  data: null,
  setData: () => null,
});

export const PageProvider: React.FC<{
  children: React.ReactElement;
}> = ({ children }) => {
  const [data, setData] = useState<PageData<unknown> | null>(null);

  const context = useMemo(() => {
    return { data, setData };
  }, [data]);

  return (
    <PageContext.Provider value={context}>{children}</PageContext.Provider>
  );
};

export const usePageContext = <T,>(): {
  data: PageData<T> | null;
  setData: Dispatch<SetStateAction<PageData<T> | null>>;
} =>
  useContext(
    PageContext as Context<{
      data: PageData<T> | null;
      setData: Dispatch<SetStateAction<PageData<T> | null>>;
    }>
  );
