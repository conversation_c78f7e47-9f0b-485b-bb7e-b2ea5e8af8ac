import { countries } from "@organice/core/domain/data-completion";

import {
  IMemberField,
  Color,
  PhotoField,
  DepartmentField,
  TeamsField,
  ManagerField,
  DateField,
  TextField,
  LinkField,
  UserField,
  Department as DepartmentType,
  Team as TeamType,
  WorkingDaysResult,
} from "../graphql/client.generated";

export interface Choice<T> {
  label: string;
  value: T;
}

export interface MemberWithFields {
  id: string;
  hideBirthday?: boolean | null;
  position?: {
    id: string;
  } | null;
  fields?: (
    | IMemberField
    | PhotoField
    | TextField
    | LinkField
    | UserField
    | DateField
    | ManagerField
    | DepartmentField
    | TeamsField
  )[];
}

export interface MemberWithWorkingDays extends MemberWithFields {
  workingDays: WorkingDaysResult | null;
}

export interface Department {
  id: string;
  title: string;
  departmentColor: Color | null;
  departmentManager: string | null;
  isManager: boolean;
}

export interface Team {
  id: string;
  label: string;
  color: Color;
  managerId?: string | null;
  isManager?: boolean;
}

export interface Position {
  id: string;
  title: string;
  member: FlatMember | null;
  managedDepartmentId?: string | null;
}

export interface Photo {
  url72?: string;
  url512?: string;
}

export interface Birthday {
  date: Date;
  hideBirthday: boolean;
}

export interface SlackProfile {
  displayName: string;
  memberId: string;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
interface _BaseFlatMemberDefined {
  id: string;
  positionId: string | null;
  photo: Photo | null;
  name: string | null;
  jobTitle: string | null;
  email: string | null;
  slack: SlackProfile;
  organicePhone: string | null;
  birthday: Birthday | null;
  anniversary: Date | null;
}

export const WITHOUT_MANAGER = "WITHOUT_MANAGER";

export const withoutManagerOption: Choice<"WITHOUT_MANAGER"> = {
  label: "Doesn't have a manager",
  value: WITHOUT_MANAGER,
};
export interface FlatMember extends _BaseFlatMemberDefined {
  [key: string]:
    | string
    | null
    | Photo
    | Date
    | SlackProfile
    | Birthday
    | Position
    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
    | "WITHOUT_MANAGER"
    | Team[]
    | Department
    | FlatMember;
  manager: Position | "WITHOUT_MANAGER" | null;
  teams: Team[];
  department: Department | null;
  country: string | null;
}

export interface Country {
  id: string;
  name: string;
  emoji: string;
}

export const countryList: Country[] = countries.map((x) => ({
  id: x.name,
  name: x.name,
  emoji: x.emoji,
}));

export const countryChoices: Choice<Country>[] = countryList.map((country) => ({
  label: `${country.emoji} ${country.name}`,
  value: {
    id: country.name,
    name: country.name,
    emoji: country.emoji,
  },
}));

export type EditableFlatMemberValues =
  | string
  | null
  | Photo
  | Date
  | SlackProfile
  | Birthday
  | Choice<Country>
  | Choice<FlatMember>
  | Choice<Position | "WITHOUT_MANAGER" | FlatMember>
  | Choice<Department>
  | Choice<Team>[]
  | WorkingDaysResult;

export interface EditableFlatMember extends _BaseFlatMemberDefined {
  [key: string]: EditableFlatMemberValues;
  manager: Choice<Position | "WITHOUT_MANAGER" | FlatMember> | null;
  teams: Choice<Team>[];
  department: Choice<Department> | null;
  country: Choice<Country> | null;
}
export function choiceFromDepartment(
  department: Department
): Choice<Department> {
  return {
    label: department.title,
    value: department,
  };
}

export function choiceFromMember(member: FlatMember): Choice<FlatMember> {
  return {
    label: member.name ?? "",
    value: member,
  };
}

export function choiceFromTeam(team: Team): Choice<Team> {
  return {
    label: team.label,
    value: team,
  };
}

export function choiceFromCountry(country: string): Choice<Country> {
  return countryChoices.find((choice) => choice.value.id === country)!;
}

export function choiceFromManager(
  manager: Position | "WITHOUT_MANAGER"
): Choice<Position | "WITHOUT_MANAGER"> {
  return {
    label:
      manager === "WITHOUT_MANAGER"
        ? "Doesn't have a manager"
        : `${manager.title} ${manager.member?.name ?? ""}`,
    value: manager,
  };
}

function getDepartment(
  department: Partial<DepartmentType>,
  positionId?: string
): Department {
  return {
    id: department.id!,
    title: department.title!,
    departmentColor: department.departmentColor!,
    departmentManager: department.managerId ?? null,
    isManager: !!(department.managerId && department.managerId === positionId),
  };
}

function getTeams(teams?: Partial<TeamType>[], positionId?: string): Team[] {
  if (!teams?.length) {
    return [];
  }

  return teams.reduce<Team[]>(
    (acc, cur) =>
      Object.keys(cur).length
        ? [
            ...acc,
            {
              id: cur.id!,
              label: cur.label!,
              color: cur.color!,
              managerId: cur.managerId!,
              isManager: cur.managerId === positionId,
            },
          ]
        : acc,
    []
  );
}

export function isMemberWithFields(
  member: FlatMember | MemberWithFields
): member is MemberWithFields {
  return "fields" in member;
}

export function isFlatMember(
  value: FlatMember[keyof FlatMember]
): value is FlatMember {
  return value instanceof Object && "positionId" in value;
}

export function isMemberWithWorkingDays(
  member: FlatMember | MemberWithFields | MemberWithWorkingDays
): member is MemberWithWorkingDays {
  return "workingDays" in member;
}

export function flattenEditableMember(
  member: FlatMember | MemberWithFields
): EditableFlatMember {
  const flatMember = isMemberWithFields(member)
    ? flattenMember(member)
    : member;

  const memberFields: Record<string, Choice<FlatMember>> = Object.entries(
    flatMember
  ).reduce((fields, entry) => {
    const [key, field] = entry;

    if (isFlatMember(field)) {
      return {
        ...fields,
        [key]: choiceFromMember(field),
      };
    }

    return fields;
  }, {});

  return {
    ...flatMember,
    manager: flatMember.manager ? choiceFromManager(flatMember.manager) : null,
    teams: flatMember.teams.map(choiceFromTeam),
    department: flatMember.department
      ? choiceFromDepartment(flatMember.department)
      : null,
    country: flatMember.country ? choiceFromCountry(flatMember.country) : null,
    workingDays: isMemberWithWorkingDays(member) ? member.workingDays : null,
    ...memberFields,
  };
}

export function flattenMember(member: MemberWithFields): FlatMember {
  return (member.fields ?? []).reduce<FlatMember>(
    (row, field) => {
      if (!("__typename" in field) || !("key" in field)) {
        return row;
      }

      const { __typename: type } = field;

      if (!type) {
        return row;
      }

      let value;

      if (type === "PhotoField") {
        const { url72, url512 } = field;

        value = {
          url72,
          url512,
        } as Photo;
      }

      if (type === "DepartmentField") {
        const { department } = field;

        if (department) {
          value = getDepartment(department, member.position?.id);
        }
      }

      if (type === "TeamsField") {
        const { teams } = field;

        value = getTeams(teams, member.position?.id);
      }

      if (type === "ManagerField") {
        const { position, withoutManagerManual } = field;

        if (withoutManagerManual) {
          value = "WITHOUT_MANAGER";
        } else if (position) {
          value = {
            id: position.id,
            title: position.title!,
            teams: position.teams,
            department: position.department
              ? getDepartment(position.department)
              : null,
            managedDepartmentId: position.managedDepartmentId,
            member: position.member ? flattenMember(position.member) : null,
          };
        }
      }

      if (type === "DateField") {
        const { date, key } = field;

        if (key === "birthday") {
          value = date
            ? ({
                date,
                hideBirthday: member.hideBirthday,
              } as Birthday)
            : null;
        } else {
          value = date;
        }
      }

      if (type === "TextField") {
        value = field.text;
      }

      if (type === "LinkField") {
        if (field.key === "slack") {
          value = {
            displayName: field.link,
            memberId: member.id,
          } as SlackProfile;
        } else {
          value = field.link;
        }
      }

      if (type === "UserField") {
        const { member: memberFromField } = field;

        if (memberFromField) {
          value = flattenMember(memberFromField);
        }
      }

      return {
        ...row,
        [field.key]: value ?? null,
      };
    },
    {
      id: member.id,
      positionId: member.position?.id ?? null,
      photo: null,
      name: null,
      jobTitle: null,
      email: null,
      slack: {
        displayName: "",
        memberId: member.id,
      },
      organicePhone: null,
      manager: null,
      teams: [],
      department: null,
      birthday: null,
      anniversary: null,
      country: null,
    }
  );
}

export function getCountryFromList(country: string): Country {
  return countryList.find(
    ({ name, emoji }) => name === country || `${emoji} ${name}` === country
  )!;
}
