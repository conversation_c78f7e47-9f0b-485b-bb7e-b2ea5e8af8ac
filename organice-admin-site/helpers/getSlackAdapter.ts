import assert from "assert";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  SlackBotToken,
  <PERSON>only<PERSON><PERSON>,
} from "@organice/core/domain";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaSlackBotQueryHandler from "@organice/slack-bot/prisma/PrismaSlackBotQueryHandler";
import SlackAdapterImpl from "@organice/slack-bot/slack/SlackAdapterImpl";
import { WebClient } from "@slack/web-api";

export function getSlackAdapter(
  slackBotToken: ReadonlyDeep<SlackBotToken>
): SlackAdapter {
  assert(
    process.env.SLACK_ADMIN_SITE_URL,
    "SLACK_ADMIN_SITE_URL env variable should be defined"
  );

  const slackAdapter = new SlackAdapterImpl({
    client: new WebClient(slackBotToken.token, {
      slackApiUrl: process.env.SLACK_API_URL
        ? process.env.SLACK_API_URL
        : undefined,
    }),
    adminSiteUrl: process.env.SLACK_ADMIN_SITE_URL,
    scopes: slackBotToken.scopes,
    queryHandler: new PrismaSlackBotQueryHandler(prismaClient),
  });

  return slackAdapter;
}

export function getSlackAdapterFromClient(client: WebClient): SlackAdapter {
  assert(
    process.env.SLACK_ADMIN_SITE_URL,
    "SLACK_ADMIN_SITE_URL env variable should be defined"
  );

  return new SlackAdapterImpl({
    client,
    adminSiteUrl: process.env.SLACK_ADMIN_SITE_URL,
    queryHandler: new PrismaSlackBotQueryHandler(prismaClient),
  });
}
