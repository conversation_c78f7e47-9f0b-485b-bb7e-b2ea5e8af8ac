import { Logger, LoggerContext } from "@organice/core/domain";
import LoggerImpl from "@organice/core/logger/LoggerImpl";

import { Session } from "../domain";

export function getLogger(context: LoggerContext): Logger {
  return new LoggerImpl(context, {
    sentry: !!process.env.NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE,
    mixpanel: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN
      ? { token: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN }
      : undefined,
  });
}

export function getLoggerFromSession(session: Session): Logger {
  return getLogger({
    member: {
      id: session.memberId,
    },
    workspace: {
      id: session.workspaceId,
    },
  });
}
