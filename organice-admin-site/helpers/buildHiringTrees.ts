import { sortBy } from "lodash";

import { TreeNode, Node, RootNode } from "../components/Tree/types";

import { buildTree } from "./buildTree";

export function isRootNode<TNode extends Node, TRootNode extends RootNode>(
  node: TNode | TRootNode
): node is TRootNode {
  return node.type === "root";
}

export const buildHiringTrees = <
  TPosition extends Node & { isHidden?: boolean },
  TDepartment extends Node,
  TRootNode extends RootNode
>(
  nodes: (TPosition | TDepartment | TRootNode)[]
): {
  root: TPosition | TDepartment | TRootNode;
  tree: TreeNode<TPosition>[];
}[] => {
  const openPositions = nodes
    .filter(
      (node): node is TPosition =>
        node.type === "position" && "member" in node && !node.member
    )
    .filter((node) => !node.isHidden);

  const groupedByParent = openPositions.reduce<Record<string, TPosition[]>>(
    (acc, position) => {
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      if (acc[position.parentId]) {
        acc[position.parentId] = [
          ...acc[position.parentId],
          {
            ...position,
            subordinatesNumber: 0,
            subordinates: [],
          },
        ];
      } else {
        acc[position.parentId] = [
          {
            ...position,
            subordinatesNumber: 0,
            subordinates: [],
          },
        ];
      }

      return acc;
    },
    {}
  );

  const hiringTrees = [];

  // eslint-disable-next-line guard-for-in
  for (const id in groupedByParent) {
    const rootNode = nodes.find((node) => node.id === id)!;
    const subordinates = isRootNode(rootNode)
      ? rootNode.rootSubordinates
      : rootNode.subordinates ?? [];
    /**
     * NOTE: sort nodes in the same order we display them on the main chart
     */
    const subordinatesIds = subordinates.map((sub) => sub.id);
    const sortedNodes = groupedByParent[id].sort((a, b) => {
      return subordinatesIds.indexOf(a.id) - subordinatesIds.indexOf(b.id);
    });

    hiringTrees.push({
      root: rootNode,
      tree: buildTree({
        nodes: sortedNodes,
        depth: 0,
        pathToRoot: [],
      }),
    });
  }

  return sortBy(hiringTrees, (item) => item.root.type !== "root");
};
