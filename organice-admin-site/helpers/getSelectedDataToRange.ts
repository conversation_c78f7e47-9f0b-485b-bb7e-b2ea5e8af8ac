import {
  startOfMonth,
  subMonths,
  subYears,
  endOfMonth,
  startOfYear,
  endOfYear,
} from "date-fns";

import { Choice } from "./flattenMember";

export const DATES_OPTIONS: Choice<string>[] = [
  {
    label: "This month",
    value: "ThisMonth",
  },
  {
    label: "This year",
    value: "ThisYear",
  },
  {
    label: "Last month",
    value: "LastMonth",
  },
  {
    label: "Last year",
    value: "LastYear",
  },
];

export const selectedDateToRange = (
  selectedDate: (typeof DATES_OPTIONS)[number]["value"]
): [Date, Date] => {
  const now = new Date();
  const monthAgo = subMonths(now, 1);
  const yearAgo = subYears(now, 1);

  if (selectedDate === "ThisMonth") {
    return [startOfMonth(now), endOfMonth(now)];
  }

  if (selectedDate === "ThisYear") {
    return [startOfYear(now), endOfYear(now)];
  }

  if (selectedDate === "LastMonth") {
    return [startOfMonth(monthAgo), endOfMonth(monthAgo)];
  }

  if (selectedDate === "LastYear") {
    return [startOfYear(yearAgo), endOfYear(yearAgo)];
  }

  throw new Error(selectedDate);
};
