import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";

import { getSessionWorkspace, InvalidTokenError } from "../domain";

import { getAuthenticator } from "./getAuthenticator";
import { getBaseUrl } from "./getBaseUrl";
import { getLoggerFromSession, getLogger } from "./getLogger";

export async function redirectIfUnauthenticated(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<never> | null> {
  const authenticator = getAuthenticator(ctx.req, ctx.res);
  const session = await authenticator.getSession();

  if (!session) {
    const logger = getLogger({});

    logger.info(`Redirected to the Slack OAuth`, {
      headers: ctx.req.headers,
      url: ctx.req.url,
    });

    return redirectToAuthentication(ctx);
  }

  const repository = new PrismaWorkspaceRepository(prismaClient);
  const logger = getLoggerFromSession(session);

  try {
    await getSessionWorkspace(repository, session);
  } catch (error) {
    if (error instanceof InvalidTokenError) {
      logger.info("Invalid token found when visiting a page");
      authenticator.endSession();

      return redirectToAuthentication(ctx);
    }

    throw error;
  }

  return null;
}

function redirectToAuthentication(
  ctx: GetServerSidePropsContext
): GetServerSidePropsResult<never> {
  const targetUrl = new URL(ctx.req.url ?? "/", getBaseUrl(ctx.req));

  return {
    redirect: {
      destination: targetUrl.searchParams.get("workspace")
        ? generateOpenIdUrl(ctx, targetUrl).toString()
        : generateOAuthUrl(ctx, targetUrl).toString(),
      permanent: false,
    },
  };
}

export function generateOAuthUrl(
  ctx: GetServerSidePropsContext,
  redirectUrl: URL
): URL {
  const oauthUrl = new URL("/api/slack/oauth", getBaseUrl(ctx.req));

  oauthUrl.searchParams.set(
    "redirect_to",
    redirectUrl.pathname + redirectUrl.search
  );

  return oauthUrl;
}

function generateOpenIdUrl(
  ctx: GetServerSidePropsContext,
  redirectUrl: URL
): URL {
  const openIdUrl = new URL("/api/slack/openid", getBaseUrl(ctx.req));

  openIdUrl.searchParams.set(
    "redirect_to",
    redirectUrl.pathname + redirectUrl.search
  );

  return openIdUrl;
}
