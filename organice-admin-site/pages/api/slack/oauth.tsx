import assert from "assert";

import { LoggerContext, NoOpProductOwnerNotifier } from "@organice/core/domain";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaInstallationStore from "@organice/core/prisma/PrismaInstallationStore";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackWebhookProductOwnerNotifier from "@organice/core/slack/SlackWebhookProductOwnerNotifier";
import SlackAppManifest from "@organice/slack-bot/slack/SlackAppManifest";
import { InstallProvider } from "@slack/oauth";
import { NextApiRequest, NextApiResponse } from "next";

import { getBaseUrl } from "../../../helpers/getBaseUrl";
import { getLogger } from "../../../helpers/getLogger";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";

export default async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  assert(
    process.env.ORGANICE_SLACK_CLIENT_ID,
    "ORGANICE_SLACK_CLIENT_ID env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_CLIENT_SECRET,
    "ORGANICE_SLACK_CLIENT_SECRET env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_SIGNING_SECRET,
    "ORGANICE_SLACK_SIGNING_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_STATE_SECRET,
    "SLACK_STATE_SECRET env variable should be defined"
  );

  const loggerContext: LoggerContext = {};
  const logger = getLogger(loggerContext);

  const installationStore = new PrismaInstallationStore({
    clientId: process.env.ORGANICE_SLACK_CLIENT_ID,
    clientSecret: process.env.ORGANICE_SLACK_CLIENT_SECRET,
    logger,
    prismaClient,
    getRepository: () => new PrismaWorkspaceRepository(prismaClient),
    getSlackAdapter,
    getProductOwnerNotifier() {
      return process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
        ? new SlackWebhookProductOwnerNotifier(
            process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
          )
        : new NoOpProductOwnerNotifier();
    },
  });

  const installProvider = new InstallProvider({
    directInstall: true,
    clientId: process.env.ORGANICE_SLACK_CLIENT_ID,
    clientSecret: process.env.ORGANICE_SLACK_CLIENT_SECRET,
    stateSecret: process.env.SLACK_STATE_SECRET,
    authorizationUrl: process.env.SLACK_AUTHORIZATION_URL
      ? process.env.SLACK_AUTHORIZATION_URL
      : undefined,
    legacyStateVerification: process.env.NODE_ENV !== "production",
    clientOptions: {
      slackApiUrl: process.env.SLACK_API_URL
        ? process.env.SLACK_API_URL
        : undefined,
    },
    logger,
    installationStore,
  });

  const callbackUri = new URL("/api/slack/oauth_redirect", getBaseUrl(req));

  callbackUri.searchParams.set(
    "redirect_to",
    (req.query.redirect_to as string | undefined) ?? "/"
  );

  assert(
    callbackUri.searchParams.get("redirect_to")?.startsWith("/"),
    "Open redirect is not allowed"
  );

  await installProvider.handleInstallPath(req, res, undefined, {
    scopes: SlackAppManifest.botScopes,
    userScopes: SlackAppManifest.userScopes,
    teamId: req.query.workspace_id as string | undefined,
    redirectUri: callbackUri.toString(),
  });
};
