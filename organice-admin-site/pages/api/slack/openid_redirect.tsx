import assert from "assert";

import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackOpenIdAuthenticator from "@organice/slack-bot/slack/SlackOpenIdAuthenticator";
import { NextApiRequest, NextApiResponse } from "next";

import { Session } from "../../../domain";
import { getAuthenticator } from "../../../helpers/getAuthenticator";
import { getBaseUrl } from "../../../helpers/getBaseUrl";
import { getLogger } from "../../../helpers/getLogger";

export default async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  assert(
    process.env.ORGANICE_SLACK_CLIENT_ID,
    "ORGANICE_SLACK_CLIENT_ID env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_CLIENT_SECRET,
    "ORGANICE_SLACK_CLIENT_SECRET env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_SIGNING_SECRET,
    "ORGANICE_SLACK_SIGNING_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_STATE_SECRET,
    "SLACK_STATE_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_ADMIN_SITE_URL,
    "SLACK_ADMIN_SITE_URL env variable should be defined"
  );
  const openIdAuthenticator = new SlackOpenIdAuthenticator({
    clientId: process.env.ORGANICE_SLACK_CLIENT_ID,
    clientSecret: process.env.ORGANICE_SLACK_CLIENT_SECRET,
    signingSecret: process.env.ORGANICE_SLACK_SIGNING_SECRET,
    stateSecret: process.env.SLACK_STATE_SECRET,
    adminSiteUrl: process.env.SLACK_ADMIN_SITE_URL,
    slackApiUrl: process.env.SLACK_API_URL
      ? process.env.SLACK_API_URL
      : undefined,

    getRepository: () => new PrismaWorkspaceRepository(prismaClient),
    getLogger,
  });

  const redirectUri = new URL("/api/slack/openid_redirect", getBaseUrl(req));

  redirectUri.searchParams.set("redirect_to", req.query.redirect_to as string);

  await openIdAuthenticator.handleCallback(
    req,
    res,
    {
      success: (authentication) => {
        const session: Session = {
          userToken: authentication.user.token,
          workspaceId: authentication.team.id,
          memberId: authentication.user.id,
        };
        const authenticator = getAuthenticator(req, res);

        authenticator.setSession(session);

        res.redirect((req.query.redirect_to as string | undefined) ?? "/");
      },

      failure: () => {
        res.writeHead(500, { "Content-Type": "text/html; charset=utf-8" });
        res.end(
          `<html>
            <body>
              <h1>Oops, Something Went Wrong!</h1>
              <p>Please try again or <a href="https://calendly.com/andrew-fan/what-is-organice">contact us</a>.</p>
            </body>
          </html>`
        );
      },
    },
    {
      redirectUri: redirectUri.toString(),
    }
  );
};
