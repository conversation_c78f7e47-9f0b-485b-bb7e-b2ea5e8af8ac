import assert from "node:assert";

import {
  WorkspaceRepository,
  NoOpProductOwnerNotifier,
  ProductOwnerNotifier,
} from "@organice/core/domain";
import { ActivityLog } from "@organice/core/domain/activityLog";
import {
  <PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>,
  KudosRepository,
} from "@organice/core/domain/kudos";
import LoggerImpl from "@organice/core/logger/LoggerImpl";
import PrismaActivityLog from "@organice/core/prisma/PrismaActivityLog";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaInstallationStore from "@organice/core/prisma/PrismaInstallationStore";
import PrismaKudosQueryHandler from "@organice/core/prisma/PrismaKudosQueryHandler";
import PrismaKudosRepository from "@organice/core/prisma/PrismaKudosRepository";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackWebhookProductOwnerNotifier from "@organice/core/slack/SlackWebhookProductOwnerNotifier";
import PrismaSlackBotQueryHandler from "@organice/slack-bot/prisma/PrismaSlackBotQueryHandler";
import SlackAppManifest from "@organice/slack-bot/slack/SlackAppManifest";
import SlackBot from "@organice/slack-bot/slack/SlackBot";
import SlackBotQueryHandler from "@organice/slack-bot/slack/SlackBotQueryHandler";
import { loggerFromBoltContextAndBody } from "@organice/slack-bot/slack/components/_common";
import {
  AllMiddlewareArgs,
  AnyMiddlewareArgs,
  BufferedIncomingMessage,
  HTTPReceiver,
} from "@slack/bolt";
import { NextApiRequest, NextApiResponse } from "next";

import {
  getSlackAdapter,
  getSlackAdapterFromClient,
} from "../../../helpers/getSlackAdapter";

assert(
  process.env.ORGANICE_SLACK_CLIENT_ID,
  "ORGANICE_SLACK_CLIENT_ID env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_CLIENT_SECRET,
  "ORGANICE_SLACK_CLIENT_SECRET env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_SIGNING_SECRET,
  "ORGANICE_SLACK_SIGNING_SECRET env variable should be defined"
);
assert(
  process.env.SLACK_STATE_SECRET,
  "SLACK_STATE_SECRET env variable should be defined"
);
assert(
  process.env.SLACK_ADMIN_SITE_URL,
  "SLACK_ADMIN_SITE_URL env variable should be defined"
);

function getRepository(): WorkspaceRepository {
  return new PrismaWorkspaceRepository(prismaClient);
}

function getProductOwnerNotifier(): ProductOwnerNotifier {
  return process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
    ? new SlackWebhookProductOwnerNotifier(
        process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
      )
    : new NoOpProductOwnerNotifier();
}

function getTime(): Date {
  return new Date();
}

function getActivityLog(): ActivityLog {
  return new PrismaActivityLog(prismaClient);
}

function getSlackBotQueryHandler(): SlackBotQueryHandler {
  return new PrismaSlackBotQueryHandler(prismaClient);
}

function getKudosQueryHandler(): KudosQueryHandler {
  return new PrismaKudosQueryHandler(prismaClient);
}

function getKudosRepository(): KudosRepository {
  return new PrismaKudosRepository(prismaClient);
}

const clientId = process.env.ORGANICE_SLACK_CLIENT_ID;
const clientSecret = process.env.ORGANICE_SLACK_CLIENT_SECRET;
const signingSecret = process.env.ORGANICE_SLACK_SIGNING_SECRET;
const stateSecret = process.env.SLACK_STATE_SECRET;
const adminSiteUrl = process.env.SLACK_ADMIN_SITE_URL;
const slackApiUrl = process.env.SLACK_API_URL
  ? process.env.SLACK_API_URL
  : undefined;

const logger = new LoggerImpl(
  {},
  {
    sentry: !!process.env.NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE,
    mixpanel: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN
      ? { token: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN }
      : undefined,
  }
);

const installationStore = new PrismaInstallationStore({
  clientId,
  clientSecret,
  logger,
  prismaClient,
  getRepository,
  getSlackAdapter,
  getProductOwnerNotifier,
});

const requestToMiddlewareArgsMap = new WeakMap<
  BufferedIncomingMessage,
  AnyMiddlewareArgs & AllMiddlewareArgs
>();

const receiver = new HTTPReceiver({
  endpoints: ["/slack/events"],
  clientId,
  clientSecret,
  signingSecret,
  stateSecret,
  scopes: SlackAppManifest.botScopes,
  installerOptions: {
    userScopes: SlackAppManifest.userScopes,
  },
  installationStore,
  logger,
  customPropertiesExtractor(request) {
    return {
      provideContextForUnhandledRequestHandler(
        args: AnyMiddlewareArgs & AllMiddlewareArgs
      ) {
        requestToMiddlewareArgsMap.set(request, args);
      },
    };
  },
  unhandledRequestHandler(shittyArgs) {
    const args = requestToMiddlewareArgsMap.get(
      // It's guaranteed to be BufferedIncomingMessage — I looked at the sources!
      shittyArgs.request as BufferedIncomingMessage
    );

    if (args && "action" in args) {
      const logger1 = loggerFromBoltContextAndBody(args.context, args.body);

      logger1.error(
        `An incoming action ${
          // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
          "action_id" in args.action
            ? // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              `"${args.action.action_id}"`
            : args.action.type === "dialog_submission"
            ? `with "${args.action.type}" type with "${JSON.stringify(
                args.action.submission
              )}" submission args`
            : args.action.type === "button" || args.action.type === "select"
            ? `with "${args.action.type}" with "${args.action.name}" name`
            : `with "${args.action.type}" type`
        } was not acknowledged within 3 seconds`
      );
    } else if (args && "command" in args) {
      const logger1 = loggerFromBoltContextAndBody(args.context, args.body);

      logger1.error(
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-unsafe-member-access
        `An incoming command "${args.command.command}" was not acknowledged within 3 seconds`
      );
    } else if (args && "event" in args) {
      const logger1 = loggerFromBoltContextAndBody(args.context, args.body);

      logger1.error(
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-unsafe-member-access
        `An incoming event "${args.event.type}" was not acknowledged within 3 seconds`
      );
    } else if (args && "shortcut" in args) {
      const logger1 = loggerFromBoltContextAndBody(args.context, args.body);

      logger1.error(
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-unsafe-member-access
        `An incoming shortcut "${args.shortcut.callback_id}" was not acknowledged within 3 seconds`
      );
    } else if (args && "view" in args) {
      const logger1 = loggerFromBoltContextAndBody(args.context, args.body);

      logger1.error(
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-unsafe-member-access
        `An incoming view callback for ${args.view.type} "${
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          (args.view.callback_id || null) ?? args.view.title?.text
        }" was not acknowledged within 3 seconds`
      );
    } else {
      shittyArgs.logger.error(
        `An incoming something was not acknowledged within 3 seconds`
      );
    }
  },
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const slackBot = new SlackBot({
  clientId,
  clientSecret,
  signingSecret,
  stateSecret,
  slackApiUrl,
  logger,
  adminSiteUrl,

  installationStore,
  receiver,
  getActivityLog,
  getRepository,
  getSlackAdapter: getSlackAdapterFromClient,
  getProductOwnerNotifier,
  getKudosQueryHandler,
  getSlackBotQueryHandler,
  getKudosRepository,
  getTime,
});

export default (req: NextApiRequest, res: NextApiResponse): void => {
  receiver.requestListener(req, res);
};

export const config = {
  api: {
    bodyParser: false,
    externalResolver: true,
  },
};
