// eslint-disable-next-line max-classes-per-file
import {
  Workspace as DomainWorkspace,
  ReadonlyDeep,
  WorkspaceRepository,
} from "@organice/core/domain";

export interface Session {
  memberId: string;
  workspaceId: string;
  userToken: string;
  workspace?: ReadonlyDeep<DomainWorkspace>;
}

export class InvalidTokenError extends Error {}

export class TokenPointsToDeletedWorkspaceError extends InvalidTokenError {
  constructor() {
    super("Token points to the deleted workspace");
  }
}

export class TokenRevokedError extends InvalidTokenError {
  constructor() {
    super("Token has been revoked");
  }
}

export class InvalidFeedError extends Error {
  constructor() {
    super("Feed does not exists");
  }
}

export async function getSessionWorkspace(
  repository: WorkspaceRepository,
  session: Session
): Promise<ReadonlyDeep<DomainWorkspace>> {
  /**
   * if used from graphql resolvers, session.workspace will be already set
   * In our current architecture every query has access to all workspace data
   * and each query triggers a new request
   * to improve the performance we can get workspace data once before Apollo
   * starts resolving queries and mutations and put this data in the session
   */
  if (session.workspace) {
    return session.workspace;
  }

  const workspace = await repository.getWorkspace(session.workspaceId);

  if (!workspace) {
    throw new TokenPointsToDeletedWorkspaceError();
  }

  if (
    workspace.slackMemberTokens[session.memberId]?.token !== session.userToken
  ) {
    throw new TokenRevokedError();
  }

  return workspace;
}
