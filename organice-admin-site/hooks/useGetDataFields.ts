import { useMemo } from "react";

interface Field {
  id: string;
  label: string;
  type: string;
}

const EXCLUDED_FIELD_TYPES = [
  "manager",
  "jobTitle",
  "department",
  "teams",
  "photo",
];

export const useGetDataFields = (fields: Field[] = []): Field[] => {
  const filteredFields = useMemo(() => {
    return fields.filter((field) => !EXCLUDED_FIELD_TYPES.includes(field.id));
  }, [fields]);

  return filteredFields;
};
