import { useFragment } from "@apollo/client";
import { useCallback, useEffect, useState, useRef } from "react";

import { SidebarTab } from "../components/Sidebar/MemberSidebar/EditableMemberSidebar";
import { useActiveSidebarContext } from "../context/ActiveSidebarContext";
import { useSessionContext } from "../context/SessionContext";
import {
  PositioNodeFragment,
  PositioNodeFragmentDoc,
  usePositionMemberLookupLazyQuery,
  usePositionSidebarLazyQuery,
} from "../graphql/client.generated";

import { useFlattenEditablePosition } from "./useFlattenPosition";
import { useGetHiringManager } from "./useGetHiringManager";
import { useRemoveNodeFromSidebar } from "./useRemoveNodeFromSidebar";

interface SidebarData {
  id: string;
  mode: "edit" | "read";
  setExpandedIds?: (ids: string[]) => void;
  onOpen?: () => void;
  onClose?: () => void;
  showGoToOrgChartBtn: boolean;
}

export const usePositionSidebar = (): {
  openSidebar: (data: SidebarData) => void;
  closeSidebar: () => void;
} => {
  const { activeSidebar, openSidebar, closeSidebar } =
    useActiveSidebarContext();
  const [lookUpPosition] = usePositionMemberLookupLazyQuery();
  const [getPosition, { refetch: refetchPosition }] =
    usePositionSidebarLazyQuery();
  const { session } = useSessionContext();
  const getHiringManager = useGetHiringManager();
  const flattenEditablePosition = useFlattenEditablePosition(getHiringManager);

  const [sidebar, setSidebar] = useState<SidebarData | null>(null);
  const openPositionSidebar = useCallback((data: SidebarData) => {
    setSidebar(data);
  }, []);
  const closePositionSidebar = useCallback(() => {
    setSidebar(null);
  }, []);
  const sidebarIsOpening = useRef(false);
  const removeNodeHandler = useRemoveNodeFromSidebar();

  const { data: cachedPosition, missing } = useFragment<PositioNodeFragment>({
    fragment: PositioNodeFragmentDoc,
    fragmentName: "PositioNode",
    /**
     * NOTE: skiping optimistic updates is important
     * to support a case when an user assigns a member to a position
     */
    optimistic: false,
    from: {
      __typename: "Position",
      id: sidebar?.id ?? "NOT_FOUND",
    },
  });

  if (sidebar && missing) {
    void lookUpPosition({
      variables: {
        positionId: sidebar.id,
      },
    });
  }

  useEffect(() => {
    const positionId = cachedPosition.id;

    if (
      !sidebar ||
      !positionId ||
      cachedPosition.member?.id ||
      (activeSidebar?.type === "position" && activeSidebar.id === positionId)
    ) {
      return;
    }

    sidebarIsOpening.current = true;

    openSidebar({
      type: "position",
      id: positionId,
      mode: sidebar.mode,
      setExpandedIds: sidebar.setExpandedIds,
      showGoToOrgChartBtn: sidebar.showGoToOrgChartBtn,
      removeNodeHandler: () => {
        return removeNodeHandler(positionId);
      },
      onOpen: () => {
        sidebarIsOpening.current = false;

        if (sidebar.onOpen) {
          sidebar.onOpen();
        }
      },
      onClose: () => {
        if (sidebarIsOpening.current) {
          return;
        }

        if (sidebar.onClose) {
          sidebar.onClose();
        }
        closePositionSidebar();
      },
    });
  }, [
    cachedPosition.id,
    cachedPosition.member?.id,
    activeSidebar,
    openSidebar,
    sidebar,
    flattenEditablePosition,
    getPosition,
    refetchPosition,
    removeNodeHandler,
    closePositionSidebar,
  ]);

  useEffect(() => {
    const memberId = cachedPosition.member?.id;
    const positionId = cachedPosition.id;

    if (
      !positionId ||
      !sidebar ||
      !memberId ||
      (activeSidebar?.type === "member" && activeSidebar.id === memberId)
    ) {
      return;
    }

    sidebarIsOpening.current = true;

    const memberSidebarTabs: SidebarTab[] = [
      {
        id: "profile",
        isActive: true,
      },
    ];

    if (session?.me.isAdmin) {
      memberSidebarTabs.push({
        id: "timeOffs",
      });
      memberSidebarTabs.push({
        id: "activityLog",
      });
    }

    openSidebar({
      type: "member",
      id: memberId,
      positionId,
      tabs: memberSidebarTabs,
      mode: sidebar.mode,
      setExpandedIds: sidebar.setExpandedIds,
      showGoToOrgChartBtn: sidebar.showGoToOrgChartBtn,
      onOpen: () => {
        sidebarIsOpening.current = false;

        if (sidebar.onOpen) {
          sidebar.onOpen();
        }
      },
      onClose: () => {
        if (sidebarIsOpening.current) {
          return;
        }

        if (sidebar.onClose) {
          sidebar.onClose();
        }
        closePositionSidebar();
      },
      removeNodeHandler: () => {
        return removeNodeHandler(positionId);
      },
    });
  }, [
    sidebar,
    closePositionSidebar,
    cachedPosition,
    activeSidebar,
    openSidebar,
    removeNodeHandler,
    session,
  ]);

  useEffect(() => {
    if (
      !sidebar &&
      activeSidebar &&
      ["member", "position"].includes(activeSidebar.type)
    ) {
      closeSidebar(activeSidebar);
    }
  }, [activeSidebar, closeSidebar, sidebar]);

  return {
    openSidebar: openPositionSidebar,
    closeSidebar: closePositionSidebar,
  };
};
