import {
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  offset,
  flip,
  shift,
  autoUpdate,
  FloatingPortal,
  useClick,
  FloatingArrow,
  arrow,
  ComputePositionConfig,
} from "@floating-ui/react";
import cn from "classnames";
import { FC, ReactElement, useRef, useState, useCallback } from "react";

export interface Menu {
  label: string | ReactElement;
  id: string;
  onClick: () => void;
  type?: "danger";
}

interface DropdownMenuProps {
  className?: string;
  children: JSX.Element | string | FC<{ isMenuOpen: boolean }>;
  menu?: (Menu | undefined)[];
  renderMenu?: FC<{ hide: () => void }>;
  variant?: "link-menu" | "button-menu";
  placement?: ComputePositionConfig["placement"];
  hideArrow?: boolean;
  onClose?: () => void;
  registerDropdownRef?: (node: HTMLDivElement | null) => void;
}

const DropdownMenu: FC<DropdownMenuProps> = ({
  className,
  children,
  menu,
  renderMenu,
  placement = "bottom",
  variant = "button-menu",
  onClose,
  registerDropdownRef,
  hideArrow,
  ...props
}) => {
  const [visible, setVisible] = useState(false);
  const arrowRef = useRef(null);

  const { refs, floatingStyles, context } = useFloating({
    open: visible,
    onOpenChange: setVisible,
    strategy: "fixed",
    placement,
    middleware: [
      offset(hideArrow ? 4 : 12),
      flip({
        padding: 9,
      }),
      shift({ padding: 9 }),
      arrow({
        element: arrowRef,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const setDropdownRefs = useCallback(
    (node: HTMLDivElement | null) => {
      if (registerDropdownRef) {
        registerDropdownRef(node);
      }

      refs.setFloating(node);
    },
    [registerDropdownRef, refs]
  );

  const click = useClick(context);
  const dismiss = useDismiss(context, {
    outsidePress: true,
  });
  const role = useRole(context, { role: "menu" });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
    role,
  ]);

  const hide = (): void => {
    setVisible(false);
    onClose?.();
  };

  return (
    <>
      <div
        className="relative w-fit"
        ref={refs.setReference}
        {...getReferenceProps()}
      >
        <div {...props} className="cursor-pointer">
          {typeof children === "function"
            ? children({ isMenuOpen: visible })
            : children}
        </div>
      </div>

      {visible ? (
        <FloatingPortal>
          <div
            ref={setDropdownRefs}
            style={floatingStyles}
            {...getFloatingProps()}
            className={cn(
              `z-[99999] min-w-[150px] rounded-md border border-gray-300 bg-white py-2 text-sm shadow-primary`,

              className
            )}
            data-test="dropdown-menu"
            data-prevent-sidebar-close
          >
            {!hideArrow ? (
              <FloatingArrow
                ref={arrowRef}
                context={context}
                width={15}
                height={10}
                fill="white"
                stroke="#CBD5E1"
                strokeWidth={1}
              />
            ) : null}

            {renderMenu?.({ hide })}
            {!renderMenu &&
              menu?.reduce<JSX.Element[]>((acc, item) => {
                if (item) {
                  const { id, onClick, type, label } = item;

                  acc.push(
                    <div
                      data-test={`dropdown-item-${item.id}`}
                      key={id}
                      role="button"
                      tabIndex={0}
                      onClick={(e) => {
                        e.stopPropagation();
                        onClick();
                        setVisible(false);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          onClick();
                          setVisible(false);
                        }
                      }}
                      className="group cursor-pointer pl-1 pr-1 first:pt-1 last:pb-1"
                    >
                      <div
                        className={cn(
                          "max-w-xs truncate rounded-md px-3 py-2 ",
                          {
                            "hover:bg-main-100": variant === "button-menu",
                          }
                        )}
                        key={id}
                      >
                        <span
                          className={cn("whitespace-nowrap text-sm ", {
                            "text-red-500": type === "danger",
                            "text-gray-900": type !== "danger",
                            "group-hover:text-violet-600":
                              variant === "link-menu",
                          })}
                        >
                          {label}
                        </span>
                      </div>
                    </div>
                  );
                }

                return acc;
              }, [])}
          </div>
        </FloatingPortal>
      ) : null}
    </>
  );
};

export default DropdownMenu;
