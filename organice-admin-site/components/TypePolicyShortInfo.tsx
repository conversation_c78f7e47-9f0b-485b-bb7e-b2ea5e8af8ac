import {
  getDaysAndHoursInfo,
  balanceByEOY,
  balanceForYear,
} from "@organice/core/domain/time-offs";
import formatDate from "@organice/core/utils/formatDate";
import { FC } from "react";

import { AccrualsFrequency, DayOfWeek } from "../graphql/client.generated";

import { dayOfWeekLabels } from "./Settings/TimeOffs/consts";

export interface TypePolicyData {
  rollOverToNextYear: boolean;
  onStartQuota: number;
  maxCapacity: number;
  accrualsQuota: number;
  accuralsFrequency: AccrualsFrequency | null;
}

interface Props {
  typePolicy: TypePolicyData;
  policy: {
    workDays: DayOfWeek[];
    includedWeekendDays: DayOfWeek[];
  };
  nextResetAt: Date | null;
}

const TypePolicyShortInfo: FC<Props> = ({
  typePolicy,
  policy,
  nextResetAt,
}) => {
  const getNow = (): Date => new Date();
  const {
    accrualsQuota,
    accuralsFrequency,
    maxCapacity,
    onStartQuota,
    rollOverToNextYear,
  } = typePolicy;
  const { workDays, includedWeekendDays } = policy;

  return (
    <>
      {onStartQuota ? (
        <p>
          • <strong>{getDaysAndHoursInfo(onStartQuota)}</strong> on hire and
          every PTO year start.
        </p>
      ) : null}
      {workDays.length > 0 ? (
        <p>
          {`• ${workDays
            .map((dayOfWeek) => dayOfWeekLabels[dayOfWeek])
            .join(", ")} ${
            workDays.length > 1 ? "are working days" : "is working day"
          }`}
        </p>
      ) : null}
      {includedWeekendDays.length > 0 ? (
        <p>
          {`• ${includedWeekendDays
            .map((dayOfWeek) => dayOfWeekLabels[dayOfWeek])
            .join(", ")} ${
            includedWeekendDays.length > 1 ? "are" : "is"
          } included in the calculated time off duration`}
        </p>
      ) : null}
      {accrualsQuota && accuralsFrequency ? (
        <>
          <p>
            • Accrue <strong>{getDaysAndHoursInfo(accrualsQuota)}</strong> every{" "}
            {accuralsFrequency === AccrualsFrequency.Week ? "week" : "month"},
            or{" "}
            <strong>
              {getDaysAndHoursInfo(
                balanceForYear(accrualsQuota, accuralsFrequency)
              )}
            </strong>{" "}
            per working year
          </p>
          <p>
            • By the end of the calendar year{" "}
            <strong>
              {getDaysAndHoursInfo(
                balanceByEOY(accrualsQuota, accuralsFrequency, getNow)
              )}
            </strong>{" "}
            will be available
          </p>
        </>
      ) : null}

      {maxCapacity ? (
        <p>
          • Max capacity is <strong>{getDaysAndHoursInfo(maxCapacity)}</strong>
        </p>
      ) : null}

      <p>
        •{" "}
        {rollOverToNextYear
          ? "Rollovers to the next PTO year"
          : nextResetAt
          ? `Will reset on ${formatDate(nextResetAt)}`
          : "Resets at the end of the current PTO year"}
      </p>
    </>
  );
};

export default TypePolicyShortInfo;
