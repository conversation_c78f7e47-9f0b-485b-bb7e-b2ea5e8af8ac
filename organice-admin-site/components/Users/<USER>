import { CellProps } from "react-datasheet-grid";

import { EditableFlatMember } from "../../helpers/flattenMember";

import UserCell from "./UserCell";

interface ColumnOptions<RowData> {
  hasSidebar: (row: RowData) => boolean;
  openSidebar?: (row: RowData) => void;
  isActive: (row: RowData) => boolean;
  sidebarTooltip?: string;
}

const StickyColumn = <RowData extends EditableFlatMember>({
  rowData,
  columnData,
}: CellProps<RowData, ColumnOptions<RowData>>): JSX.Element => {
  const hasSidebar = columnData.hasSidebar(rowData);
  const isActive = columnData.isActive(rowData);
  const message = columnData.sidebarTooltip ?? "Click to view details";

  return (
    <UserCell
      name={rowData.name ?? ""}
      photoUrl={rowData.photo?.url72 ?? ""}
      showSidebarButton={hasSidebar}
      isSidebarActive={isActive}
      sidebarTooltip={message}
      onSidebarButtonClick={
        columnData.openSidebar
          ? () => columnData.openSidebar?.(rowData)
          : undefined
      }
      className="px-2"
    />
  );
};

export default StickyColumn;
