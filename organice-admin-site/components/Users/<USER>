import cn from "classnames";
import { AltArrowRight } from "solar-icon-set";

import Avatar from "../Avatar";
import Tooltip from "../Tooltip";

export interface UserCellProps {
  name: string;
  photoUrl?: string;
  showSidebarButton?: boolean;
  isSidebarActive?: boolean;
  sidebarTooltip?: string;
  onSidebarButtonClick?: () => void;
  className?: string;
}

const UserCell = ({
  name,
  photoUrl = "",
  showSidebarButton = false,
  isSidebarActive = false,
  sidebarTooltip = "Click to view details",
  onSidebarButtonClick,
  className,
}: UserCellProps): JSX.Element => {
  return (
    <div className={cn("flex w-full items-center gap-2", className)}>
      <Avatar size="s" photoUrl={photoUrl} className="mx-auto" />
      <div className="flex-1 truncate font-semibold text-black" title={name}>
        {name}
      </div>
      {showSidebarButton ? (
        <Tooltip message={sidebarTooltip}>
          <div
            data-test="cell-open-sidebar-button"
            role="button"
            tabIndex={0}
            className={cn(
              "flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded",
              {
                "bg-violet-500 text-white": isSidebarActive,
                "hover:bg-slate-200": !isSidebarActive,
              }
            )}
            onClick={onSidebarButtonClick}
            onKeyDown={(e) => e.stopPropagation()}
          >
            <AltArrowRight />
          </div>
        </Tooltip>
      ) : null}
    </div>
  );
};

export default UserCell;
