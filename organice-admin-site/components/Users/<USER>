import { FC, useCallback, MouseEvent, KeyboardEvent, ReactNode } from "react";
import { ArrowDown, ArrowUp } from "solar-icon-set";

export interface TitleCellProps {
  title: ReactNode;
  field: string;
  sortValue: string;
  sort: (sort: string) => void;
}

const TitleCell: FC<TitleCellProps> = ({
  title,
  field,
  sortValue,
  sort,
}): JSX.Element => {
  const [sortField, sortDirection] = sortValue.split(":");
  const isSortedByField = sortField === field;

  const changeSort = useCallback(
    (e: MouseEvent | KeyboardEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (sortDirection === "asc") {
        sort(`${field}:desc`);
      } else {
        sort(`${field}:asc`);
      }
    },
    [sortDirection, field, sort]
  );

  return (
    <div
      data-test={`title-cell-${field}`}
      role="button"
      tabIndex={0}
      onClick={changeSort}
      onMouseDown={(e) => {
        e.stopPropagation();
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          changeSort(e);
        }
      }}
      className="group flex w-full items-center gap-2 hover:text-black"
    >
      <div className="truncate">{title}</div>
      {isSortedByField ? (
        sortDirection === "asc" ? (
          <ArrowUp />
        ) : (
          sortDirection === "desc" && <ArrowDown />
        )
      ) : (
        <div className="opacity-0 group-hover:opacity-100">
          <ArrowUp />
        </div>
      )}
    </div>
  );
};

export default TitleCell;
