import cn from "classnames";
import addMonths from "date-fns/addMonths";
import {
  DetailedHTMLProps,
  FC,
  HTMLAttributes,
  useCallback,
  useMemo,
} from "react";

import {
  UpcomingGroupBy,
  useUpcomingEventsQuery,
} from "../../graphql/client.generated";
import { useLocalStorageForRender } from "../../hooks/useLocalStorage";
import EmptyUpcoming from "../../public/svg/hometab/emptyUpcoming.svg";
import EmptyPageState from "../EmptyPageState";
import { MultiSelectFilterDropdown, Option } from "../FilterDropdown";
import SkeletonEvents from "../SkeletonEvents";
import { Tab } from "../Tab";
import Tooltip from "../Tooltip";

import {
  UpcompingEvent,
  isHolidayEvent,
  isTimeOffEvent,
  isCelebrationEvent,
} from "./UpcompingEvent";
import { UpcomingEvent } from "./types";

export interface UpcomingFeatureProps
  extends DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  itemsBlockClass?: string;
}

const Types = [
  {
    id: "all",
    title: "All",
    subtitle: "All Upcoming Events",
  },
  {
    id: "timeOff",
    title: "Time Offs",
    subtitle: "Upcoming Time Offs",
  },
  {
    id: "holiday",
    title: "Holidays",
    subtitle: "Upcoming Holidays",
  },
  {
    id: "celebration",
    title: "Celebrations",
    subtitle: "Upcoming Celebrations",
  },
];

const today = new Date();

function filterEvents(
  events: UpcomingEvent[],
  type: (typeof Types)[number]["id"],
  selectedFilters: string[]
): UpcomingEvent[] {
  return events
    .map((dayEvents) => ({
      ...dayEvents,
      events: dayEvents.events.filter(({ event }) => {
        if (type === Types[1].id && !isTimeOffEvent(event)) {
          return false;
        }

        if (type === Types[2].id && !isHolidayEvent(event)) {
          return false;
        }

        if (type === Types[3].id && !isCelebrationEvent(event)) {
          return false;
        }

        return (
          !selectedFilters.length || selectedFilters.includes(event.type.id)
        );
      }),
    }))
    .filter((dayEvents) => dayEvents.events.length);
}

const UpcomingEvents: FC<UpcomingFeatureProps> = ({
  itemsBlockClass,
  className,
  ...props
}) => {
  const [localState, setLocalState] = useLocalStorageForRender<string>(
    "upcoming-events",
    Types[0].id
  );

  const eventsType = useMemo(() => {
    const [type] = localState.split(":");

    return type;
  }, [localState]);

  const activeFilters = useMemo(() => {
    const [, filters] = localState.split(":");

    return filters ? filters.split(",") : [];
  }, [localState]);

  const activeSubtitle = useMemo(() => {
    return (
      Types.find(({ id }) => id === eventsType)?.subtitle ?? Types[0].subtitle
    );
  }, [eventsType]);

  const { data, loading } = useUpcomingEventsQuery({
    variables: {
      to: addMonths(today, 3),
      groupBy: UpcomingGroupBy.Day,
    },
  });

  const options = useMemo(() => {
    if (!data) {
      return [];
    }

    const { timeOffTypes, holidayTypes, celebrationTypes } = data;

    const result = [];

    if (eventsType === Types[1].id) {
      result.push(...timeOffTypes);
    } else if (eventsType === Types[2].id) {
      result.push(...holidayTypes);
    } else if (eventsType === Types[3].id) {
      result.push(...celebrationTypes);
    } else {
      result.push(...timeOffTypes, ...celebrationTypes, ...holidayTypes);
    }

    return result.map(({ id, label, emoji }) => ({
      value: id,
      label: (
        <>
          <span className="mr-2">{emoji}</span>
          <span>{label}</span>
        </>
      ),
    }));
  }, [eventsType, data]);

  const activeOptions = useMemo(
    () => options.filter(({ value }) => activeFilters.includes(value)),
    [options, activeFilters]
  );

  const filteredEvents = useMemo(
    () => filterEvents(data?.upcomingEvents ?? [], eventsType, activeFilters),
    [data, eventsType, activeFilters]
  );

  const totalEvents = useMemo(() => {
    return filteredEvents.reduce((acc, { events }) => acc + events.length, 0);
  }, [filteredEvents]);

  const updateFilter = useCallback(
    (selectedOptions: Option[]): void => {
      if (selectedOptions.length) {
        setLocalState(
          `${eventsType}:${selectedOptions.map(({ value }) => value).join(",")}`
        );
      } else {
        setLocalState(eventsType);
      }
    },
    [eventsType, setLocalState]
  );

  return (
    <div
      className={cn(
        "flex min-h-[520px] w-[440px] flex-col overflow-hidden rounded-xl border border-main-300 bg-white",
        className
      )}
      {...props}
    >
      <div className="no-scrollbar flex items-center gap-2 overflow-x-auto overflow-y-hidden border-b px-5 py-3 text-sm">
        {Types.map((t) => (
          <Tooltip key={t.id} message={t.subtitle}>
            <Tab
              isActive={() => eventsType === t.id}
              onClick={() => {
                if (eventsType !== t.id) {
                  setLocalState(t.id);
                }
              }}
            >
              {t.title}
            </Tab>
          </Tooltip>
        ))}
      </div>
      <div
        className={cn(
          "flex h-full w-full flex-col overflow-hidden",
          itemsBlockClass
        )}
      >
        <div className="mb-4 flex flex-wrap items-center justify-between gap-2 px-5 pt-4">
          <span className="flex items-center font-semibold">
            {activeSubtitle}
            {totalEvents ? ` (${totalEvents})` : null}
          </span>

          <MultiSelectFilterDropdown
            title="Filters"
            options={options}
            selectedOptions={activeOptions}
            onChange={updateFilter}
          />
        </div>
        <div className="overflow-y-auto pb-4 text-sm">
          {loading ? (
            <SkeletonEvents className="px-5" />
          ) : filteredEvents.length ? (
            <>
              {filteredEvents.map((upcoming) => (
                <UpcompingEvent key={upcoming.date.getTime()} {...upcoming} />
              ))}
            </>
          ) : (
            <EmptyPageState emptyStateIcon={<EmptyUpcoming />}>
              You don&apos;t have any events yet.
            </EmptyPageState>
          )}
        </div>
      </div>
    </div>
  );
};

export default UpcomingEvents;
