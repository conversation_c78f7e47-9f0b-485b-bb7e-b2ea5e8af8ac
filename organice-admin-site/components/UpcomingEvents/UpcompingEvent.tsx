import cn from "classnames";
import { isToday, format } from "date-fns";
import React from "react";

import {
  CelebrationEvent,
  HolidayEvent,
  TimeOffEvent,
} from "../../graphql/client.generated";
import DateSeparator from "../DateSeparator";

import UpcomingCelebration from "./UpcomingCelebration";
import UpcomingHolidayEvent from "./UpcomingHolidayEvent";
import UpcomingTimeOff from "./UpcomingTimeOff";
import { UpcomingEvent } from "./types";

const UPCOMING_EVENTS_DATE_FORMAT = "MMM d, yyyy";

export const isHolidayEvent = (
  event: UpcomingEvent["events"][0]["event"]
): event is HolidayEvent => "isOfficial" in event;

export const isTimeOffEvent = (
  event: UpcomingEvent["events"][0]["event"]
): event is TimeOffEvent => {
  return "status" in event;
};

export const isCelebrationEvent = (
  event: UpcomingEvent["events"][0]["event"]
): event is CelebrationEvent => {
  return event.type.__typename === "CelebrationEventType";
};

export const UpcompingEvent = (upcoming: UpcomingEvent): JSX.Element => {
  const { date, events } = upcoming;
  const dateText = isToday(date)
    ? "Today"
    : format(date, UPCOMING_EVENTS_DATE_FORMAT);

  return (
    <>
      <DateSeparator dateLabel={dateText} className="mb-2" />
      {events.map((event, index) => {
        if (isHolidayEvent(event.event)) {
          return (
            <div
              key={event.event.id}
              className={cn("mx-5 flex justify-between gap-4 py-2", {
                "border-t": index,
              })}
            >
              <UpcomingHolidayEvent
                key={`${event.event.id}-${event.event.type.label}`}
                event={event.event}
              />
            </div>
          );
        }

        if (!event.member) {
          return null;
        }

        if (isTimeOffEvent(event.event)) {
          return (
            <div
              key={event.event.id}
              className={cn("mx-5 flex justify-between gap-4 py-2", {
                "border-t": index,
              })}
            >
              <UpcomingTimeOff
                key={`${event.member.id}-${event.event.type.label}`}
                member={event.member}
                event={event.event}
              />
            </div>
          );
        }

        return (
          <div
            key={event.event.id}
            className={cn("mx-5 flex justify-between gap-4 py-2", {
              "border-t": index,
            })}
          >
            <UpcomingCelebration
              key={`${event.member.id}-${event.event.type.label}`}
              member={event.member}
              event={event.event}
            />
          </div>
        );
      })}
    </>
  );
};

export default UpcompingEvent;
