import cn from "classnames";
import { isEqual } from "lodash";
import React, {
  FC,
  ReactElement,
  useRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
  PropsWithChildren,
  MouseEvent,
  SetStateAction,
  Dispatch,
  HTMLAttributes,
} from "react";
import {
  ReactFlow,
  useReactFlow,
  Controls,
  Position,
  Handle,
  Background,
  Node as FlowNode,
  useUpdateNodeInternals,
  ReactFlowProvider,
  NodeProps,
  applyNodeChanges,
  applyEdgeChanges,
  NodeChange,
  EdgeChange,
  NodeDimensionChange,
  BaseEdge,
  getSmoothStepPath,
  SmoothStepEdgeProps,
  Edge,
  MiniMap,
  NodeTypes,
  ControlButton,
} from "reactflow";
import { Layers, Subtitles } from "solar-icon-set";

import { useConfirm } from "../../context/ConfirmContext";
import {
  useOrgChartContext,
  OrgChartProvider,
  DataField,
  DataFieldWithType,
} from "../../context/OrgChartContext";
import { useSessionContext } from "../../context/SessionContext";
import {
  useMoveDepartmentMutation,
  useMovePositionMutation,
  PosFieldsFragment,
  DepFieldsFragment,
  RootNodeFieldsFragment,
} from "../../graphql/client.generated";
import { EdgeData } from "../../helpers/buildTree";
import {
  SelectDepartmentColors,
  getDepartmentColors,
} from "../../helpers/colors";
import { dateToDateString } from "../../helpers/date";
import {
  FlatMember,
  flattenMember,
  getCountryFromList,
} from "../../helpers/flattenMember";
import { LoadNodeWithSubordinatesFunc } from "../../hooks/useAllKnownNodes";
import useDebouncedCallback from "../../hooks/useDebouncedCallback";
import useSyncChildrenDepartmentInCache from "../../hooks/useSyncChildrenDepartmentInCache";
import { useToast } from "../../hooks/useToast";
import { TreeState, TreeActions } from "../../hooks/useTree";
import StackedSVG from "../../public/svg/orgChart/stacked.svg";
import UnstackedSVG from "../../public/svg/orgChart/unstacked.svg";
import PlusSVG from "../../public/svg/rawPlus.svg";
import Avatar from "../Avatar";
import { LinkButton } from "../Button";
import CardExpandToggler from "../CardExpandToggler";
import NodeSkeleton from "../Loading/NodeSkeleton";
import RootNode from "../RootNode";
import AddNodeButton from "../Settings/OrgChart/AddNodeButton";
import TeamsTags from "../TeamsTags";
import Tooltip from "../Tooltip";
import { NEW_NODE_ID } from "../Tree/constants";
import { Node, RootNode as DataRootNode, NodeRef } from "../Tree/types";

import DataFieldDropdown from "./DataFieldDropdown";
import NewNode from "./NewNode";
import { usePositionNodes, ClusteringOptions } from "./hooks";
import {
  getDataNodesChanges,
  getDataEdgesChanges,
  applyDataEdgesChanges,
  applyDataNodesChanges,
  isDataNode,
  isRootNode,
  getIntersectionSide,
  alignPath,
  getDimensionForNode,
} from "./utils";

import "reactflow/dist/style.css";

type DataNode = Node & {
  isClustered?: boolean;
  department?: {
    id: string;
  } | null;
};

// https://reactflow.dev/examples/interaction/contextual-zoom

interface BaseNodeProps extends HTMLAttributes<HTMLElement> {
  nodeId: string;
  subordinates: {
    id: string;
  }[];
  hasIncomindEdge?: boolean;
  className?: string;
}

const SmoothEdge: FC<SmoothStepEdgeProps> = ({
  id,
  target,
  sourcePosition = Position.Bottom,
  targetPosition = Position.Top,
}) => {
  const { chartState } = useOrgChartContext();

  if (!chartState) {
    return null;
  }
  const edge = chartState.outgoingEdges.find((e) => e.id === id);

  if (!edge) {
    return null;
  }

  /**
   * NOTE: reactflow sends sourceX/Y targetX/Y values as params
   * but as they are calculated with shifts
   * that depens on Handler elements sizes we do all calculates based on our
   * coordinates
   */
  const sourceX = edge.sourceHandler.x;
  const sourceY = edge.sourceHandler.y;

  const outgoingPaths = edge.edges.map((e) => {
    const [path] = getSmoothStepPath({
      sourceX,
      sourceY,
      sourcePosition,
      targetX: e.targetHandler.x,
      targetY: e.targetHandler.y,
      targetPosition,
    });

    return path;
  });

  const [path] = getSmoothStepPath({
    sourceX,
    sourceY,
    targetX: edge.targetHandler.x,
    targetY: edge.targetHandler.y,
  });

  const isSelected = chartState.activePath.includes(target);

  return (
    <BaseEdge
      data-test="vertical-line"
      id={id}
      path={alignPath(path, outgoingPaths)}
      style={{
        stroke: isSelected ? "#7C3AED" : "#94A3B8",
        strokeWidth: isSelected ? 2 : 1,
      }}
    />
  );
};

interface SelectableNodeProps {
  nodeId: string;
}

export const SelectableNode: FC<PropsWithChildren<SelectableNodeProps>> = ({
  nodeId,
  children,
}) => {
  const { treeState } = useOrgChartContext();
  const selected = treeState.currentNodeId === nodeId;

  return (
    <div
      data-test={`node-item-wrap-${nodeId}`}
      data-active={selected}
      className={cn("h-full w-full cursor-pointer", {
        "rounded-md ring ring-violet-500": selected,
      })}
    >
      {children}
    </div>
  );
};

interface ExtandableNodeProps {
  nodeId: string;
}

interface NewNodeData {
  parentId: string;
  rootNodeId: string;
  activePath: string[];
}

interface LoadingNodeData {
  parentId: string;
}

const defaultHandleStyle = {
  display: "none",
};

const ExtendableNode: FC<PropsWithChildren<ExtandableNodeProps>> = ({
  children,
  nodeId,
}) => {
  const updateNodeInternals = useUpdateNodeInternals();
  const { setEdges, setNodes } = useReactFlow<NewNodeData>();
  const {
    chartState,
    getActivePath,
    registerNewNode,
    registerAffectedNodes,
    onNewNodeOpen,
  } = useOrgChartContext();

  const addNewNodeAction = useMemo(
    () => ({
      label: "Add position or department",
      content: (
        <Tooltip
          onMouseDown={(e) => {
            e.stopPropagation();
          }}
          message="Add position or department"
        >
          <PlusSVG />
        </Tooltip>
      ),
      handler: () => {
        updateNodeInternals(nodeId);
        registerNewNode(nodeId);
        setEdges((edges) =>
          edges.reduce<typeof edges>((acc, edge, i) => {
            /**
             * NOTE: if we are adding to a root node the new node edge
             * should be first
             */
            if (edge.source === nodeId && !i) {
              acc.push({
                id: `${nodeId}-${NEW_NODE_ID}`,
                type: "new",
                source: nodeId,
                target: NEW_NODE_ID,
                focusable: false,
              });
            }
            acc.push(edge);

            if (edge.target === nodeId) {
              acc.push({
                id: `${nodeId}-${NEW_NODE_ID}`,
                type: "new",
                source: nodeId,
                target: NEW_NODE_ID,
                focusable: false,
              });
            }

            return acc;
          }, [])
        );
        setNodes((nodes) =>
          nodes.reduce<typeof nodes>((acc, node) => {
            acc.push(node);

            if (node.id === nodeId) {
              acc.push({
                id: NEW_NODE_ID,
                type: "new",
                data: {
                  parentId: nodeId,
                  rootNodeId: nodeId,
                  activePath: getActivePath(nodeId),
                },
                draggable: false,
                position: {
                  x: 0,
                  y: 0,
                },
                zIndex: 100,
              });
            }

            return acc;
          }, [])
        );
        registerAffectedNodes([NEW_NODE_ID], 100);

        if (onNewNodeOpen) {
          onNewNodeOpen();
        }
      },
    }),
    [
      setEdges,
      setNodes,
      nodeId,
      getActivePath,
      registerNewNode,
      updateNodeInternals,
      registerAffectedNodes,
      onNewNodeOpen,
    ]
  );

  return (
    <>
      {children}
      {chartState?.newNodeFor === nodeId ? (
        <Handle
          key={NEW_NODE_ID}
          type="source"
          position={Position.Bottom}
          id={NEW_NODE_ID}
          style={defaultHandleStyle}
        />
      ) : null}
      <AddNodeButton
        chipClassName="opacity-0 transition-opacity z-40 -translate-x-1/2 group-two-hover:visible group-two-hover:opacity-100"
        action={addNewNodeAction}
      />
    </>
  );
};

export const BaseNode: FC<PropsWithChildren<BaseNodeProps>> = ({
  children,
  nodeId,
  subordinates,
  hasIncomindEdge = true,
  className,
  ...attrs
}) => {
  const updateNodeInternals = useUpdateNodeInternals();
  const oldSubordinates = useRef(subordinates);

  /**
   * NOTE: when we do DnD, edges got rendered before we call updateNodeInternals
   * that is why reactflow raises a warning about the missing Handle
   */
  useEffect(() => {
    if (!isEqual(oldSubordinates.current, subordinates)) {
      updateNodeInternals(nodeId);
      oldSubordinates.current = subordinates;
    }
  }, [subordinates, nodeId, updateNodeInternals]);

  return (
    <>
      {subordinates.map((subordinate) => {
        return (
          <Handle
            key={subordinate.id}
            type="source"
            position={Position.Bottom}
            id={subordinate.id}
            style={defaultHandleStyle}
          />
        );
      })}
      {hasIncomindEdge ? (
        <Handle
          type="target"
          position={Position.Top}
          id={nodeId}
          style={defaultHandleStyle}
        />
      ) : null}
      <div
        className={cn(
          "group-two relative box-border rounded-md border border-gray-300 bg-white",
          className
        )}
        {...attrs}
      >
        {children}
      </div>
    </>
  );
};

interface ExpandableNodeProps<TNode extends DataNode> {
  node: TNode;
}

export const ExpandableNode = <TNode extends DataNode>({
  children,
  node,
}: ExpandableNodeProps<TNode> & {
  children?: ReactElement;
}): ReactElement | null => {
  const { treeState, treeActions, loadSubordinates, registerAffectedNodes } =
    useOrgChartContext();
  const { getNodes, setEdges, setNodes } = useReactFlow<LoadingNodeData>();
  const isExpanded = treeState.expandedNodesIds.some((id) => id === node.id);

  const toggleCard = useCallback(async () => {
    treeActions.toggleNodeExpansion(node.id);
    const knownNodes = getNodes();

    if (!isExpanded && node.subordinatesNumber > 0) {
      if (
        node.subordinates?.some((s) => !knownNodes.some((n) => n.id === s.id))
      ) {
        setNodes((nodes) =>
          nodes.reduce<typeof nodes>((acc, n) => {
            acc.push(n);

            if (n.id === node.id) {
              acc.push(
                ...(node.subordinates?.map((s) => ({
                  id: s.id,
                  type: "loading",
                  data: {
                    parentId: node.id,
                  },
                  draggable: false,
                  position: {
                    x: 0,
                    y: 0,
                  },
                })) ?? [])
              );
            }

            return acc;
          }, [])
        );

        setEdges((edges) =>
          edges.reduce<typeof edges>((acc, edge) => {
            acc.push(edge);

            if (edge.target === node.id) {
              acc.push(
                ...(node.subordinates?.map((s) => ({
                  id: `${node.id}-${s.id}`,
                  type: "loading",
                  source: node.id,
                  target: s.id,
                  focusable: false,
                })) ?? [])
              );
            }

            return acc;
          }, [])
        );
      }

      if (node.subordinates?.length) {
        registerAffectedNodes(
          node.subordinates.map((n) => n.id),
          100
        );
      }
      await loadSubordinates(node);
    }
  }, [
    node,
    isExpanded,
    treeActions,
    loadSubordinates,
    setNodes,
    setEdges,
    getNodes,
    registerAffectedNodes,
  ]);

  return (
    <>
      {children}
      <CardExpandToggler
        className={cn(
          "nodrag absolute right-[8px] bottom-[8px] z-40 h-[20px] text-xs"
        )}
        count={node.allPositionNumber}
        isVisible={node.subordinatesNumber > 0}
        isExpanded={isExpanded}
        onClick={(event) => {
          event.stopPropagation();
          void toggleCard();
        }}
      />
    </>
  );
};

interface DroppableNodeProps {
  nodeId: string;
}

const DroppableNode: FC<PropsWithChildren<DroppableNodeProps>> = ({
  nodeId,
  children,
}) => {
  const { chartState } = useOrgChartContext();
  const isHovered = useMemo(() => {
    return chartState?.draggingNode?.intersectedNode === nodeId;
  }, [chartState?.draggingNode?.intersectedNode, nodeId]);

  const isDragging = useMemo(() => {
    return chartState?.draggingNode?.node === nodeId;
  }, [chartState?.draggingNode?.node, nodeId]);

  const intersectionSide = useMemo(() => {
    if (!isHovered) {
      return null;
    }

    return chartState?.draggingNode?.intersectedSide;
  }, [isHovered, chartState?.draggingNode?.intersectedSide]);

  const isParentDragging = useMemo(() => {
    return chartState?.draggingNode?.subTree.includes(nodeId);
  }, [chartState?.draggingNode?.subTree, nodeId]);

  const sideIntersectionTooltip = useMemo(
    () =>
      intersectionSide === "left"
        ? "Drop here to set the node as a previous sibling of the highlighted node"
        : "Drop here to set the node as a next sibling of the highlighted node",
    [intersectionSide]
  );

  return (
    <div
      className={cn("h-full rounded-md hover:ring hover:ring-violet-500", {
        "ring ring-violet-500": isHovered,
        "opacity-50": isDragging || isParentDragging,
      })}
    >
      {intersectionSide === "center" ? (
        <Tooltip
          message="Drop here to set the node as a child of the selected node"
          className="absolute top-0 left-[125px] !w-full rounded-md"
          isOpenOnStart
        >
          <div />
        </Tooltip>
      ) : null}
      {children}
      {intersectionSide && ["top", "bottom"].includes(intersectionSide) ? (
        <Tooltip
          message={sideIntersectionTooltip}
          className={cn(
            "absolute left-0 h-[10px] !w-full rounded-md bg-violet-600",
            {
              "top-[-20px]": intersectionSide === "top",
              "bottom-[-20px]": intersectionSide === "bottom",
            }
          )}
          placement={intersectionSide === "bottom" ? "bottom" : "top"}
          isOpenOnStart
        >
          <div className="w-[250px]" />
        </Tooltip>
      ) : null}
      {intersectionSide && ["left", "right"].includes(intersectionSide) ? (
        <Tooltip
          message={sideIntersectionTooltip}
          className={cn(
            "absolute top-0 h-full w-[10px] rounded-md bg-violet-600",
            {
              "left-[-20px]": intersectionSide === "left",
              "right-[-20px]": intersectionSide === "right",
            }
          )}
          isOpenOnStart
        >
          <div className="w-[10px]" />
        </Tooltip>
      ) : null}
    </div>
  );
};

const CustomLoadingNode: FC<NodeProps> = ({ id }) => {
  return (
    <BaseNode nodeId={id} subordinates={[]} className="h-[100px] w-[250px]">
      <NodeSkeleton />
    </BaseNode>
  );
};

const CustomNewNode: FC<NodeProps<NewNodeData>> = ({ id, data }) => {
  const { deleteElements } = useReactFlow();
  const { treeActions } = useOrgChartContext();
  const { unregisterNewNode } = useOrgChartContext();

  const onAddNode = useCallback(
    (node: DepFieldsFragment | PosFieldsFragment) =>
      treeActions.toggleNodeExpansion(node.id),
    [treeActions]
  );

  /**
   * NOTE: nopan is a reactflow class w/o it reactflow will not pass click
   * events to the select
   *
   * https://github.com/xyflow/xyflow/discussions/2694#discussioncomment-5501955
   *
   * nowheel is a reactflow class that disables wheel events
   * https://reactflow.dev/api-reference/types/node-props#notes
   */
  return (
    <BaseNode
      nodeId={id}
      subordinates={[]}
      className="nopan nowheel h-[100px] w-[250px]"
    >
      <NewNode
        selectNode={treeActions.selectNode}
        setNewNode={() => {
          unregisterNewNode();
          deleteElements({
            nodes: [
              {
                id,
              },
            ],
          });
        }}
        newNode={data}
        currentPath={data.activePath}
        onAddNode={onAddNode}
      />
    </BaseNode>
  );
};

export type RootNodeHOC =
  | { Component: React.FC<BaseNodeProps>; props: BaseNodeProps }
  | { Component: React.FC<ExtandableNodeProps>; props: ExtandableNodeProps };

const CustomRootNode = <TNode extends RootNodeFieldsFragment>({
  id,
  data,
}: NodeProps<TNode>): ReactElement | null => {
  const { editable } = data;

  const hocs: RootNodeHOC[] = [
    {
      Component: BaseNode,
      props: {
        nodeId: id,
        hasIncomindEdge: false,
        subordinates: data.rootSubordinates,
        className: "h-[62px] w-[62px]",
        // @ts-expect-error: eventhough we extend HTMLAttributes<HTMLElement> TS does not like it
        "data-root": true,
      },
    },
  ];

  if (editable) {
    hocs.unshift({
      Component: ExtendableNode,
      props: {
        nodeId: id,
      },
    });
  }

  return hocs.reduce(
    // @ts-expect-error TS does not work well with reduce + unions
    (acc, { Component, props }) => <Component {...props}>{acc}</Component>,
    <RootNode node={data} />
  );
};

export type NodeHOC<TNode extends DataNode> =
  | { Component: React.FC<BaseNodeProps>; props: BaseNodeProps }
  | { Component: React.FC<SelectableNodeProps>; props: SelectableNodeProps }
  | { Component: React.FC<ExtandableNodeProps>; props: ExtandableNodeProps }
  | { Component: React.FC<DroppableNodeProps>; props: DroppableNodeProps }
  | {
      Component: React.FC<ExpandableNodeProps<TNode>>;
      props: ExpandableNodeProps<TNode>;
    };

const NoSubtitleMessage: FC<{ label: string }> = ({ label }) => (
  <div className="flex items-center gap-2 text-main-500">
    <Subtitles /> {label} is not set
  </div>
);

export const PositionSubtitle: FC<{
  member: FlatMember;
  dataField?: DataFieldWithType;
}> = ({ member, dataField }) => {
  const { session } = useSessionContext();

  const memberCountry = member.country
    ? getCountryFromList(member.country)
    : null;

  if (!dataField || dataField.id === "country") {
    return memberCountry ? (
      <div className="text-main-500">
        {memberCountry.emoji} {memberCountry.name}
      </div>
    ) : (
      <NoSubtitleMessage label={dataField?.label ?? "Country"} />
    );
  }

  const { id, label, type } = dataField;

  if (id === "birthday") {
    const shouldHideBirthday =
      member.birthday?.hideBirthday &&
      !session?.me.isAdmin &&
      session?.me.id !== member.id;

    if (!member.birthday || shouldHideBirthday) {
      return <NoSubtitleMessage label={label} />;
    }

    return (
      <div className="text-main-500">
        {dateToDateString(member.birthday.date)}
      </div>
    );
  }

  const fieldValue = member[id];

  if (!fieldValue) {
    return <NoSubtitleMessage label={label} />;
  }

  if (type === "date") {
    return fieldValue instanceof Date ? (
      <div className="text-main-500">{dateToDateString(fieldValue)}</div>
    ) : null;
  }

  if (type === "link") {
    return typeof fieldValue === "string" ? (
      <div className="text-main-500">
        <LinkButton href={fieldValue}>{fieldValue}</LinkButton>
      </div>
    ) : null;
  }

  if (type === "text") {
    return typeof fieldValue === "string" ? (
      <div className="text-main-500">{fieldValue}</div>
    ) : null;
  }

  if (type === "user") {
    return typeof fieldValue === "object" && "positionId" in fieldValue ? (
      <div className="flex flex-nowrap items-center gap-2 text-main-500">
        <Avatar size="s" photoUrl={fieldValue.photo?.url72} />
        <div className="truncate" title={fieldValue.name ?? ""}>
          {fieldValue.name}
        </div>
      </div>
    ) : null;
  }

  return null;
};

export const PositionNode = <TNode extends PosFieldsFragment>({
  position,
  canOpenTeam,
  onTeamClick,
  dataField,
}: {
  position: TNode;
  canOpenTeam?: boolean;
  dataField?: DataFieldWithType;
  onTeamClick?: (teamId: string, e: MouseEvent) => void;
}): ReactElement | null => {
  const isDepartmentManager =
    position.managedDepartmentId === position.department?.id;

  const member = position.member ? flattenMember(position.member) : null;

  return (
    <div
      data-prevent-sidebar-close
      className={cn(
        "flex h-full w-full flex-col justify-end overflow-hidden text-sm text-main-900",
        {
          rounded: isDepartmentManager,
        },
        isDepartmentManager &&
          position.department &&
          getDepartmentColors(position.department.departmentColor)
      )}
    >
      <div
        className={cn(
          "mb-auto  h-[6px] w-full overflow-hidden rounded-t-md",
          { "bg-gray-100": !position.department },
          position.department &&
            getDepartmentColors(position.department.departmentColor)
        )}
      />
      <div className="mb-2 flex items-center justify-between gap-2 px-2">
        <div
          data-test={`node-position-title-${position.id}`}
          className="truncate whitespace-nowrap"
        >
          {position.title ? (
            position.title
          ) : (
            <span className="text-main-400">Title Not Set</span>
          )}
        </div>
        <div className="nodrag">
          <TeamsTags
            teams={position.teams}
            positionId={position.id}
            mouseDown={canOpenTeam && onTeamClick ? onTeamClick : undefined}
          />
        </div>
      </div>

      <div className="flex items-center gap-3 px-2 pb-2">
        <Avatar photoUrl={member?.photo?.url72} />
        <div className="overflow-hidden">
          <div
            className={cn("truncate", {
              "font-normal !text-main-500": !member?.name,
              "font-semibold": member?.name,
            })}
          >
            {member?.name ?? "Open position"}
          </div>
          {member ? (
            <PositionSubtitle member={member} dataField={dataField} />
          ) : null}
        </div>
      </div>
    </div>
  );
};

const CustomPositionNode = <TNode extends PosFieldsFragment>({
  id,
  data,
}: NodeProps<TNode>): ReactElement | null => {
  const { editable } = data;
  const orgChartContext = useOrgChartContext();
  const hocs: NodeHOC<TNode>[] = [
    {
      Component: ExpandableNode<TNode>,
      props: {
        node: data,
      },
    },
    {
      Component: DroppableNode,
      props: {
        nodeId: id,
      },
    },
  ];

  const onTeamClick = useCallback(
    (teamId: string, e: MouseEvent): void => {
      e.stopPropagation();
      e.preventDefault();

      if (orgChartContext.onTeamClick) {
        orgChartContext.onTeamClick(teamId);
      }
    },
    [orgChartContext]
  );

  if (editable) {
    hocs.push({
      Component: ExtendableNode,
      props: {
        nodeId: id,
      },
    });
  }
  hocs.push(
    ...[
      { Component: SelectableNode, props: { nodeId: id } },
      {
        Component: BaseNode,
        props: {
          nodeId: id,
          subordinates: data.subordinates ?? [],
          className: "h-[100px] w-[250px]",
        },
      },
    ]
  );

  return hocs.reduce(
    // @ts-expect-error TS does not work well with reduce + unions
    (acc, { Component, props }) => <Component {...props}>{acc}</Component>,
    <PositionNode
      position={data}
      canOpenTeam
      onTeamClick={onTeamClick}
      dataField={orgChartContext.dataField}
    />
  );
};

export const DepartmentNode = <TNode extends DepFieldsFragment>({
  department,
}: {
  department: TNode;
}): ReactElement | null => {
  return (
    <div
      data-prevent-sidebar-close
      className="flex h-full w-full items-center gap-2 rounded-md p-4"
    >
      <p className="truncate text-sm font-semibold">{department.title}</p>
    </div>
  );
};

const CustomDepartmentNode = <TNode extends DepFieldsFragment>({
  id,
  data,
}: NodeProps<TNode>): ReactElement | null => {
  const { editable } = data;

  const hocs: NodeHOC<TNode>[] = [
    {
      Component: ExpandableNode<TNode>,
      props: {
        node: data,
      },
    },
    {
      Component: DroppableNode,
      props: {
        nodeId: id,
      },
    },
  ];

  if (editable) {
    hocs.push({
      Component: ExtendableNode,
      props: {
        nodeId: id,
      },
    });
  }
  hocs.push(
    ...[
      { Component: SelectableNode, props: { nodeId: id } },
      {
        Component: BaseNode,
        props: {
          nodeId: id,
          subordinates: data.subordinates ?? [],
          className: cn(
            "h-[36px] w-[250px]",
            getDepartmentColors(data.departmentColor)
          ),
        },
      },
    ]
  );

  return hocs.reduce(
    // @ts-expect-error TS does not work well with reduce + unions
    (acc, { Component, props }) => <Component {...props}>{acc}</Component>,
    <DepartmentNode department={data} />
  );
};

const defaultNodeTypes = {
  root: CustomRootNode,
  new: CustomNewNode,
  loading: CustomLoadingNode,
  position: CustomPositionNode,
  department: CustomDepartmentNode,
};

const edgetTypes = {
  default: SmoothEdge,
  loading: SmoothEdge,
  new: SmoothEdge,
};

interface NodesAndEdgesState<
  TNode extends DataNode,
  TRoot extends DataRootNode
> {
  nodes: FlowNode<TNode | TRoot>[];
  edges: Edge<EdgeData>[];
}

const usePositionedNodes = <TNode extends DataNode, TRoot extends DataRootNode>(
  props: NodesAndEdgesState<TNode, TRoot>,
  options: ClusteringOptions
): [
  NodesAndEdgesState<TNode, TRoot>,
  Dispatch<SetStateAction<NodesAndEdgesState<TNode, TRoot>>>
] => {
  const positionNodes = usePositionNodes<TNode, TRoot>(options);

  const positionedInitialNodes = useMemo(
    () => positionNodes(props.nodes, props.edges),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const [data, setData] = useState<{
    nodes: FlowNode<TNode | TRoot>[];
    edges: Edge<EdgeData>[];
  }>({
    nodes: positionedInitialNodes,
    edges: props.edges,
  });

  const repositionAndSetNodes = useCallback(
    (newState: SetStateAction<NodesAndEdgesState<TNode, TRoot>>) => {
      setData((prev) => {
        const state =
          typeof newState === "function" ? newState(prev) : newState;

        /**
         * NOTE: during the drag we don't want to visually update the dragging
         * node position as we use draggingNode from the context
         */
        const nodes = state.nodes.map((n) => {
          if (n.dragging) {
            const prevPosition = prev.nodes.find(
              (pn) => pn.id === n.id
            )?.position;

            if (prevPosition) {
              return {
                ...n,
                position: prevPosition,
              };
            }
          }

          return n;
        });

        const positionedNodes = positionNodes(nodes, state.edges);

        return {
          nodes: isEqual(positionedNodes, prev.nodes)
            ? prev.nodes
            : positionedNodes,
          edges: isEqual(state.edges, prev.edges) ? prev.edges : state.edges,
        };
      });
    },
    [positionNodes]
  );

  return [data, repositionAndSetNodes];
};

const getSubtree = <TNode extends DataNode>(
  nodeId: string,
  nodes: FlowNode<TNode>[]
): string[] => {
  const subtree: string[] = [];
  const stack = [nodeId];

  while (stack.length) {
    const currentNodeId = stack.pop();
    const children = nodes
      .filter((n) => n.data.parentId === currentNodeId)
      .map((n) => n.id);

    subtree.push(...children);
    stack.push(...children.map((child) => child));
  }

  return subtree;
};

const useCalcualteOutgoingEdges: (nodes: FlowNode[], edges: Edge[]) => void = (
  nodes,
  edges
) => {
  const positionedNodes = useRef<FlowNode[]>([]);
  const { chartState, calcualteOutgoingEdges } = useOrgChartContext();
  const [shouldRecalculateEdges, setShouldRecalculateEdges] = useState(false);

  useEffect(() => {
    if (shouldRecalculateEdges) {
      calcualteOutgoingEdges(positionedNodes.current, edges);
      setShouldRecalculateEdges(false);
    }
  }, [calcualteOutgoingEdges, edges, shouldRecalculateEdges]);

  useEffect(() => {
    if (
      positionedNodes.current.length !== nodes.length ||
      !chartState?.outgoingEdges ||
      nodes.some(
        (node, i) =>
          node.position.x !== positionedNodes.current[i].position.x ||
          node.position.y !== positionedNodes.current[i].position.y
      )
    ) {
      setShouldRecalculateEdges(true);
    }
    positionedNodes.current = nodes;
  }, [nodes, setShouldRecalculateEdges, chartState?.outgoingEdges]);
};

interface OrgChartProps<TNode extends DataNode, TRoot extends DataRootNode> {
  nodes: FlowNode<TNode | TRoot>[];
  edges: Edge<EdgeData>[];
  nodeTypes: NodeTypes;
  setNodes: Dispatch<SetStateAction<FlowNode<TNode | TRoot>[]>>;
  setEdges: Dispatch<SetStateAction<Edge<EdgeData>[]>>;
  clusterHorizontal: boolean;
  toggleChartLayout?: () => void;
  dataFields: DataField[];
  updateChartDataField?: (field: string) => void;
}

const OrgChart = <TNode extends DataNode, TRoot extends DataRootNode>({
  nodes,
  edges,
  nodeTypes,
  setNodes,
  setEdges,
  clusterHorizontal,
  toggleChartLayout,
  dataFields,
  updateChartDataField,
}: OrgChartProps<TNode, TRoot>): ReactElement | null => {
  const { fitView, getIntersectingNodes, screenToFlowPosition, getViewport } =
    useReactFlow<TNode>();
  const { session } = useSessionContext();
  const syncChildrenDepartmentInCacheFn = useSyncChildrenDepartmentInCache();

  const {
    treeState,
    treeActions,
    chartState,
    registerAffectedNodes,
    unregisterAffectedNodes,
    setDraggingNode,
    getActivePath,
    dataField,
  } = useOrgChartContext();

  useCalcualteOutgoingEdges(nodes, edges);

  const nodesWORoot = useMemo(
    () => nodes.filter((n): n is FlowNode<TNode> => n.type !== "root"),
    [nodes]
  );
  const [movePosition] = useMovePositionMutation({
    refetchQueries: [
      "MemberSidebar",
      "PositionSidebar",
      "MemberSidebarPositionParentInfo",
      "DepartmentManagerInfo",
      "DepartmentParentInfo",
    ],
  });
  const [moveDepartment] = useMoveDepartmentMutation({
    refetchQueries: [
      "MemberSidebar",
      "PositionSidebar",
      "MemberSidebarPositionParentInfo",
      "DepartmentManagerInfo",
      "DepartmentParentInfo",
    ],
  });
  const toast = useToast();
  const { confirm } = useConfirm();

  const nodeDimensionChanges = useRef<NodeDimensionChange[]>([]);

  const applyDimensionNodeChanges = useDebouncedCallback((): void => {
    const necessaryChanges = nodeDimensionChanges.current
      .reduce<NodeDimensionChange[]>(
        (changes, change) => [
          ...changes.filter((c) => c.id !== change.id),
          change,
        ],
        []
      )
      .filter((change) => {
        const changedNode = nodes.find((n) => n.id === change.id);

        return (
          changedNode &&
          (changedNode.width !== change.dimensions?.width ||
            changedNode.height !== change.dimensions?.height)
        );
      });

    if (necessaryChanges.length) {
      setNodes((oldNodes) => applyNodeChanges(necessaryChanges, oldNodes));
    }

    nodeDimensionChanges.current = [];
  }, 100);

  /**
   * NOTE: reactflow fires a lot of unneccessay dimensionChanges changes events
   * to avoid unnecessary position calculations we skips unnecessary changes
   * as an alternative we can have simillar type of optimisation inside nodes
   * setter
   */
  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      const dimensionChanges: NodeDimensionChange[] = [];
      const otherChanges: NodeChange[] = [];

      changes.forEach((change) => {
        if (change.type === "dimensions") {
          dimensionChanges.push(change);

          return;
        }

        otherChanges.push(change);
      });

      if (dimensionChanges.length) {
        nodeDimensionChanges.current.push(...dimensionChanges);

        applyDimensionNodeChanges();
      }

      if (otherChanges.length) {
        setNodes((oldNodes) => applyNodeChanges(otherChanges, oldNodes));
      }
    },
    [applyDimensionNodeChanges, setNodes]
  );

  /**
   * NOTE: this triggers only when we add or remove an edge using
   * methods from useReactFlow hook
   */
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      setEdges((oldEdges) => applyEdgeChanges(changes, oldEdges));
    },
    [setEdges]
  );

  const onNodeMouseEnter = useCallback(
    (_: MouseEvent, node: FlowNode<TNode>) => {
      setNodes((oldNodes) =>
        oldNodes.map((n) => {
          if (n.id === node.id) {
            return {
              ...n,
              zIndex: 10,
            };
          }

          return n;
        })
      );
    },
    [setNodes]
  );

  const onNodeMouseLeave = useCallback(() => {
    setNodes((oldNodes) =>
      oldNodes.map((n) => {
        if (n.type === "new") {
          return n;
        }

        return {
          ...n,
          zIndex: 0,
        };
      })
    );
  }, [setNodes]);

  const onNodeDragStop = useCallback((): void => {
    setDraggingNode(null);
    setNodes((oldNodes) =>
      oldNodes.map((n) => {
        return {
          ...n,
          zIndex: 0,
        };
      })
    );

    if (!chartState?.draggingNode) {
      return;
    }
    const draggingNode = nodesWORoot.find(
      (n) => n.id === chartState.draggingNode?.node
    );

    if (!draggingNode) {
      return;
    }
    const intersectedNode = nodesWORoot.find(
      (n) => n.id === chartState.draggingNode?.intersectedNode
    );

    if (!intersectedNode) {
      return;
    }

    const input: {
      nodeId: string;
      parentId: string;
      nextItemIndex?: number;
    } = {
      nodeId: draggingNode.id,
      parentId: intersectedNode.id,
      nextItemIndex: undefined,
    };

    if (
      chartState.draggingNode.intersectedSide &&
      chartState.draggingNode.intersectedSide !== "center"
    ) {
      const intersectedNodeParent = nodes.find(
        (n) => n.id === intersectedNode.data.parentId
      );

      if (!intersectedNodeParent) {
        return;
      }

      input.parentId = intersectedNodeParent.id;

      const subordinates: NodeRef[] = [];

      if (isDataNode<TNode, TRoot>(intersectedNodeParent)) {
        subordinates.push(...(intersectedNodeParent.data.subordinates ?? []));
      }

      if (isRootNode<TNode, TRoot>(intersectedNodeParent)) {
        subordinates.push(...intersectedNodeParent.data.rootSubordinates);
      }

      const intersectedNodeOrder = subordinates.findIndex(
        (s) => s.id === intersectedNode.id
      );

      if (["left", "top"].includes(chartState.draggingNode.intersectedSide)) {
        input.nextItemIndex = intersectedNodeOrder;
      }

      if (
        ["right", "bottom"].includes(chartState.draggingNode.intersectedSide)
      ) {
        if (intersectedNodeOrder !== -1) {
          input.nextItemIndex = intersectedNodeOrder + 1;
        }
      }
    }

    if (draggingNode.type === "position") {
      void (
        draggingNode.data.managedDepartmentId
          ? confirm({
              message: "Are you sure you want to continue?",
              description:
                "This position is currently managing another department. Moving it will remove the manager role.",
              buttonConfirm: "Move and Remove Manager Role",
              buttonCancel: "Cancel",
            })
          : Promise.resolve()
      ).then(() => {
        void movePosition({
          variables: {
            data: {
              position: {
                positionId: input.nodeId,
              },
              parent: {
                nodeId: input.parentId,
              },
              nextItemIndex: input.nextItemIndex,
            },
          },
          onCompleted: (data) => {
            syncChildrenDepartmentInCacheFn(data.movePosition.position.id);
          },
        }).catch((err: Error) => {
          toast("error", `Operation cancelled, ${err.message}`);
        });
      });
    } else {
      void moveDepartment({
        variables: {
          department: {
            parentId: input.parentId,
            departmentId: input.nodeId,
            nextItemIndex: input.nextItemIndex,
          },
        },
        onCompleted: () => {
          registerAffectedNodes([input.nodeId], 100);
        },
      }).catch((err: Error) => {
        toast("error", `Operation cancelled, ${err.message}`);
      });
    }
  }, [
    confirm,
    syncChildrenDepartmentInCacheFn,
    setNodes,
    registerAffectedNodes,
    chartState,
    setDraggingNode,
    nodes,
    nodesWORoot,
    movePosition,
    moveDepartment,
    toast,
  ]);

  const onNodeDrag = useCallback(
    (event: MouseEvent, node: FlowNode<TNode>) => {
      const draggingAlignedRect = {
        ...screenToFlowPosition({
          x: event.clientX,
          y: event.clientY,
        }),
        ...getDimensionForNode("drag"),
      };
      const subTree = getSubtree(node.id, nodesWORoot);

      const intersections = getIntersectingNodes(draggingAlignedRect).filter(
        (n) => n.id !== node.id && !subTree.includes(n.id)
      );
      const intersectedNode = intersections.length
        ? intersections[intersections.length - 1]
        : null;

      let intersectedSide = intersectedNode
        ? getIntersectionSide(
            draggingAlignedRect,
            {
              x: intersectedNode.position.x,
              y: intersectedNode.position.y,
              width: intersectedNode.width ?? 0,
              height: intersectedNode.height ?? 0,
            },
            intersectedNode.data.isClustered ?? false
          )
        : null;

      /**
       * NOTE: we can not drop node into its parent
       */
      if (
        intersectedNode?.id === node.data.parentId &&
        intersectedSide === "center"
      ) {
        intersectedSide = null;
      }

      const isDepartmentManager =
        intersectedNode?.data.department?.id &&
        intersectedNode.data.managedDepartmentId ===
          intersectedNode.data.department.id;

      if (
        isDepartmentManager &&
        ["top", "left"].includes(intersectedSide ?? "")
      ) {
        intersectedSide = null;
      }

      if (intersectedNode) {
        setNodes((oldNodes) =>
          oldNodes.map((n) => {
            if (n.id === intersectedNode.id) {
              return {
                ...n,
                zIndex: 10,
              };
            }

            return n;
          })
        );
      }

      setDraggingNode({
        node: node.id,
        nodeParent: node.data.parentId,
        subTree,
        intersectedNode: intersectedNode?.id ?? null,
        intersectedSide,
        rect: draggingAlignedRect,
        position: {
          x: event.clientX,
          y: event.clientY,
        },
      });
    },
    [
      getIntersectingNodes,
      setDraggingNode,
      screenToFlowPosition,
      nodesWORoot,
      setNodes,
    ]
  );

  useEffect(() => {
    if (!chartState?.affectedNodes.length) {
      return;
    }
    const viewport = getViewport();

    window.requestAnimationFrame(() => {
      fitView({
        duration: 200,
        /**
         * NOTE: do not change the zoom
         */
        maxZoom: viewport.zoom,
        nodes: chartState.affectedNodes.map((id) => ({
          id,
        })),
      });

      unregisterAffectedNodes();
    });
  }, [
    chartState?.affectedNodes,
    fitView,
    unregisterAffectedNodes,
    getViewport,
  ]);

  return (
    <>
      {chartState?.draggingNode?.position ? (
        <div
          className="fixed z-10 flex h-[40px] w-[40px] cursor-grabbing items-center justify-center bg-violet-600 font-bold text-white opacity-50"
          style={{
            top: `${chartState.draggingNode.position.y}px`,
            left: `${chartState.draggingNode.position.x}px`,
          }}
        >
          {chartState.draggingNode.subTree.length + 1}
        </div>
      ) : null}

      <ReactFlow
        data-test="root-chart"
        nodes={nodes}
        edges={edges}
        onInit={() => {
          /**
           * NOTE: init happens after we positioned nodes for the first time
           */
          window.requestAnimationFrame(() =>
            fitView({
              maxZoom: 1,
              minZoom: 1,
              nodes: treeState.currentNodeId
                ? [
                    {
                      id: treeState.currentNodeId,
                    },
                  ]
                : [],
            })
          );
        }}
        nodesDraggable={session?.myWorkspace.editable ?? false}
        edgesFocusable={false}
        elementsSelectable={false}
        zoomOnDoubleClick={false}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        edgeTypes={edgetTypes}
        onNodeDragStop={onNodeDragStop}
        onNodeDrag={onNodeDrag}
        onNodeMouseEnter={onNodeMouseEnter}
        onNodeMouseLeave={onNodeMouseLeave}
        onNodeClick={(_, node: FlowNode<TNode>) => {
          registerAffectedNodes([node.id]);
          treeActions.selectNode(node.data, getActivePath(node.id));
        }}
        /**
         * NOTE: reactflow does not pass click events, so we can not close
         * sidebar when clicking on the empty space, so we do it here
         */
        onPaneClick={() => {
          treeActions.deselectNode();
        }}
        proOptions={{
          hideAttribution: true,
        }}
      >
        <Background className="bg-main-100" />
        {/* NOTE: reactflow sets higher z-index to map, so it can hover
        new node options dropdown */}
        {!chartState?.newNodeFor ? (
          <MiniMap
            className="border border-gray-300"
            position="bottom-left"
            nodeColor={(n: FlowNode<DepFieldsFragment>) => {
              if (n.type === "department") {
                return SelectDepartmentColors[n.data.departmentColor];
              }

              return "#E2E8F0";
            }}
          />
        ) : null}

        <Controls position="top-left" showInteractive={false}>
          {updateChartDataField && dataField ? (
            <DataFieldDropdown
              currentValue={dataField}
              dataFields={dataFields}
              onSelect={(value) => {
                updateChartDataField(value);
              }}
            />
          ) : null}
          {toggleChartLayout ? (
            <ControlButton onClick={toggleChartLayout}>
              <Tooltip
                className="pt-1"
                message={
                  <>
                    <div className="mb-4 w-full text-left">
                      {clusterHorizontal
                        ? "Nodes are stacked to save horizontal space. Click to unstack"
                        : "Nodes are unstacked. Click to stack them to save horizontal space"}
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "flex w-full flex-col items-center rounded-md p-2",
                          {
                            "border border-gray-300": clusterHorizontal,
                            "border border-white bg-white text-main-900":
                              !clusterHorizontal,
                          }
                        )}
                      >
                        <div>Unstacked</div>
                        <UnstackedSVG />
                      </div>
                      <div
                        className={cn(
                          "flex w-full flex-col items-center rounded-md p-2",
                          {
                            "border border-white bg-white text-main-900":
                              clusterHorizontal,
                            "border border-gray-300": !clusterHorizontal,
                          }
                        )}
                      >
                        <div>Stacked</div>
                        <StackedSVG />
                      </div>
                    </div>
                  </>
                }
                placement="right"
              >
                <Layers
                  svgProps={{
                    style: {
                      maxHeight: "16px",
                      maxWidth: "16px",
                    },
                  }}
                  className={cn({
                    "!text-violet-500": clusterHorizontal,
                  })}
                />
              </Tooltip>
            </ControlButton>
          ) : null}
        </Controls>
      </ReactFlow>
    </>
  );
};

interface OrgChartWrapperProps<
  TNode extends DataNode,
  TRoot extends DataRootNode
> {
  data: {
    nodes: FlowNode<TNode | TRoot>[];
    edges: Edge<EdgeData>[];
  };
  clusterHorizontal: boolean;
  dataField?: DataFieldWithType;
  dataFields?: DataField[];
  toggleChartLayout?: () => void;
  updateChartDataField?: (field: string) => void;
  nodeTypes?: NodeTypes;
  nodeToActivePath: Record<string, string[]>;
  treeState: TreeState;
  treeActions: TreeActions<TNode>;
  loadSubordinates: LoadNodeWithSubordinatesFunc;
  onNewNodeOpen?: () => void;
  onTeamClick?: (teamId: string) => void;
}

const PositionedOrgChart = <
  TNode extends DataNode,
  TRoot extends DataRootNode
>({
  data,
  clusterHorizontal,
  dataField,
  dataFields = [],
  toggleChartLayout,
  updateChartDataField,
  treeState,
  treeActions,
  nodeToActivePath,
  loadSubordinates,
  nodeTypes,
  onNewNodeOpen,
  onTeamClick,
}: OrgChartWrapperProps<TNode, TRoot>): ReactElement | null => {
  const clusterHorizontalRef = useRef(clusterHorizontal);
  const [{ nodes, edges }, setNodesAndEdges] = usePositionedNodes<TNode, TRoot>(
    data,
    {
      ranksep: 40,
      nodesep: 40,
      clusterSep: 5,
      minClusterSize: clusterHorizontal ? 2 : 9999,
    }
  );

  const setNodes = useCallback(
    (newState: SetStateAction<FlowNode<TNode | TRoot>[]>) => {
      setNodesAndEdges((prev) => {
        const state =
          typeof newState === "function" ? newState(prev.nodes) : newState;

        return {
          nodes: state,
          edges: prev.edges,
        };
      });
    },
    [setNodesAndEdges]
  );

  const setEdges = useCallback(
    (newState: SetStateAction<Edge<EdgeData>[]>) => {
      setNodesAndEdges((prev) => {
        const state =
          typeof newState === "function" ? newState(prev.edges) : newState;

        return {
          nodes: prev.nodes,
          edges: state,
        };
      });
    },
    [setNodesAndEdges]
  );

  /**
   * NOTE: here we handle all changes that come from outside
   */
  useEffect(() => {
    const changes = getDataNodesChanges<TNode, TRoot>(data.nodes, nodes);

    if (changes.length) {
      const updatedNodes = applyDataNodesChanges(nodes, changes);

      setNodes(updatedNodes);
    }
  }, [data.nodes, nodes, setNodes]);

  useEffect(() => {
    if (clusterHorizontalRef.current !== clusterHorizontal) {
      setNodes((oldNodes) => oldNodes);
      clusterHorizontalRef.current = clusterHorizontal;
    }
  }, [clusterHorizontal, setNodes]);

  useEffect(() => {
    const changes = getDataEdgesChanges(data.edges, edges);

    if (changes.length) {
      const updatedEdges = applyDataEdgesChanges(edges, changes);

      setEdges(updatedEdges);
    }
  }, [data.edges, edges, setEdges]);

  /**
   * NOTE: reactflow asks not to make nodeTypes dynamic as it might affect
   * the performance, so we allow to customize it only once
   */
  const mergedNodeTypes = useMemo(() => {
    return {
      ...defaultNodeTypes,
      ...(nodeTypes ?? {}),
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <OrgChartProvider
      nodeToActivePath={nodeToActivePath}
      treeState={treeState}
      treeActions={treeActions}
      loadSubordinates={loadSubordinates}
      onNewNodeOpen={onNewNodeOpen}
      onTeamClick={onTeamClick}
      dataField={dataField}
    >
      <OrgChart<TNode, TRoot>
        nodes={nodes}
        edges={edges}
        clusterHorizontal={clusterHorizontal}
        toggleChartLayout={toggleChartLayout}
        setNodes={setNodes}
        setEdges={setEdges}
        nodeTypes={mergedNodeTypes}
        dataFields={dataFields}
        updateChartDataField={updateChartDataField}
      />
    </OrgChartProvider>
  );
};

const OrgChartWrapper = <TNode extends DataNode, TRoot extends DataRootNode>(
  props: OrgChartWrapperProps<TNode, TRoot>
): ReactElement | null => {
  return (
    <ReactFlowProvider>
      <PositionedOrgChart {...props} />
    </ReactFlowProvider>
  );
};

export default OrgChartWrapper;
