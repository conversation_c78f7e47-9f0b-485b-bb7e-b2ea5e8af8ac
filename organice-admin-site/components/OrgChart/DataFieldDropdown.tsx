import {
  FloatingOverlay,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  autoUpdate,
  FloatingPortal,
  useClick,
} from "@floating-ui/react";
import cn from "classnames";
import { FC, useState } from "react";
import { ControlButton } from "reactflow";
import { Subtitles } from "solar-icon-set";

import { DataField, DataFieldWithType } from "../../context/OrgChartContext";
import Tooltip from "../Tooltip";

interface DataFieldDropdownProps {
  currentValue: DataFieldWithType;
  dataFields: DataField[];
  onSelect: (value: string) => void;
}

const DataFieldDropdown: FC<DataFieldDropdownProps> = ({
  currentValue,
  dataFields,
  onSelect,
}) => {
  const [visible, setVisible] = useState(false);

  const { refs, floatingStyles, context } = useFloating({
    open: visible,
    onOpenChange: setVisible,
    strategy: "fixed",
    placement: "bottom-start",
    whileElementsMounted: autoUpdate,
  });

  const click = useClick(context);
  const dismiss = useDismiss(context, {
    outsidePress: true,
  });
  const role = useRole(context, { role: "menu" });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
    role,
  ]);

  const handleOptionSelect = (value: string): void => {
    onSelect(value);
    setVisible(false);
  };

  return (
    <>
      <ControlButton data-test="data-field-dropdown-trigger">
        <Tooltip
          message="Change Org Chart subtitle"
          placement="right-start"
          className="pt-1"
        >
          <div ref={refs.setReference} {...getReferenceProps()}>
            <Subtitles
              svgProps={{
                style: {
                  maxHeight: "16px",
                  maxWidth: "16px",
                },
              }}
            />
          </div>
        </Tooltip>
      </ControlButton>
      {visible ? (
        <FloatingPortal>
          <FloatingOverlay className="bg-black bg-opacity-50" />
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className="shadow-lg z-50 min-w-[200px] rounded-md border border-gray-300 bg-white"
            data-test="data-field-dropdown-menu"
          >
            <div className="border-b border-gray-200 px-4 py-3">
              <span className="font-semibold text-gray-900">
                Select Subtitle
              </span>
            </div>
            <div className="py-2">
              {dataFields.map((option) => (
                <button
                  key={option.id}
                  type="button"
                  onClick={() => handleOptionSelect(option.id)}
                  className={cn(
                    "block w-full px-4 py-2 text-left text-sm hover:bg-violet-50 focus:bg-violet-50 focus:outline-none",
                    {
                      "bg-violet-100 text-violet-700":
                        option.id === currentValue.id,
                      "text-gray-700": option.id !== currentValue.id,
                    }
                  )}
                  data-test={`data-field-option-${option.id}`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </FloatingPortal>
      ) : null}
    </>
  );
};

export default DataFieldDropdown;
