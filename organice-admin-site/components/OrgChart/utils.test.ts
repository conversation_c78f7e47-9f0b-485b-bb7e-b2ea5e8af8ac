import { expect } from "@jest/globals";
import { Edge, Node as FlowNode } from "reactflow";

import {
  getDataEdgesChanges,
  applyDataEdgesChanges,
  getDataNodesChanges,
  applyDataNodesChanges,
  OutsideEdgeChange,
  OutsideNodeChange,
  getIntersectionSide,
  alignPath,
} from "./utils";

describe("should generate edge changes", () => {
  it("should add new edges", () => {
    const dataEdges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: {} },
    ];

    const edges: Edge[] = [];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([{ type: "add", item: dataEdges[0], order: 0 }]);
  });

  it("should remove edges", () => {
    const dataEdges: Edge[] = [];
    const edges: Edge[] = [{ id: "a-b", source: "a", target: "b", data: {} }];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([{ type: "remove", id: "a-b" }]);
  });

  it("should update hidden status", () => {
    const dataEdges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: {}, hidden: true },
    ];
    const edges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: {}, hidden: false },
    ];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([{ type: "hidden", id: "a-b", hidden: true }]);
  });

  it("should update edge data", () => {
    const dataEdges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: { key: "value" } },
    ];
    const edges: Edge[] = [{ id: "a-b", source: "a", target: "b", data: {} }];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([
      { type: "data", id: "a-b", data: { key: "value" } },
    ]);
  });

  it("should update edge order", () => {
    const dataEdges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: {} },
      { id: "b-c", source: "b", target: "c", data: {} },
    ];
    const edges: Edge[] = [
      { id: "b-c", source: "b", target: "c", data: {} },
      { id: "a-b", source: "a", target: "b", data: {} },
    ];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([
      { id: "a-b", type: "order", order: 0 },
      { id: "b-c", type: "order", order: 1 },
    ]);
  });

  it("should handle mixed changes", () => {
    const dataEdges: Edge[] = [
      {
        id: "a-b",
        source: "a",
        target: "b",
        data: { key: "value" },
        hidden: true,
      },
      { id: "c-d", source: "c", target: "d", data: {} },
    ];
    const edges: Edge[] = [
      { id: "a-b", source: "a", target: "b", data: {}, hidden: false },
      { id: "b-c", source: "b", target: "c", data: {} },
    ];

    const changes = getDataEdgesChanges(dataEdges, edges);

    expect(changes).toEqual([
      { type: "hidden", id: "a-b", hidden: true },
      { type: "add", item: dataEdges[1], order: 1 },
      { type: "remove", id: "b-c" },
    ]);
  });
});

describe("should apply edge changes", () => {
  let edges: Edge[];

  beforeEach(() => {
    edges = [
      { id: "a-b", source: "a", target: "b", data: {} },
      { id: "b-c", source: "b", target: "c", data: {} },
      { id: "c-d", source: "c", target: "d", data: {} },
    ];
  });

  it("should add a new edge", () => {
    const changes: OutsideEdgeChange<unknown>[] = [
      {
        type: "add",
        item: { id: "d-e", source: "d", target: "e", data: {} },
        order: 1,
      },
    ];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges).toHaveLength(4);
    expect(updatedEdges[1]).toEqual({
      id: "d-e",
      source: "d",
      target: "e",
      data: {},
    });
  });

  it("should remove an edge", () => {
    const changes: OutsideEdgeChange<unknown>[] = [
      { type: "remove", id: "b-c" },
    ];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges).toHaveLength(2);
    expect(updatedEdges.find((e) => e.id === "b-c")).toBeUndefined();
  });

  it("should update the hidden status of an edge", () => {
    const changes: OutsideEdgeChange<unknown>[] = [
      { type: "hidden", id: "a-b", hidden: true },
    ];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges.find((e) => e.id === "a-b")?.hidden).toBe(true);
  });

  it("should update the data of an edge", () => {
    const changes: OutsideEdgeChange<{
      key?: string;
    }>[] = [{ type: "data", id: "a-b", data: { key: "value" } }];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges.find((e) => e.id === "a-b")?.data).toEqual({
      key: "value",
    });
  });

  it("should change the order of an edge", () => {
    const changes: OutsideEdgeChange<unknown>[] = [
      { type: "order", id: "b-c", order: 0 },
    ];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges[0].id).toBe("b-c");
    expect(updatedEdges[1].id).toBe("a-b");
    expect(updatedEdges[2].id).toBe("c-d");
  });

  it("should handle mixed changes", () => {
    const changes: OutsideEdgeChange<{
      key?: string;
    }>[] = [
      { type: "hidden", id: "a-b", hidden: true },
      { type: "data", id: "b-c", data: { key: "value" } },
      { type: "remove", id: "c-d" },
      {
        type: "add",
        item: { id: "d-e", source: "d", target: "e", data: {} },
        order: 1,
      },
      { type: "order", id: "a-b", order: 2 },
    ];

    const updatedEdges = applyDataEdgesChanges(edges, changes);

    expect(updatedEdges).toEqual([
      { id: "d-e", source: "d", target: "e", data: {} },
      { id: "a-b", source: "a", target: "b", data: {}, hidden: true },
      { id: "b-c", source: "b", target: "c", data: { key: "value" } },
    ]);
  });
});

describe("should generate node changes", () => {
  let dataNodes: FlowNode[];
  let nodes: FlowNode[];

  beforeEach(() => {
    dataNodes = [
      {
        id: "1",
        type: "default",
        data: { label: "Node 1" },
        position: { x: 0, y: 0 },
      },
      {
        id: "2",
        type: "default",
        data: { label: "Node 2" },
        position: { x: 0, y: 0 },
      },
    ];

    nodes = [
      {
        id: "1",
        type: "default",
        data: { label: "Node 1" },
        position: { x: 0, y: 0 },
      },
      {
        id: "2",
        type: "default",
        data: { label: "Node 2" },
        position: { x: 0, y: 0 },
      },
    ];
  });

  it("should add new nodes", () => {
    const changes = getDataNodesChanges(
      [
        ...dataNodes,
        {
          id: "3",
          type: "default",
          data: { label: "Node 3" },
          position: { x: 0, y: 0 },
        },
      ],
      nodes
    );

    expect(changes).toEqual([
      {
        type: "add",
        item: {
          id: "3",
          type: "default",
          data: { label: "Node 3" },
          position: { x: 0, y: 0 },
        },
        order: 2,
      },
    ]);
  });

  it("should remove nodes", () => {
    const changes = getDataNodesChanges(
      [
        {
          id: "1",
          type: "default",
          data: { label: "Node 1" },
          position: { x: 0, y: 0 },
        },
      ],
      nodes
    );

    expect(changes).toEqual([{ type: "remove", id: "2" }]);
  });

  it("should update hidden status", () => {
    dataNodes[0].hidden = true;

    const changes = getDataNodesChanges(dataNodes, nodes);

    expect(changes).toEqual([{ type: "hidden", id: "1", hidden: true }]);
  });

  it("should update node data", () => {
    dataNodes[0].data = { label: "Updated Node 1" };
    const changes = getDataNodesChanges(dataNodes, nodes);

    expect(changes).toEqual([
      { type: "data", id: "1", data: { label: "Updated Node 1" } },
    ]);
  });

  it("should handle loading nodes", () => {
    nodes[0].type = "loading";
    nodes[0].data = { label: "Node 1" };

    const changes = getDataNodesChanges(dataNodes, nodes);

    expect(changes).toEqual([
      {
        type: "loaded",
        id: "1",
        data: { label: "Node 1" },
        nodeType: "default",
      },
    ]);
  });

  it("should handle mixed changes", () => {
    dataNodes[0].hidden = true;
    dataNodes[1].data = { label: "Updated Node 2" };

    const changes = getDataNodesChanges(
      [
        ...dataNodes,
        {
          id: "3",
          type: "default",
          data: { label: "Node 3" },
          position: { x: 0, y: 0 },
        },
      ],
      nodes
    );

    expect(changes).toEqual([
      { type: "hidden", id: "1", hidden: true },
      { type: "data", id: "2", data: { label: "Updated Node 2" } },
      {
        type: "add",
        item: {
          id: "3",
          type: "default",
          data: { label: "Node 3" },
          position: { x: 0, y: 0 },
        },
        order: 2,
      },
    ]);
  });
});

describe("should apply node changes", () => {
  let nodes: FlowNode[];

  beforeEach(() => {
    nodes = [
      {
        id: "1",
        type: "default",
        data: { label: "Node 1" },
        position: { x: 0, y: 0 },
      },
      {
        id: "2",
        type: "default",
        data: { label: "Node 2" },
        position: { x: 0, y: 0 },
      },
      {
        id: "3",
        type: "default",
        data: { label: "Node 3" },
        position: { x: 0, y: 0 },
      },
    ];
  });

  it("should add a new node", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      {
        type: "add",
        item: {
          id: "4",
          type: "default",
          data: { label: "Node 4" },
          position: { x: 0, y: 0 },
        },
        order: 1,
      },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes).toHaveLength(4);
    expect(updatedNodes[1]).toEqual({
      id: "4",
      type: "default",
      data: { label: "Node 4" },
      position: { x: 0, y: 0 },
    });
  });

  it("should remove a node", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      { type: "remove", id: "2" },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes).toHaveLength(2);
    expect(updatedNodes.find((n) => n.id === "2")).toBeUndefined();
  });

  it("should update the hidden status of a node", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      { type: "hidden", id: "1", hidden: true },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes.find((n) => n.id === "1")?.hidden).toBe(true);
  });

  it("should update the data of a node", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      { type: "data", id: "1", data: { label: "Updated Node 1" } },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes.find((n) => n.id === "1")?.data).toEqual({
      label: "Updated Node 1",
    });
  });

  it("should handle loading nodes", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      {
        type: "loaded",
        id: "1",
        data: { label: "Loaded Node 1" },
        nodeType: "default",
      },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes.find((n) => n.id === "1")?.data).toEqual({
      label: "Loaded Node 1",
    });
    expect(updatedNodes.find((n) => n.id === "1")?.type).toBe("default");
  });

  it("should handle mixed changes", () => {
    const changes: OutsideNodeChange<unknown, unknown>[] = [
      { type: "hidden", id: "1", hidden: true },
      { type: "data", id: "2", data: { label: "Updated Node 2" } },
      { type: "remove", id: "3" },
      {
        type: "add",
        item: {
          id: "4",
          type: "default",
          data: { label: "Node 4" },
          position: { x: 0, y: 0 },
        },
        order: 1,
      },
      {
        type: "loaded",
        id: "1",
        data: { label: "Loaded Node 1" },
        nodeType: "default",
      },
    ];

    const updatedNodes = applyDataNodesChanges(nodes, changes);

    expect(updatedNodes).toEqual([
      {
        id: "1",
        type: "default",
        data: { label: "Loaded Node 1" },
        position: { x: 0, y: 0 },
        hidden: true,
      },
      {
        id: "4",
        type: "default",
        data: { label: "Node 4" },
        position: { x: 0, y: 0 },
      },
      {
        id: "2",
        type: "default",
        data: { label: "Updated Node 2" },
        position: { x: 0, y: 0 },
      },
    ]);
  });
});

describe("should calculate intersection sides", () => {
  it('should return "top" when rectA is above rectB and clustered', () => {
    const rectA = { x: 100, y: 61, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };
    const result = getIntersectionSide(rectA, rectB, true);

    expect(result).toBe("top");
  });

  it('should return "bottom" when rectA is below rectB and clustered', () => {
    const rectA = { x: 100, y: 199, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };
    const result = getIntersectionSide(rectA, rectB, true);

    expect(result).toBe("bottom");
  });

  it('should return "left" when rectA is to the left of rectB and not clustered', () => {
    const rectA = { x: 61, y: 100, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };
    const result = getIntersectionSide(rectA, rectB, false);

    expect(result).toBe("left");
  });

  it('should return "right" when rectA is to the right of rectB and not clustered', () => {
    const rectA = { x: 349, y: 100, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };
    const result = getIntersectionSide(rectA, rectB, false);

    expect(result).toBe("right");
  });

  it('should return "center" when rectA is within the center range of rectB and clustered', () => {
    const rectA = { x: 150, y: 150, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };

    const result = getIntersectionSide(rectA, rectB, true);

    expect(result).toBe("center");
  });

  it('should return "center" when rectA is within the center range of rectB and not clustered', () => {
    const rectA = { x: 150, y: 150, width: 40, height: 40 };
    const rectB = { x: 100, y: 100, width: 250, height: 100 };
    const result = getIntersectionSide(rectA, rectB, false);

    expect(result).toBe("center");
  });
});

describe("should align paths", () => {
  it("should return the original path if there are no outgoing paths", () => {
    const path = "M10 10 L20 20";
    const outgoingPaths: string[] = [];

    const result = alignPath(path, outgoingPaths);

    expect(result).toBe(path);
  });

  it("should return the original path if the path does not have 2 curves", () => {
    const path = "M10 10 L20 20 Q30 30 40 40";
    const outgoingPaths = ["M10 10 L20 20 Q30 30 40 40"];

    const result = alignPath(path, outgoingPaths);

    expect(result).toBe(path);
  });

  it("should align the path based on the minimum bend Y coordinate", () => {
    const path =
      "M 415 60 L 415 80 L 415 165 Q 415 170 420 170 L 500 170 Q 505 170 505 175 L 505 180 L 505 180";
    const outgoingPaths = [
      "M415 60L415 80L 415,115Q 415,120 410,120L 130,120Q 125,120 125,125L125 160L125 180",
    ];

    const result = alignPath(path, outgoingPaths);

    expect(result).toBe(
      "M415 60L 415 80L 415 115Q 415,120 420,120L 500 120Q 505,120 505,125L 505 130L 505 180"
    );
  });

  it("should return the original path if the minimum bend Y coordinate is the same as the current bend Y coordinate", () => {
    const path =
      "M415 60L415 80L 415,115Q 415,120 410,120L 130,120Q 125,120 125,125L125 160L125 180";
    const outgoingPaths = [
      "M 415 60 L 415 80 L 415 165 Q 415 170 420 170 L 500 170 Q 505 170 505 175 L 505 180 L 505 180",
    ];

    const result = alignPath(path, outgoingPaths);

    expect(result).toBe(path);
  });
});
