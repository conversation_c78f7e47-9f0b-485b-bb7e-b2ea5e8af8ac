import cn from "classnames";
import React, { memo } from "react";

import { useSessionContext } from "../../context/SessionContext";
import {
  DepFieldsFragment,
  PosFieldsFragment,
} from "../../graphql/client.generated";
import { getDepartmentColors } from "../../helpers/colors";
import { PositionNode, DepartmentNode } from "../OrgChart/OrgChart";

import { isDepartment } from "./utils";

interface Props {
  node: PosFieldsFragment | DepFieldsFragment;
  size?: "s" | "m";
}

const TreeCard: React.FC<Props> = ({ node, size = "m" }) => {
  const department = isDepartment(node);
  const departmentColor = department
    ? getDepartmentColors(node.departmentColor)
    : {};
  const { session } = useSessionContext();

  return (
    <div
      className={cn(
        "z-15 box-border w-[250px] rounded-md border border-gray-300",
        {
          "h-[36px]": department,
          "h-[100px]": !department,
          "w-[250px]": size === "m",
          "w-[240px]": size === "s",
          ...departmentColor,
        }
      )}
    >
      {department ? (
        <DepartmentNode department={node} />
      ) : (
        <PositionNode
          position={node}
          dataField={session?.myWorkspace.dataField}
        />
      )}
    </div>
  );
};

export default memo(TreeCard);
