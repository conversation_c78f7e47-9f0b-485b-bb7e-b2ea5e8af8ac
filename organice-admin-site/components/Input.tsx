import cn from "classnames";
import React, { ReactNode, ChangeEvent, FocusEvent } from "react";

interface InputProps {
  label?: string | ReactNode;
  type?: "text" | "date" | "number" | "email";
  min?: string;
  max?: string;
  maxLength?: number;
  minLength?: number;
  placeholder?: string;
  hint?: string;
  color?: "default" | "yellow";
  name?: string;
  disabled?: boolean;
  value?: string;
  defaultValue?: string;
  autoFocus?: boolean;
  required?: boolean;
  wrapperClassnames?: string;
  onChange?: (value: string) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  getBeforeIconBlock?: () => ReactNode;
  getAfterIconBlock?: () => ReactNode;
}

const Input: React.FC<InputProps> = ({
  color = "default",
  type = "text",
  name,
  placeholder,
  label,
  hint,
  value,
  defaultValue,
  onChange,
  disabled,
  autoFocus,
  onBlur,
  onFocus,
  wrapperClassnames,
  getBeforeIconBlock,
  getAfterIconBlock,
  ...props
}) => {
  const onInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    if (onChange) onChange(e.target.value);
  };
  const onInputBlur = (e: FocusEvent<HTMLInputElement>): void => {
    if (onBlur) onBlur(e);
  };

  const onInputFocus = (e: FocusEvent<HTMLInputElement>): void => {
    if (onFocus) onFocus(e);
  };

  return (
    <div className={wrapperClassnames}>
      {label && <div className="text-sm font-semibold leading-4">{label}</div>}
      <div
        className={cn("relative mt-2 flex items-center", {
          "mt-0": !label,
          "mt-2": label,
        })}
      >
        {getBeforeIconBlock ? (
          <div className="absolute left-2 z-10 flex h-full items-center">
            {getBeforeIconBlock()}
          </div>
        ) : (
          ""
        )}
        <input
          {...props}
          disabled={disabled}
          type={type}
          autoComplete="off"
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={autoFocus}
          name={name}
          className={cn(
            "leading-1 relative flex w-full items-center text-ellipsis rounded border bg-white px-3 py-[7px] text-sm outline-none transition-colors focus:border-violet-600",
            {
              "!block": type === "date",
              "border-yellow-400": color === "yellow",
              "border-slate-300": color === "default",
              "pl-8": !!getBeforeIconBlock,
              "pr-8": !!getAfterIconBlock,
              "cursor-not-allowed border-slate-300 bg-slate-100 text-slate-400":
                disabled,
            }
          )}
          value={value}
          defaultValue={defaultValue}
          onChange={onInputChange}
          onBlur={onInputBlur}
          onFocus={onInputFocus}
          placeholder={placeholder}
        />
        {getAfterIconBlock ? (
          <div className="absolute right-2 z-10 flex h-full items-center">
            {getAfterIconBlock()}
          </div>
        ) : (
          ""
        )}
      </div>
      {hint && <div className="mt-2 text-xs text-slate-500">{hint}</div>}
    </div>
  );
};

export default Input;
