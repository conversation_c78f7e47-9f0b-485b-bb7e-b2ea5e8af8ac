import cn from "classnames";
import { getDaysInMonth, addDays, startOfMonth, startOfDay } from "date-fns";
import { AnimatePresence } from "framer-motion";
import Link from "next/link";
import React, {
  FC,
  useCallback,
  RefObject,
  useEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { UserRounded, AltArrowRight } from "solar-icon-set";

import { useActiveSidebarContext } from "../../context/ActiveSidebarContext";
import { useCalendarContext } from "../../context/CalendarContext";
import { useSessionContext } from "../../context/SessionContext";
import {
  Color,
  UpcomingEventFieldsFragment,
  TimeOffEvent,
} from "../../graphql/client.generated";
import { EditableFlatMember } from "../../helpers/flattenMember";
import EmptyCountries from "../../public/svg/holidays/emptyCountries.svg";
import EmptyOrgChart from "../../public/svg/hometab/emptyOrgChart.svg";
import Avatar from "../Avatar";
import EmptyPageState from "../EmptyPageState";
import TimeOffSidebar from "../Sidebar/TimeOffSidebar/TimeOffSidebar";
import TableSkeleton from "../TableSkeleton";
import Tooltip from "../Tooltip";

import CalendarRow from "./CalendarRow";
import DateRow from "./DateRow";

export enum PageType {
  COUNTRIES = "countries",
  DEPARTMENTS = "departments",
  ORGANIZATION = "organization",
  TEAMS = "teams",
}

export type MemberWithCalendarEvents = EditableFlatMember & {
  events: UpcomingEventFieldsFragment[];
};

const CELL_WIDTH = 40;

export interface CalendarRowGroup {
  name: string;
  rows: MemberWithCalendarEvents[];
  color?: Color | null;
  memberQuantity?: number | null;
}

export interface CalendarPageProps {
  groups: CalendarRowGroup[];
  groupedBy: PageType;
  getColor?: (color: Color) => Record<string, boolean>;
  groupTextColor?: "white" | "default";
  showMemberSidebar?: boolean;
  showEventSidebar?: boolean;
  isDataLoading?: boolean;
  hasFilters?: boolean;
}

const Calendar: FC<CalendarPageProps> = ({
  groups,
  groupedBy,
  getColor,
  groupTextColor,
  showMemberSidebar = true,
  showEventSidebar = true,
  isDataLoading,
  hasFilters,
}) => {
  const { selectedDate, headerRef } = useCalendarContext();
  const { activeSidebar, openSidebar } = useActiveSidebarContext();
  const { session } = useSessionContext();

  const daysInMonth = getDaysInMonth(selectedDate);
  const days = Array.from({ length: daysInMonth }).map((_, dayNumber) =>
    addDays(startOfMonth(startOfDay(selectedDate)), dayNumber)
  );

  const [editingEvent, setEditingEvent] = useState<TimeOffEvent | null>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const peopleColumnRef = useRef<HTMLDivElement>(null);

  useSyncScrollbars([calendarRef, headerRef], "horizontal");
  useSyncScrollbars([calendarRef, peopleColumnRef], "vertical");

  const canISeeSidebar = useCallback(
    (row: MemberWithCalendarEvents) =>
      (showMemberSidebar && session?.me.isAdmin) ?? row.id === session?.me.id,
    [session, showMemberSidebar]
  );

  const NoResultsContent = useMemo(() => {
    return {
      [PageType.ORGANIZATION]: null,
      [PageType.COUNTRIES]: (
        <EmptyPageState emptyStateIcon={<EmptyCountries />}>
          <>
            <div className="mb-2">
              Please ensure that the &apos;country&apos; field is enabled in the{" "}
              <Link
                className="underline"
                href="/settings/profile-fields"
                target="_blank"
              >
                settings
              </Link>
            </div>
            <div>
              Also, make sure that users have their country set, which you can
              verify on the{" "}
              <Link className="underline" href="/users" target="_blank">
                users page
              </Link>
            </div>
          </>
        </EmptyPageState>
      ),
      [PageType.DEPARTMENTS]: (
        <EmptyPageState emptyStateIcon={<EmptyOrgChart />}>
          <>
            You don&apos;t have departments yet, but you can{" "}
            <Link
              className="underline"
              href="/org-chart/departments"
              target="_blank"
            >
              create
            </Link>{" "}
            your first one
          </>
        </EmptyPageState>
      ),
      [PageType.TEAMS]: (
        <EmptyPageState emptyStateIcon={<EmptyOrgChart />}>
          <>
            You don&apos;t have teams yet, but you can{" "}
            <Link className="underline" href="/org-chart/teams" target="_blank">
              create
            </Link>{" "}
            your first one
          </>
        </EmptyPageState>
      ),
    };
  }, []);

  return (
    <>
      <AnimatePresence>
        {editingEvent ? (
          <TimeOffSidebar
            id={editingEvent.id}
            onClose={() => setEditingEvent(null)}
          />
        ) : null}
      </AnimatePresence>
      <div className="flex max-w-full flex-shrink-0 flex-col overflow-x-auto overflow-y-hidden">
        <DateRow />
      </div>
      {isDataLoading ? (
        <TableSkeleton />
      ) : !hasFilters && !groups.length ? (
        NoResultsContent[groupedBy]
      ) : null}

      <div
        className={cn("flex flex-col overflow-hidden", {
          hidden: isDataLoading,
        })}
      >
        <div className="relative h-full max-w-full">
          <div className="flex h-full">
            <div
              className="no-scrollbar h-full min-w-[250px] overflow-x-hidden overflow-y-scroll pb-4"
              ref={peopleColumnRef}
            >
              {groups.map((group, index) => (
                <React.Fragment key={group.name + String(index)}>
                  <div
                    className={cn(
                      "top-0 z-40 border-b border-gray-300",
                      {
                        "border-t": index,
                      },
                      group.color && getColor
                        ? getColor(group.color)
                        : "bg-white"
                    )}
                  >
                    <div className="flex h-[44px] w-[250px] min-w-[250px] items-center  px-3 py-1 font-semibold">
                      <span
                        className={cn("pr-1.5", {
                          "text-white":
                            groupTextColor === "white" &&
                            group.name !== session?.me.id,
                        })}
                      >
                        {group.name === session?.me.id ? "You" : group.name}
                      </span>

                      {group.memberQuantity ? (
                        <UserRounded
                          iconStyle="Bold"
                          className={cn({
                            "!text-gray-500": groupTextColor === "default",
                            "!text-white": groupTextColor === "white",
                          })}
                        />
                      ) : null}

                      <span
                        className={cn("pl-1.5", {
                          "text-white": groupTextColor === "white",
                        })}
                      >
                        {group.memberQuantity}
                      </span>
                    </div>
                  </div>

                  {group.rows.map((row, rowIndex) => (
                    <div
                      key={row.id}
                      className={cn("border-r", {
                        "border-b":
                          rowIndex !== group.rows.length - 1 ||
                          index === groups.length - 1,
                      })}
                    >
                      <div
                        className={cn(
                          "relative flex min-h-[45px] w-[250px] items-center gap-2 px-3 py-1 font-semibold",
                          {
                            "border-2 border-r border-t-2 border-violet-600  border-r-inherit bg-[#F5F3FE]":
                              row.id === session?.me.id,
                          }
                        )}
                      >
                        <Avatar
                          className="h-8 w-8"
                          rounded="md"
                          photoUrl={row.photo?.url72 ?? null}
                        />
                        <p className="flex-1 truncate">{row.name}</p>
                        {canISeeSidebar(row) ? (
                          <Tooltip message="Click to view time offs">
                            <div
                              role="button"
                              tabIndex={0}
                              className={cn(
                                "flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded ",
                                {
                                  "bg-violet-500 text-white":
                                    activeSidebar?.type === "member" &&
                                    row.id === activeSidebar.id,
                                  "hover:bg-slate-200":
                                    activeSidebar?.type === "member" &&
                                    row.id !== activeSidebar.id,
                                }
                              )}
                              onClick={() => {
                                openSidebar({
                                  type: "member",
                                  id: row.id,
                                  tabs: [
                                    {
                                      id: "profile",
                                    },
                                    {
                                      id: "timeOffs",
                                      isActive: true,
                                    },
                                    {
                                      id: "activityLog",
                                    },
                                  ],
                                  mode: "edit",
                                  showGoToOrgChartBtn: true,
                                });
                              }}
                              onKeyDown={(e) => e.stopPropagation()}
                            >
                              <AltArrowRight />
                            </div>
                          </Tooltip>
                        ) : null}
                      </div>
                    </div>
                  ))}
                </React.Fragment>
              ))}
            </div>

            <div
              className="no-scrollbar h-full w-full overflow-x-auto overflow-y-auto"
              ref={calendarRef}
            >
              {groups.map((group, index) => (
                <React.Fragment key={group.name + String(index)}>
                  <div
                    style={{ width: CELL_WIDTH * days.length }}
                    className={cn(
                      "top-0 z-40 border-b border-gray-300 bg-white",
                      {
                        "border-t": index,
                      }
                    )}
                  >
                    <div className="h-[44px] w-full" />
                  </div>
                  {group.rows.map((row, rowIndex) => (
                    <div
                      key={row.id}
                      className={cn("w-fit", {
                        "border-b":
                          rowIndex !== group.rows.length - 1 ||
                          index === groups.length - 1,
                      })}
                    >
                      <CalendarRow
                        key={row.id}
                        days={days}
                        events={row.events}
                        isCurrentUserRow={row.id === session?.me.id}
                        onEdit={showEventSidebar ? setEditingEvent : undefined}
                      />
                    </div>
                  ))}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

function useSyncScrollbars(
  refs: RefObject<HTMLElement>[],
  type: "vertical" | "horizontal"
): void {
  useEffect(() => {
    const elements = refs.map((ref) => ref.current);

    if (!elements.every((el): el is HTMLElement => !!el)) {
      return undefined;
    }

    function syncScrollbars(event: Event): void {
      const target = event.target as HTMLDivElement;

      for (const el of elements as HTMLDivElement[]) {
        if (type === "horizontal") {
          el.scrollLeft = target.scrollLeft;
        }

        if (type === "vertical") {
          el.scrollTop = target.scrollTop;
        }
      }
    }

    for (const el of elements) {
      el.addEventListener("scroll", syncScrollbars);
    }

    return () => {
      for (const el of elements) {
        el.removeEventListener("scroll", syncScrollbars);
      }
    };
  }, [refs, type]);
}

export default Calendar;
