import cn from "classnames";
import { isLastDayOfMonth, isWeekend, startOfDay, endOfDay } from "date-fns";
import React, { FC, PropsWithChildren, useMemo } from "react";

import {
  CalendarEvent,
  HolidayEvent,
  TimeOffEvent,
} from "../../graphql/client.generated";
import { dateToDateString } from "../../helpers/date";

import CalendarCelebrationEventCell from "./EventCells/CalendarCelebrationEventCell";
import CalendarHolidayEventCell from "./EventCells/CalendarHolidayEventCell";
import CalendarMultipleEventCell from "./EventCells/CalendarMultipleEventCell";
import CalendarTimeOffEventCell from "./EventCells/CalendarTimeOffEventCell";
import { prepareEventGroups, isHolidayDay } from "./utils";

interface Props {
  days: Date[];
  events: CalendarEvent[];
  isCurrentUserRow: boolean;
  onEdit?: (event: TimeOffEvent) => void;
}

export const monthGridClassnames: Record<number, string> = {
  28: "grid grid-cols-[repeat(28,auto)] w-full max-w-full",
  29: "grid grid-cols-[repeat(29,auto)] w-full max-w-full",
  30: "grid grid-cols-[repeat(30,auto)] w-full max-w-full",
  31: "grid grid-cols-[repeat(31,auto)] w-full max-w-full",
};

const CalendarRow: FC<Props> = ({ days, events, isCurrentUserRow, onEdit }) => {
  const selectedMonth = {
    start: startOfDay(days[0]),
    end: endOfDay(days[days.length - 1]),
  };

  const officialHolidays = useMemo(
    () =>
      events.filter(
        (event): event is HolidayEvent =>
          "isOfficial" in event && event.isOfficial
      ),
    [events]
  );

  return (
    <div className="relative">
      <div className={cn(monthGridClassnames[days.length])}>
        {days.map((day) => (
          <CalendarDay
            key={day.toISOString()}
            day={day}
            isCurrentUserRow={isCurrentUserRow}
            isDayOfficialHoliday={isHolidayDay(day, officialHolidays)}
          />
        ))}
      </div>
      <div className={cn("absolute inset-0", monthGridClassnames[days.length])}>
        {prepareEventGroups(selectedMonth, events).map((group) => {
          const eventType =
            group.eventsNum === 1 ? group.events[0].type.id : "EVENT_GROUP";

          if (group.eventsNum === 1) {
            switch (group.events[0].__typename) {
              case "TimeOffEvent":
                return (
                  <CalendarTimeOffEventCell
                    key={`${eventType}:${dateToDateString(group.startDate)}`}
                    group={group}
                    onEdit={onEdit}
                  />
                );
              case "CelebrationEvent":
                return (
                  <CalendarCelebrationEventCell
                    key={`${eventType}:${dateToDateString(group.startDate)}`}
                    group={group}
                  />
                );
              case "HolidayEvent":
                return (
                  <CalendarHolidayEventCell
                    key={`${eventType}:${dateToDateString(group.startDate)}`}
                    group={group}
                  />
                );
              default:
                break;
            }
          }

          return (
            <CalendarMultipleEventCell
              key={`${eventType}:${dateToDateString(group.startDate)}`}
              group={group}
              onEdit={onEdit}
            />
          );
        })}
      </div>
    </div>
  );
};

interface CalendarDayProps {
  day: Date;
  isCurrentUserRow: boolean;
  isDayOfficialHoliday?: boolean;
}

const CalendarDay: FC<PropsWithChildren<CalendarDayProps>> = ({
  day,
  isCurrentUserRow,
  isDayOfficialHoliday,
  children,
}) => {
  return (
    <div
      className={cn("h-[45px] min-w-[40px] border-r", {
        "bg-main-100": isWeekend(day) || isDayOfficialHoliday,
        "border-b-2 border-t-2 border-b-violet-600 border-t-violet-600":
          isCurrentUserRow,
        "bg-[#F5F3FE]":
          isCurrentUserRow && (!isWeekend(day) || !isDayOfficialHoliday),
        "bg-[#EEEEFB]":
          isCurrentUserRow && (isWeekend(day) || isDayOfficialHoliday),
        "border-r-2 border-r-violet-600":
          isCurrentUserRow && isLastDayOfMonth(day),
      })}
    >
      {children}
    </div>
  );
};

export default CalendarRow;
