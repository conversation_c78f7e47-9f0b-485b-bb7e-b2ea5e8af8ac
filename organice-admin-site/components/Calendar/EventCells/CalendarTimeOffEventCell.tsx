import cn from "classnames";
import { intervalToDuration, getDate } from "date-fns";
import React, { FC, useMemo } from "react";

import { TimeOffEvent } from "../../../graphql/client.generated";
import { TimeOffRequestStatus } from "../../../graphql/server.generated";
import { getEventsColors } from "../../../helpers/colors";
import Tooltip from "../../Tooltip";
import { EventGroup, WithInnerGroupFlag, WithText } from "../utils";

interface CalendarTimeOffEventCellProps {
  group: WithInnerGroupFlag<WithText<EventGroup>>;
  onEdit?: (event: TimeOffEvent) => void;
}

const CalendarTimeOffEventCell: FC<CalendarTimeOffEventCellProps> = ({
  group,
  onEdit,
}) => {
  const { textStartDay, textLengthInDays, isInnerGroup } = group;
  const currentEvent = group.events[0] as TimeOffEvent;
  const { type, status } = currentEvent;

  const editable = useMemo(() => {
    return currentEvent.editable && onEdit;
  }, [currentEvent.editable, onEdit]);

  const handleEdit = (event: TimeOffEvent): void => {
    if (event.editable && onEdit) {
      onEdit(event);
    }
  };

  const startDay = getDate(group.startDate);
  const eventDuration =
    (intervalToDuration({
      start: group.startDate,
      end: group.endDate,
    }).days ?? 0) + 1;

  const isPending = status === TimeOffRequestStatus.Pending;

  return (
    <div
      className="absolute my-1 h-[40px] w-full self-center"
      style={{
        gridRow: 1,
        gridColumnStart: startDay,
        gridColumnEnd: `span ${eventDuration}`,
      }}
    >
      {/* Wrapper */}
      <div
        className={cn(
          "relative flex h-full w-full items-center justify-center border-[1px] text-center",
          !isInnerGroup && "z-10",
          isInnerGroup && "z-20 border-y-4",
          getEventsColors(type.color),
          editable && "cursor-pointer"
        )}
        style={{
          gridRow: 1,
          gridColumnStart: textStartDay,
          gridColumnEnd: `span ${textLengthInDays}`,
        }}
      >
        {/* Background */}
        {isPending && (
          <div className="bg-stripped absolute left-0 top-0 z-10 h-full w-full overflow-hidden" />
        )}

        {/* Tooltip */}
        <Tooltip
          variant="light"
          className="z-50 h-full !w-full"
          onMouseDown={() => handleEdit(currentEvent)}
          message={
            isPending ? (
              <span>Pending...</span>
            ) : textLengthInDays <= 5 ? (
              type.label
            ) : undefined
          }
        >
          <div className="h-[45px] w-full" />
        </Tooltip>

        {/* Title */}
        <span className="absolute z-20 flex w-full justify-center gap-1 font-semibold">
          {type.emoji}
          {textLengthInDays > 5 && (
            <span className="text-black">{type.label}</span>
          )}
        </span>
      </div>
    </div>
  );
};

export default CalendarTimeOffEventCell;
