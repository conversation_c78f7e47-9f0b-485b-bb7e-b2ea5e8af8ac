import cn from "classnames";
import { intervalToDuration, getDate } from "date-fns";
import React, { FC } from "react";

import { CelebrationEvent } from "../../../graphql/client.generated";
import { getEventsColors } from "../../../helpers/colors";
import Tooltip from "../../Tooltip";
import { EventGroup, WithInnerGroupFlag, WithText } from "../utils";

interface CalendarCelebrationEventCellProps {
  group: WithInnerGroupFlag<WithText<EventGroup>>;
}

const CalendarCelebrationEventCell: FC<CalendarCelebrationEventCellProps> = ({
  group,
}) => {
  const { textStartDay, textLengthInDays, isInnerGroup } = group;
  const { type } = group.events[0] as CelebrationEvent;

  const startDay = getDate(group.startDate);
  const eventDuration =
    (intervalToDuration({
      start: group.startDate,
      end: group.endDate,
    }).days ?? 0) + 1;

  return (
    <div
      className="absolute my-1 h-[40px] w-full self-center"
      style={{
        gridRow: 1,
        gridColumnStart: startDay,
        gridColumnEnd: `span ${eventDuration}`,
      }}
    >
      {/* Wrapper */}
      <div
        className={cn(
          "relative flex h-full w-full items-center justify-center border-[1px] text-center",
          !isInnerGroup && "z-10",
          isInnerGroup && "z-20 border-y-4",
          getEventsColors(type.color)
        )}
        style={{
          gridRow: 1,
          gridColumnStart: textStartDay,
          gridColumnEnd: `span ${textLengthInDays}`,
        }}
      >
        {/* Tooltip */}
        <Tooltip
          variant="light"
          className="z-50 h-full !w-full"
          message={type.label}
        >
          <div className="h-[45px] w-full" />
        </Tooltip>

        {/* Title */}
        <span className="absolute z-20 w-full font-semibold ">
          {type.emoji}
        </span>
      </div>
    </div>
  );
};

export default CalendarCelebrationEventCell;
