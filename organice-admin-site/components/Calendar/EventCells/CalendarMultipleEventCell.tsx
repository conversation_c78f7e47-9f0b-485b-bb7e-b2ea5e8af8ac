import cn from "classnames";
import { intervalToDuration, getDate } from "date-fns";
import React, { FC, useState } from "react";

import { TimeOffEvent } from "../../../graphql/client.generated";
import { TimeOffRequestStatus } from "../../../graphql/server.generated";
import { getEventsColors } from "../../../helpers/colors";
import SimpleDropDown from "../../SimpleDropDown";
import Tooltip from "../../Tooltip";
import { EventGroup, WithInnerGroupFlag, WithText } from "../utils";

interface CalendarMultipleEventCellProps {
  group: WithInnerGroupFlag<WithText<EventGroup>>;
  onEdit?: (event: TimeOffEvent) => void;
}

const CalendarMultipleEventCell: FC<CalendarMultipleEventCellProps> = ({
  group,
  onEdit,
}) => {
  const [dropdownMenuOpen, setDropdownMenuOpen] = useState(false);

  const { textStartDay, textLengthInDays, isInnerGroup } = group;

  const handleEdit = (event: TimeOffEvent, shouldEdit: boolean): void => {
    if ("editable" in event && event.editable && shouldEdit && onEdit) {
      onEdit(event);
    }
  };

  const startDay = getDate(group.startDate);
  const eventDuration =
    (intervalToDuration({
      start: group.startDate,
      end: group.endDate,
    }).days ?? 0) + 1;

  return (
    <div
      className="absolute my-1 h-[40px] w-full self-center"
      style={{
        gridRow: 1,
        gridColumnStart: startDay,
        gridColumnEnd: `span ${eventDuration}`,
      }}
    >
      {/* Wrapper */}
      <div
        className={cn(
          "relative flex h-full w-full items-center justify-center border-[1px] text-center",
          "border-main-400 text-main-900",
          !isInnerGroup && "z-10",
          isInnerGroup && "z-20 border-y-4",
          group.events.find(
            (event) => "editable" in event && event.editable && onEdit
          ) && "cursor-pointer"
        )}
        style={{
          gridRow: 1,
          gridColumnStart: textStartDay,
          gridColumnEnd: `span ${textLengthInDays}`,
        }}
      >
        {/* Tooltip */}
        <Tooltip
          variant="light"
          className="z-50 h-full !w-full"
          hide={dropdownMenuOpen}
          tooltipClassName="text-left"
          message={
            textLengthInDays <= 5 ? (
              <div className="flex flex-col items-start gap-1">
                {group.events.map((event) => (
                  <p key={event.id}>
                    <span className="mr-1">{event.type.emoji}</span>
                    <span className={cn(getEventsColors(event.type.color))}>
                      {"title" in event ? event.title : event.type.label}
                    </span>
                    {"status" in event &&
                      event.status === TimeOffRequestStatus.Pending && (
                        <span>&nbsp;(Pending)</span>
                      )}
                    {"isOfficial" in event && event.isOfficial && (
                      <span>&nbsp;(Official holiday)</span>
                    )}
                  </p>
                ))}
              </div>
            ) : undefined
          }
        >
          {group.events.reduce(
            (acc, cur) => acc || "editable" in cur,
            false
          ) && (
            <SimpleDropDown
              className="!absolute inset-0 cursor-pointer"
              onOpenStateChange={setDropdownMenuOpen}
              items={[
                ...group.events
                  .filter((event) => "editable" in event && event.editable)
                  .map((event) => ({
                    key: event.id,
                    label: `Edit ${event.type.label}`,
                    onClick: () => handleEdit(event as TimeOffEvent, true),
                  })),
              ]}
              showAttachmentTriangle={false}
            >
              {(props) => <div {...props} className="h-full w-full" />}
            </SimpleDropDown>
          )}
          <div className="h-[45px] w-full" />
        </Tooltip>

        {/* Title */}
        <span className="absolute z-20 w-full text-2xl font-semibold">
          {group.eventsNum}
        </span>
      </div>
    </div>
  );
};

export default CalendarMultipleEventCell;
