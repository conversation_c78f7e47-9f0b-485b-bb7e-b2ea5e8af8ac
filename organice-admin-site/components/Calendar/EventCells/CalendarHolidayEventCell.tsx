import cn from "classnames";
import { intervalToDuration, getDate } from "date-fns";
import React, { FC } from "react";

import { HolidayEvent } from "../../../graphql/client.generated";
import Tooltip from "../../Tooltip";
import { EventGroup, WithInnerGroupFlag, WithText } from "../utils";

interface CalendarHolidayEventCellProps {
  group: WithInnerGroupFlag<WithText<EventGroup>>;
}

const CalendarHolidayEventCell: FC<CalendarHolidayEventCellProps> = ({
  group,
}) => {
  const { textLengthInDays } = group;
  const { type, title, isOfficial } = group.events[0] as HolidayEvent;

  const startDay = getDate(group.startDate);
  const eventDuration =
    (intervalToDuration({
      start: group.startDate,
      end: group.endDate,
    }).days ?? 0) + 1;

  return (
    <div
      className="absolute h-full w-full"
      style={{
        gridRow: 1,
        gridColumnStart: startDay,
        gridColumnEnd: `span ${eventDuration}`,
      }}
    >
      {/* Wrapper */}
      <div
        className={cn(
          "relative flex h-full w-full items-center justify-center"
        )}
      >
        {/* Tooltip */}
        <Tooltip
          variant="light"
          className="z-50 h-full !w-full"
          message={isOfficial ? `${title} (Official holiday)` : title}
        >
          <div className="h-[45px] w-full" />
        </Tooltip>

        {/* Title */}
        <span className="absolute z-20 flex w-full justify-center gap-1 font-semibold">
          {type.emoji}
          {textLengthInDays > 5 && <span className="text-black">{title}</span>}
        </span>
      </div>
    </div>
  );
};

export default CalendarHolidayEventCell;
