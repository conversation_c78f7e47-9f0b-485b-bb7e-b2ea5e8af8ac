import {
  areIntervalsOverlapping,
  clamp,
  eachDayOfInterval,
  endOfDay,
  Interval,
  isSameDay,
  isWithinInterval,
  startOfDay,
} from "date-fns";
import { groupBy, partition } from "lodash";

import {
  CalendarEvent,
  HolidayEvent,
  TimeOffRequestStatus,
} from "../../graphql/client.generated";
import { dateToDateString } from "../../helpers/date";

export interface EventGroup {
  eventsNum: number;
  events: CalendarEvent[];
  startDate: Date;
  endDate: Date;
  status: TimeOffRequestStatus;
}

export type WithText<T extends EventGroup> = T & {
  textStartDay: number;
  textEndDay: number;
  textLengthInDays: number;
};

export type WithInnerGroupFlag<T extends EventGroup> = T & {
  isInnerGroup: boolean;
};

export function prepareEventGroups(
  month: Interval,
  events: CalendarEvent[]
): WithInnerGroupFlag<WithText<EventGroup>>[] {
  const clipped = clipByMonth(month, events);
  const grouped = groupSameDayEvents(clipped);
  const withTextInfo = attachTextPlacementInfo(grouped);
  const withInnerGroupFlags = attachInnerGroupFlag(withTextInfo);

  return withInnerGroupFlags;
}

/*
 * Leave only those events that fit on the current calendar page, cut their beginning and end
 */
export function clipByMonth(
  month: Interval,
  events: CalendarEvent[]
): CalendarEvent[] {
  return events
    .map((event) => {
      const eventInterval = {
        start: startOfDay(event.startDate),
        end: endOfDay(event.endDate),
      };

      if (!areIntervalsOverlapping(eventInterval, month)) {
        return null;
      }

      return {
        ...event,
        startDate: clamp(eventInterval.start, month),
        endDate: clamp(eventInterval.end, month),
      };
    })
    .filter((v): v is CalendarEvent => !!v);
}

/*
 * If two or more one-day events happen on the same day, they're displayed in a single group
 */
export function groupSameDayEvents(events: CalendarEvent[]): EventGroup[] {
  const [oneDayEvents, multipleDayEvents] = partition(events, isOneDayEvent);
  const groupedOneDayEvents = groupBy(
    oneDayEvents,
    (event) =>
      `${dateToDateString(event.startDate)}${dateToDateString(event.endDate)}`
  );

  return [...multipleDayEvents, ...Object.values(groupedOneDayEvents)].map(
    (eventOrGroup: CalendarEvent | CalendarEvent[]) => {
      const groupEvents =
        "length" in eventOrGroup ? eventOrGroup : [eventOrGroup];
      const { startDate, endDate, type } = groupEvents[0];
      const status =
        "status" in groupEvents[0]
          ? groupEvents[0].status
          : TimeOffRequestStatus.Approved;
      const groupStatus =
        groupEvents.length > 1 ? TimeOffRequestStatus.Approved : status;

      return {
        eventsNum: groupEvents.length,
        events: groupEvents,
        startDate,
        endDate,
        type,
        status: groupStatus,
      };
    }
  );
}

/*
 * When a long event has one or more overlapping one-day events,
 * the text should be displayed in the longest empty space
 */
export function attachTextPlacementInfo<T extends EventGroup>(
  groups: T[]
): WithText<T>[] {
  const [oneDayGroups, multipleDayGroups] = partition(groups, isOneDayEvent);

  const oneDayGroupsWithTextPlacementInfo = oneDayGroups.map((group) => ({
    ...group,
    textStartDay: startOfDay(group.startDate).getDate(),
    textEndDay: startOfDay(group.endDate).getDate(),
    textLengthInDays: 1,
  }));

  const oneDayEventDates = oneDayGroups.map((group) =>
    startOfDay(group.startDate)
  );

  const groupsWithTextPlacementInfo = multipleDayGroups.map((group) => {
    const longEventInterval = {
      start: startOfDay(group.startDate),
      end: endOfDay(group.endDate),
    };

    const textPlacement = findLongestEmptySubinterval(
      longEventInterval,
      oneDayEventDates
    );

    const defaultStart = longEventInterval.start.getDate();
    const defaultEnd = longEventInterval.end.getDate();
    const defaultLength = defaultEnd - defaultStart + 1;

    return {
      ...group,
      textStartDay: textPlacement.start?.getDate() ?? defaultStart,
      textEndDay: textPlacement.end?.getDate() ?? defaultEnd,
      textLengthInDays: textPlacement.length || defaultLength,
    };
  });

  return [...groupsWithTextPlacementInfo, ...oneDayGroupsWithTextPlacementInfo];
}

interface DateRange {
  start: Date | null;
  end: Date | null;
  length: number;
}

/*
 * When a one-day event is inside a long event, it should be marked as inner event
 */
export function attachInnerGroupFlag<T extends EventGroup>(
  groups: T[]
): WithInnerGroupFlag<T>[] {
  const [oneDayGroups, multipleDayGroups] = partition(groups, isOneDayEvent);

  const flaggedMultipleDayGroups = multipleDayGroups.map((group) => ({
    ...group,
    isInnerGroup: false,
  }));

  const flaggedOneDayGroups = oneDayGroups.map((group) => {
    const startDate = startOfDay(group.startDate);

    return {
      ...group,
      isInnerGroup: multipleDayGroups.some((longEvent) => {
        const eventInterval = {
          start: startOfDay(longEvent.startDate),
          end: endOfDay(longEvent.endDate),
        };

        return isWithinInterval(startDate, eventInterval);
      }),
    };
  });

  return [...flaggedMultipleDayGroups, ...flaggedOneDayGroups];
}

/*
 * Returns the longest subinterval that doesn't contain any one-day events
 */
function findLongestEmptySubinterval(
  longEventInterval: Interval,
  oneDayEventDates: Date[]
): DateRange {
  const empty: DateRange = {
    start: null,
    end: null,
    length: 0,
  };

  let currentSubinterval = { ...empty };
  let longestSubinterval = { ...empty };

  for (const currentDay of eachDayOfInterval(longEventInterval)) {
    const isOccupiedByAnotherEvent = oneDayEventDates.some((eventDate) =>
      isSameDay(currentDay, eventDate)
    );

    if (isOccupiedByAnotherEvent) {
      currentSubinterval = { ...empty };
      // Don't want to add another level of nesting with "else"
      // eslint-disable-next-line no-continue
      continue;
    }

    currentSubinterval.length += 1;

    if (!currentSubinterval.start) {
      currentSubinterval.start = currentDay;
    }
    currentSubinterval.end = currentDay;

    if (currentSubinterval.length > longestSubinterval.length) {
      longestSubinterval = { ...currentSubinterval };
    }
  }

  return longestSubinterval;
}

function isOneDayEvent(event: { startDate: Date; endDate: Date }): boolean {
  return isSameDay(event.startDate, event.endDate);
}

export function isHolidayDay(day: Date, holidays: HolidayEvent[]): boolean {
  return holidays.some((holiday) =>
    isWithinInterval(day, {
      start: holiday.startDate,
      end: holiday.endDate,
    })
  );
}
