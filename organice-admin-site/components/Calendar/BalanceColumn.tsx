import {
  useFloating,
  offset,
  flip,
  shift,
  autoUpdate,
  FloatingPortal,
} from "@floating-ui/react";
import { useState, useEffect, ForwardedRef, forwardRef } from "react";
import { CellProps, CellComponent, floatColumn } from "react-datasheet-grid";

import { DayOfWeek } from "../../graphql/client.generated";
import TypePolicyShortInfo, { TypePolicyData } from "../TypePolicyShortInfo";

export interface BalanceData {
  balance: number;
  workDays: DayOfWeek[];
  includedWeekendDays: DayOfWeek[];
  typePolicy: TypePolicyData | null;
  nextResetAt: Date | null;
}

export interface BalanceOptions {
  placeholder?: string;
}

const defaultFloatColumn = floatColumn;

interface Props {
  typePolicy: TypePolicyData;
  workDays: DayOfWeek[];
  includedWeekendDays: DayOfWeek[];
  style: React.CSSProperties;
  nextResetAt: Date | null;
}

const TypePolicyInfo = forwardRef(
  (
    { typePolicy, workDays, includedWeekendDays, style, nextResetAt }: Props,
    ref: ForwardedRef<HTMLDivElement>
  ) => {
    return (
      <div
        ref={ref}
        style={style}
        className="rounded-md border border-gray-300 bg-white p-2 text-xs  shadow-primary"
      >
        <TypePolicyShortInfo
          typePolicy={typePolicy}
          policy={{ workDays, includedWeekendDays }}
          nextResetAt={nextResetAt}
        />
      </div>
    );
  }
);

const BalanceColumn = ({
  rowData,
  columnData,
  active,
  disabled,
  ...args
}: CellProps<BalanceData, BalanceOptions>): JSX.Element => {
  const FloatComponent = defaultFloatColumn.component as CellComponent;
  const [delayedActive, setDelayedActive] = useState(false);

  /**
   * Set Active with a delay so TextComponent can handle data change before
   * it will be destroyed and so a user can trigger a link before it will be
   * destroyed
   */
  useEffect(() => {
    setDelayedActive(active);
  }, [active]);

  const { refs, floatingStyles } = useFloating({
    open: true,
    strategy: "fixed",
    placement: "bottom",
    middleware: [
      offset(12),
      flip({
        padding: 9,
      }),
      shift({ padding: 9 }),
    ],
    whileElementsMounted: autoUpdate,
  });

  return delayedActive && !disabled ? (
    <>
      <div ref={refs.setReference} className="flex w-full">
        <FloatComponent
          rowData={rowData.balance || undefined}
          columnData={{
            ...defaultFloatColumn.columnData,
            placeholder: "Start typing a number",
            continuousUpdates: false,
          }}
          disabled={disabled}
          active
          {...args}
        />
      </div>
      {rowData.typePolicy ? (
        <FloatingPortal>
          <TypePolicyInfo
            ref={refs.setFloating}
            style={floatingStyles}
            workDays={rowData.workDays}
            includedWeekendDays={rowData.includedWeekendDays}
            typePolicy={rowData.typePolicy}
            nextResetAt={rowData.nextResetAt}
          />
        </FloatingPortal>
      ) : null}
    </>
  ) : rowData.typePolicy ? (
    <div className="w-full px-2 text-right text-sm">
      <span className="text-xs text-gray-500">Available days</span>{" "}
      {rowData.balance}
      {rowData.typePolicy.maxCapacity ? (
        <>
          {" "}
          <span className="text-xs text-gray-500">up to </span>
          {rowData.typePolicy.maxCapacity}
        </>
      ) : null}
    </div>
  ) : (
    <div className="dsg-cell-disabled w-full px-2 text-right">N/A</div>
  );
};

export default BalanceColumn;
