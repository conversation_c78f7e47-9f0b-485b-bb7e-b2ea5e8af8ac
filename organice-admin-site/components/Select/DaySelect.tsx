import { FC } from "react";

import { DayOfWeek } from "../../graphql/client.generated";
import { dayOfWeekLabels } from "../Settings/TimeOffs/consts";

import Select, { Props as SelectProps, Option } from ".";

export interface DayOption extends Option {
  disabled: boolean;
  value: DayOfWeek;
}

export const dayOptions: DayOption[] = [
  {
    value: DayOfWeek.Monday,
    title: dayOfWeekLabels[DayOfWeek.Monday],
    disabled: false,
  },
  {
    value: DayOfWeek.Tuesday,
    title: dayOfWeekLabels[DayOfWeek.Tuesday],
    disabled: false,
  },
  {
    value: DayOfWeek.Wednesday,
    title: dayOfWeekLabels[DayOfWeek.Wednesday],
    disabled: false,
  },
  {
    value: DayOfWeek.Thursday,
    title: dayOfWeekLabels[DayOfWeek.Thursday],
    disabled: false,
  },
  {
    value: DayOfWeek.Friday,
    title: dayOfWeekLabels[DayOfWeek.Friday],
    disabled: false,
  },
  {
    value: DayOfWeek.Saturday,
    title: dayOfWeekLabels[DayOfWeek.Saturday],
    disabled: false,
  },
  {
    value: DayOfWeek.Sunday,
    title: dayOfWeekLabels[DayOfWeek.Sunday],
    disabled: false,
  },
];

type Props = Omit<SelectProps, "options" | "renderOption">;

const DaySelect: FC<Props> = (props) => {
  return (
    <Select
      {...props}
      value={dayOptions.find((x) => x.value === props.value?.value)!}
      options={dayOptions}
      onChange={(option): void => {
        props.onChange(option);
      }}
    />
  );
};

export default DaySelect;
