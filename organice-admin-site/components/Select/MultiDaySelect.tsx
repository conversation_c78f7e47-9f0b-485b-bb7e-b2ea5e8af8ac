import cn from "classnames";
import { FC } from "react";

import { DayOfWeek } from "../../graphql/client.generated";
import Checkbox from "../Checkbox";

import { dayOptions, DayOption } from "./DaySelect";

import Select, { Props as SelectProps } from ".";

type Props = Omit<
  SelectProps<DayOption, true>,
  "options" | "renderOption" | "value" | "onChange"
> & {
  disabledOptions?: DayOfWeek[];
  value: DayOfWeek[];
  onChange: (value: DayOfWeek[]) => void;
};

const MultiDaySelect: FC<Props> = ({
  value,
  disabledOptions,
  onChange,
  ...props
}) => {
  const options = dayOptions.map<DayOption>((option) => ({
    ...option,
    disabled: disabledOptions?.includes(option.value) ?? false,
  }));

  return (
    <Select<DayOption, true>
      {...props}
      value={dayOptions.filter((d) => value.some((v) => d.value === v))}
      options={options}
      onChange={(newValue): void => {
        onChange(
          newValue?.filter((o) => !o.disabled).map((o) => o.value) ?? []
        );
      }}
      renderOption={({ active, selected, option, capitalize }) => {
        return (
          <div
            data-test={`day-select-${option.value}`}
            className={cn(
              "relative block cursor-pointer select-none truncate px-3 py-1 text-base text-slate-900",
              active && "bg-slate-100",
              selected ? "font-medium" : "font-normal",
              capitalize && "capitalize"
            )}
          >
            <Checkbox
              checked={selected || option.disabled}
              onChange={() => {}}
              disabled={option.disabled}
            >
              {option.title}
            </Checkbox>
          </div>
        );
      }}
    />
  );
};

export default MultiDaySelect;
