/* eslint-disable react/jsx-no-useless-fragment */
import Link from "next/link";
import { useMemo, FC } from "react";
import { AltArrowRight } from "solar-icon-set";

import { SurveyCardFieldsFragment } from "../../graphql/client.generated";
import Avatar from "../Avatar";
import { VirtualizedTable, VirtualizedTableColumn } from "../Table/Table";
import TitleCell from "../Users/<USER>";

import SurveyActionDropdown from "./SurveyActionDropdown";
import SurveyStatusBadge from "./SurveyStatusBadge";

interface SurveysTableProps {
  surveys: SurveyCardFieldsFragment[];
  sort?: string;
  onSortChange?: (sort: string) => void;
}

const SurveyNameCell: FC<SurveyCardFieldsFragment> = (row) => (
  <Link
    href={`/surveys/${row.id}`}
    className="flex w-full items-center justify-between gap-3 whitespace-nowrap [&_[role=button]]:hover:bg-slate-200"
  >
    <span className="truncate font-medium">{row.title}</span>
    <div
      role="button"
      className="flex h-[24px] w-[24px] cursor-pointer items-center justify-center rounded"
    >
      <AltArrowRight />
    </div>
  </Link>
);

const TemplateCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full truncate">
    {row.template ? row.template.title : "From scratch"}
  </div>
);

const CreatedByCell: FC<SurveyCardFieldsFragment> = (row) =>
  row.createdBy ? (
    <div className="flex w-full items-center gap-2 whitespace-nowrap">
      <Avatar size="s" photoUrl={row.createdBy.photo72Url} />
      <span className="truncate">{row.createdBy.realName}</span>
    </div>
  ) : (
    <div className="px-3" />
  );

const FrequencyCell: FC = () => <div className="flex w-full">Single-Use</div>;

const ChannelsCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full">#{row.channelName}</div>
);

const CreatedAtCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full justify-end">
    {row.createdAt.toLocaleDateString()}
  </div>
);

const CompletionRateCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full justify-end">{row.completionRate}%</div>
);

const StatusCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full">
    <SurveyStatusBadge status={row.status} />
  </div>
);

const ActionsCell: FC<SurveyCardFieldsFragment> = (row) => (
  <div className="flex w-full justify-center">
    <SurveyActionDropdown survey={row} />
  </div>
);

const SurveysTable: React.FC<SurveysTableProps> = ({
  surveys,
  sort = "title:asc",
  onSortChange = () => {},
}) => {
  const columns: VirtualizedTableColumn<SurveyCardFieldsFragment>[] = useMemo(
    () => [
      {
        id: "title",
        header: (
          <TitleCell
            title="Name of survey"
            field="title"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: SurveyNameCell,
        width: 400,
      },
      {
        id: "template",
        header: (
          <TitleCell
            title="Based on"
            field="template"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: TemplateCell,
        width: 180,
      },
      {
        id: "createdBy",
        header: (
          <TitleCell
            title="Created by"
            field="createdBy"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: CreatedByCell,
        width: 200,
      },
      {
        id: "frequency",
        header: (
          <TitleCell
            title="Frequency"
            field="frequency"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: FrequencyCell,
        width: 120,
      },
      {
        id: "channels",
        header: (
          <TitleCell
            title="Published in"
            field="channels"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: ChannelsCell,
        width: 180,
      },
      {
        id: "createdAt",
        header: (
          <TitleCell
            title="Last published date"
            field="createdAt"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: CreatedAtCell,
        width: 180,
      },
      {
        id: "completionRate",
        header: (
          <TitleCell
            title="% Completion rate"
            field="completionRate"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: CompletionRateCell,
        width: 170,
      },
      {
        id: "status",
        header: (
          <TitleCell
            title="Status"
            field="status"
            sortValue={sort}
            sort={onSortChange}
          />
        ),
        cell: StatusCell,
        width: 124,
      },
      {
        id: "actions",
        header: <div className="px-3" />,
        cell: ActionsCell,
        width: 56,
      },
    ],
    [sort, onSortChange]
  );

  return (
    <VirtualizedTable<SurveyCardFieldsFragment>
      data={surveys}
      columns={columns}
      stickyHeader
      stickyFirstColumn
      className="flex-grow"
    />
  );
};

export default SurveysTable;
