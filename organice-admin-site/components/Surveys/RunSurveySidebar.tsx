import React from "react";
import { createPortal } from "react-dom";

import { useRunSurveyModalQuery } from "../../graphql/client.generated";
import Button from "../Button";
import ExpandableInput from "../ExpandableInput";
import Sidebar from "../Sidebar/Sidebar";
import { Tab } from "../Tab";
import Tooltip from "../Tooltip";

import RunSurveyParticipants from "./RunSurveyParticipants";
import RunSurveySettings from "./RunSurveySettings";
import RunSurveyTemplateSelector from "./RunSurveyTemplateSelector";
import { SurveyQuestionsForm } from "./SurveyQuestionsForm";
import useQuestionsController from "./useQuestionsController";
import useRunSurveyState from "./useRunSurveyState";

const tabs = [
  {
    id: "questions" as const,
    title: "Questions",
  },
  {
    id: "participants" as const,
    title: "Participants",
  },
  {
    id: "settings" as const,
    title: "Settings",
  },
];

interface RunSurveySidebarProps {
  onClose: () => void;
}

const RunSurveySidebar: React.FC<RunSurveySidebarProps> = ({ onClose }) => {
  const { data, loading } = useRunSurveyModalQuery();

  const {
    status,
    formData,
    validationError,
    submitting,
    startFromScratch,
    startSetup,
    activeTab,
    setActiveTab,
    setTemplate,
    setChannel,
    setAnonymous,
    setSurveyName,
    setQuestions,
    handleSubmit,
  } = useRunSurveyState({
    onSubmit: onClose,
  });

  const questionsController = useQuestionsController({
    questions: formData.questions,
    onChange: setQuestions,
  });

  return createPortal(
    <Sidebar
      size="large"
      menu={[]}
      headerClassName="!px-6"
      closeSidebar={onClose}
      title={
        status === "setup" ? (
          <ExpandableInput
            maxLength={200}
            focusInitially
            className="px-4"
            placeholder="Enter survey title"
            value={formData.surveyTitle}
            onChange={setSurveyName}
          />
        ) : undefined
      }
      footer={
        <div className="flex justify-end gap-5">
          <Button color="secondary" variant="outline" onClick={onClose}>
            Cancel
          </Button>

          {status === "idle" && (
            <Tooltip
              message="Please select any template"
              hide={!!formData.template}
            >
              <Button
                disabled={!formData.template}
                loading={submitting}
                onClick={startSetup}
              >
                Next
              </Button>
            </Tooltip>
          )}

          {status === "setup" && (
            <Tooltip message={validationError} hide={!validationError}>
              <Button
                disabled={!!validationError}
                loading={submitting}
                onClick={handleSubmit}
              >
                Run
              </Button>
            </Tooltip>
          )}
        </div>
      }
    >
      <form className="flex h-full flex-col items-stretch">
        {status === "idle" && (
          <RunSurveyTemplateSelector
            loading={loading}
            templates={data?.surveyTemplates ?? []}
            selectedTemplate={formData.template}
            setTemplate={setTemplate}
            onStartButtonClick={startFromScratch}
          />
        )}

        {status === "setup" && (
          <>
            <div className="border-b border-b-main-300 py-3 px-6">
              {tabs.map((tab) => {
                return (
                  <Tab
                    key={tab.id}
                    isActive={() => activeTab === tab.id}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.title}
                  </Tab>
                );
              })}
            </div>

            {activeTab === "questions" && (
              <SurveyQuestionsForm controller={questionsController} editable />
            )}

            {activeTab === "participants" && (
              <RunSurveyParticipants
                selectedChannel={formData.channel}
                setChannel={setChannel}
                channels={data?.channels ?? []}
              />
            )}

            {activeTab === "settings" && (
              <RunSurveySettings
                isAnonymous={formData.isAnonymous}
                setAnonymous={setAnonymous}
              />
            )}
          </>
        )}
      </form>
    </Sidebar>,
    document.body
  );
};

export default RunSurveySidebar;
