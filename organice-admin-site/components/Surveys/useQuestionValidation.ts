import { useCallback } from "react";

import { VALIDATION_MESSAGES } from "./constants";
import { Question } from "./types";
import { isQuestionWithChoices } from "./utils";

interface UseQuestionValidationReturn {
  validateQuestion: (question: Question) => Question["validation"];
  areAllQuestionsValid: (questions: Question[]) => boolean;
  validateQuestions: (questions: Question[]) => Question[];
  hasErrorOfType: (
    question: Question,
    errorType: "title" | "options"
  ) => boolean;
  getOptionError: (question: Question, optionId: string) => string | null;
}

const useQuestionValidation = (): UseQuestionValidationReturn => {
  const validateQuestion = useCallback(
    (question: Question): Question["validation"] => {
      const textOptions = isQuestionWithChoices(question)
        ? question.textOptions
        : [];
      const titleEmpty = question.title.trim().length === 0;

      const validationErrors = {
        titleError: titleEmpty ? VALIDATION_MESSAGES.TITLE_ERROR : null,
        optionsErrors: textOptions
          .filter((option) => !option.value.trim().length)
          .map((option) => ({
            key: option.key,
            error: VALIDATION_MESSAGES.OPTION_ERROR,
          })),
      };

      const isValid =
        !validationErrors.titleError &&
        validationErrors.optionsErrors.length === 0;

      return {
        isValid,
        validationErrors,
      };
    },
    []
  );

  const areAllQuestionsValid = useCallback(
    (questions: Question[]): boolean => {
      return questions.every((question) => {
        const validation = validateQuestion(question);

        return Boolean(validation?.isValid);
      });
    },
    [validateQuestion]
  );

  const validateQuestions = useCallback(
    (questions: Question[]): Question[] => {
      return questions.map((question) => {
        if (!question.wasTouched) {
          return question;
        }

        const validation = validateQuestion(question);

        return { ...question, validation };
      });
    },
    [validateQuestion]
  );

  const hasErrorOfType = useCallback(
    (question: Question, errorType: "title" | "options"): boolean => {
      if (!question.validation?.validationErrors) return false;

      switch (errorType) {
        case "title":
          return !!question.validation.validationErrors.titleError;
        case "options":
          return question.validation.validationErrors.optionsErrors.length > 0;
        default:
          return false;
      }
    },
    []
  );

  const getOptionError = useCallback(
    (question: Question, optionKey: string): string | null => {
      if (!question.validation?.validationErrors) return null;

      const optionError =
        question.validation.validationErrors.optionsErrors.find(
          (err) => err.key === optionKey
        );

      return optionError?.error ?? null;
    },
    []
  );

  return {
    validateQuestion,
    areAllQuestionsValid,
    validateQuestions,
    hasErrorOfType,
    getOptionError,
  };
};

export default useQuestionValidation;
