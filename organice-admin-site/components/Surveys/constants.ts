import { SurveyQuestionType } from "../../graphql/client.generated";

import { Question } from "./types";
import { generateId } from "./utils";

export const SURVEY_STATUS_PARAM_NAME = "survey_status";
export const PARTICIPANT_PARAM_NAME = "responder_id";

export const SURVEY_QUESTION_TYPE_MAP = {
  [SurveyQuestionType.SingleOption]: "Single choice",
  [SurveyQuestionType.MultipleOptions]: "Multiple choice",
  [SurveyQuestionType.Scale]: "1 to 5 scale",
  [SurveyQuestionType.Text]: "Paragraph",
};

export const SCALE_OPTION_LABELS: Record<number, string> = {
  1: "Strongly Disagree",
  2: "Disagree",
  3: "Neutral",
  4: "Agree",
  5: "Strongly Agree",
};

export const DEFAULT_OPTIONS = [
  "😊 Positive and supportive",
  "😟 Needs improvement",
];

export const DEFAULT_QUESTION: Question = {
  key: "default-question",
  title: "",
  required: false,
  type: SurveyQuestionType.SingleOption,
  scaleOptions: Object.entries(SCALE_OPTION_LABELS).map(([value, label]) => ({
    key: generateId(),
    label,
    value: Number(value),
  })),
  textOptions: DEFAULT_OPTIONS.map((label) => ({
    key: generateId(),
    label,
    value: label,
  })),
  wasTouched: false,
  validation: null,
};

export const VALIDATION_MESSAGES = {
  TITLE_ERROR: "Add question title",
  OPTION_ERROR: "Fill option name",
  MIN_OPTIONS_ERROR: "At least 2 options required",
};

export const QUESTION_CONFIG = {
  MIN_TEXT_OPTIONS: 2,
  DEFAULT_SCALE_OPTIONS: 5,
  TITLE_MAX_LENGTH: 1000,
  TEMPLATE_NAME_MAX_LENGTH: 200,
  OPTION_LABEL_MAX_LENGTH: 150,
};
