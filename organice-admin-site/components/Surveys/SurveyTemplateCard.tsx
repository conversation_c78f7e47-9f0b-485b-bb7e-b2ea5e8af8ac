import cn from "classnames";
import pluralize from "pluralize";
import React, { useMemo } from "react";

import Tooltip from "../Tooltip";

import { SURVEY_QUESTION_TYPE_MAP } from "./constants";
import { SurveyRawTemplate } from "./types";

interface SurveyTemplateCardProps {
  template: SurveyRawTemplate;
  className?: string;
}

const SurveyTemplateCard: React.FC<SurveyTemplateCardProps> = ({
  template,
  className,
}) => {
  const templateDetails = useMemo(() => {
    return (
      <div className="text-left text-sm">
        <p className="font-bold">
          {pluralize("question", template.questions.length, true)}
        </p>

        <ol className="mt-2 list-decimal pl-4">
          {template.questions.map((question) => (
            <li key={question.id}>
              {question.title}{" "}
              <span className="whitespace-nowrap">
                ({SURVEY_QUESTION_TYPE_MAP[question.type]})
              </span>
            </li>
          ))}
        </ol>
      </div>
    );
  }, [template]);

  return (
    <Tooltip
      message={templateDetails}
      className="!w-full"
      tooltipClassName="max-w-[480px]"
    >
      <div
        key={template.id}
        className={cn("rounded-lg border border-main-300 p-4", className)}
      >
        <p className="text-md font-semibold text-main-900 line-clamp-2">
          {template.title}
        </p>
        <p className="mt-2 text-sm text-main-500">
          {pluralize("question", template.questions.length, true)}
        </p>
      </div>
    </Tooltip>
  );
};

export default SurveyTemplateCard;
