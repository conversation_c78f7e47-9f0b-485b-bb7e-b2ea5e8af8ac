import { AnimateLayoutChanges, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import cn from "classnames";
import { CSSProperties, useEffect, useRef } from "react";

import DragHandle from "../../DragHandle";
import { Question } from "../types";

import QuestionTypeBadge from "./QuestionTypeBadge";

interface SortableQuestionProps {
  question: Question;
  index: number;
  isActive: boolean;
  editable: boolean;
  onClick: (questionId: string) => void;
  renderDropdownOptions: (question: Question) => JSX.Element;
}

const animateLayoutChanges: AnimateLayoutChanges = ({
  isSorting,
  wasDragging,
}) => !(isSorting || wasDragging);

const SortableQuestion: React.FC<SortableQuestionProps> = ({
  question,
  index,
  isActive,
  editable,
  onClick,
  renderDropdownOptions,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: question.key,
    animateLayoutChanges,
  });

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isActive) {
      ref.current?.scrollIntoView({ behavior: "smooth" });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const style: CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
  };

  const isValid = !question.wasTouched || !!question.validation?.isValid;

  return (
    <div ref={ref} className="flex items-center">
      {editable ? (
        <DragHandle listeners={listeners} isDragging={isDragging} />
      ) : null}

      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        className={cn(
          "relative flex flex-grow items-center justify-between rounded-lg  border bg-white py-2 px-4",
          isDragging && "z-[9999]",
          isActive ? "border-violet-600" : "border-main-300"
        )}
      >
        <button
          type="button"
          className={cn(
            "break-anywhere group flex w-full flex-col items-start gap-1 text-start text-sm leading-6 transition-colors hover:text-main-900",
            {
              "text-red-300": !isValid && !isActive,
              "text-red-500": !isValid && isActive,
              "text-black": isValid && isActive,
              "text-main-400": isValid && !isActive,
            }
          )}
          onClick={() => onClick(question.key)}
        >
          <span className="font-semibold">{index + 1}</span>

          <span
            className={cn("mt-1 line-clamp-3", isActive && "font-semibold")}
          >
            {question.title || "Untitled"}
          </span>

          <QuestionTypeBadge type={question.type} className="mt-2" />
        </button>

        <div className="absolute top-2 right-4">
          {editable ? renderDropdownOptions(question) : null}
        </div>
      </div>
    </div>
  );
};

export default SortableQuestion;
