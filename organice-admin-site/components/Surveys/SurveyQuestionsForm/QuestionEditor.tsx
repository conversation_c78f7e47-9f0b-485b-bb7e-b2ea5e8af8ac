import cn from "classnames";
import React, { useMemo } from "react";

import { SurveyQuestionType } from "../../../graphql/client.generated";
import Button from "../../Button";
import ExpandableInput from "../../ExpandableInput";
import Select from "../../Select";
import Toggle from "../../Toggle";
import { QUESTION_CONFIG, VALIDATION_MESSAGES } from "../constants";
import { Question } from "../types";

import AnswerVariant from "./AnswerVariant";
import QuestionTypeBadge, { questionTypeMap } from "./QuestionTypeBadge";
import { useQuestionEditor } from "./useQuestionEditor";

interface QuestionEditorProps {
  activeQuestion: Question;
  editable: boolean;
  onQuestionChange: (updatedQuestion: Question) => void;
}

const QuestionEditor: React.FC<QuestionEditorProps> = ({
  activeQuestion,
  onQuestionChange,
  editable,
}) => {
  const {
    changeName,
    chooseType,
    editOption,
    addOption,
    deleteOption,
    toggleRequired,
  } = useQuestionEditor({
    question: activeQuestion,
    onQuestionChange,
  });

  const optionErrors = useMemo(() => {
    return activeQuestion.validation?.validationErrors?.optionsErrors ?? [];
  }, [activeQuestion.validation]);

  const selectedTypeValue = questionTypeMap[activeQuestion.type];

  const isTextQuestion = activeQuestion.type === SurveyQuestionType.Text;
  const isScaleQuestion = activeQuestion.type === SurveyQuestionType.Scale;

  const renderQuestionContent = useMemo(() => {
    let canDelete = true;

    switch (activeQuestion.type) {
      case SurveyQuestionType.Scale:
        if (!activeQuestion.scaleOptions.length) return null;

        return activeQuestion.scaleOptions.map((option, index) => (
          <AnswerVariant
            key={option.id}
            index={index + 1}
            value={String(option.label)}
            editable={editable}
            error={optionErrors.find((err) => err.key === option.key)?.error}
            onChange={(value) =>
              editOption({
                key: option.key,
                value: option.value,
                label: value,
              })
            }
          />
        ));

      case SurveyQuestionType.SingleOption:
      case SurveyQuestionType.MultipleOptions:
        if (!activeQuestion.textOptions.length) return null;

        canDelete =
          activeQuestion.textOptions.length > QUESTION_CONFIG.MIN_TEXT_OPTIONS;

        return activeQuestion.textOptions.map((option, index) => (
          <AnswerVariant
            key={option.id}
            index={index + 1}
            value={option.value}
            editable={editable}
            error={optionErrors.find((err) => err.key === option.id)?.error}
            canDelete={canDelete}
            deleteTooltip={
              canDelete ? undefined : VALIDATION_MESSAGES.MIN_OPTIONS_ERROR
            }
            onChange={(value) => editOption({ key: option.key, value })}
            onDelete={() => deleteOption(option.key)}
          />
        ));

      default:
        return null;
    }
  }, [activeQuestion, editable, optionErrors, editOption, deleteOption]);

  return (
    <div className="flex h-full flex-col gap-6 overflow-auto py-5 px-6">
      <ExpandableInput
        disabled={!editable}
        maxLength={QUESTION_CONFIG.TITLE_MAX_LENGTH}
        key={activeQuestion.key}
        error={
          activeQuestion.validation?.validationErrors?.titleError ?? undefined
        }
        placeholder="Enter your question"
        value={activeQuestion.title}
        onChange={changeName}
        className={cn("!mr-0 px-3", !editable && "font-semibold")}
      />

      <div className="flex items-center justify-between gap-4">
        <div className="text-sm font-semibold leading-6 text-black">
          Question type
        </div>

        {editable ? (
          <Select
            value={selectedTypeValue}
            options={Object.values(questionTypeMap)}
            placeholder="Select type"
            onChange={chooseType}
            className="!mt-0 min-w-[170px]"
            menuClassName="px-2 space-y-1"
            renderOption={({ selected, option }) => (
              <QuestionTypeBadge
                type={option.value}
                className={cn(
                  "w-full cursor-pointer",
                  selected && "!bg-sky-200"
                )}
              />
            )}
          />
        ) : (
          <QuestionTypeBadge type={activeQuestion.type} />
        )}
      </div>

      {!isTextQuestion && (
        <div className="rounded-lg bg-main-100 py-2 px-1">
          <table className="w-full border-collapse">
            <tbody>
              {renderQuestionContent}

              {editable && !isScaleQuestion && (
                <tr>
                  <td />
                  <td className="py-2">
                    <Button variant="outline" onClick={addOption}>
                      Add choice
                    </Button>
                  </td>
                  <td />
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      <div className="flex items-center justify-between gap-4">
        <span className="text-sm font-semibold leading-6">
          Is this question required?
        </span>
        <Toggle
          disabled={!editable}
          checked={activeQuestion.required}
          onChange={toggleRequired}
        />
      </div>
    </div>
  );
};

export default QuestionEditor;
