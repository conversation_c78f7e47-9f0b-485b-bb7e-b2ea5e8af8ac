import { useCallback } from "react";

import { SurveyQuestionType } from "../../../graphql/client.generated";
import { Question, ScaleOption, TextOption } from "../types";
import { generateId, isQuestionWithChoices } from "../utils";

interface UseQuestionEditorProps {
  question: Question;
  onQuestionChange: (updatedQuestion: Question) => void;
}

interface UseQuestionEditorReturn {
  changeName: (value: string) => void;
  chooseType: (option: { value: SurveyQuestionType } | null) => void;
  toggleRequired: () => void;
  editOption: (option: TextOption | ScaleOption) => void;
  addOption: () => void;
  deleteOption: (optionKey: string) => void;
}

export function useQuestionEditor({
  question,
  onQuestionChange,
}: UseQuestionEditorProps): UseQuestionEditorReturn {
  const changeName = useCallback(
    (value: string) => {
      onQuestionChange({ ...question, title: value });
    },
    [question, onQuestionChange]
  );

  const chooseType = useCallback(
    (option: { value: SurveyQuestionType } | null) => {
      onQuestionChange({
        ...question,
        // option is non nullable until select has default value
        type: option!.value,
      });
    },
    [question, onQuestionChange]
  );

  const editOption = useCallback(
    (option: TextOption | ScaleOption) => {
      if (isQuestionWithChoices(question)) {
        const textOption = option as TextOption;
        const updatedOptions = question.textOptions.map((opt) =>
          opt.key === option.key ? textOption : opt
        );

        onQuestionChange({
          ...question,
          textOptions: updatedOptions,
        });

        return;
      }

      const scaleOption = option as ScaleOption;

      onQuestionChange({
        ...question,
        scaleOptions: question.scaleOptions.map((opt) =>
          opt.key === scaleOption.key
            ? { ...opt, label: scaleOption.label }
            : opt
        ),
      });
    },
    [question, onQuestionChange]
  );

  const addOption = useCallback(() => {
    const newOptionKey = generateId();

    if (!isQuestionWithChoices(question)) return;

    const updatedOptions = [
      ...question.textOptions,
      { key: newOptionKey, value: "" },
    ];

    onQuestionChange({
      ...question,
      textOptions: updatedOptions,
    });
  }, [question, onQuestionChange]);

  const deleteOption = useCallback(
    (optionKey: string) => {
      if (!isQuestionWithChoices(question)) return;

      const updatedOptions = question.textOptions.filter(
        (opt) => opt.key !== optionKey
      );

      onQuestionChange({
        ...question,
        textOptions: updatedOptions,
      });
    },
    [question, onQuestionChange]
  );

  const toggleRequired = useCallback(() => {
    onQuestionChange({
      ...question,
      required: !question.required,
    });
  }, [question, onQuestionChange]);

  return {
    changeName,
    chooseType,
    toggleRequired,
    editOption,
    addOption,
    deleteOption,
  };
}

export default useQuestionEditor;
