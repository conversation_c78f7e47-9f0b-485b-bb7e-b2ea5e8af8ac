import { DndContext } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import React, { FC } from "react";

import Button from "../../Button";
import { UseQuestionsControllerReturn } from "../useQuestionsController";

import QuestionDropdownMenu from "./QuestionDropdownMenu";
import QuestionEditor from "./QuestionEditor";
import SortableQuestion from "./SortableQuestion";

interface SurveyQuestionsFormProps {
  controller: UseQuestionsControllerReturn;
  editable?: boolean;
}

const SurveyQuestionsForm: FC<SurveyQuestionsFormProps> = ({
  controller,
  editable = false,
}) => {
  const {
    questions,
    activeQuestion,
    questionsListRef,
    selectQuestion,
    addNewQuestion,
    updateQuestion,
    deleteQuestion,
    copyQuestion,
    reorderQuestions,
  } = controller;

  return (
    <div className="grid h-0 flex-1 grid-cols-[30%_70%] border-main-300">
      <div className="flex h-full flex-col overflow-hidden border-r border-main-300 pl-2">
        <div
          ref={questionsListRef}
          className="scrollbar-stable flex flex-col gap-4 overflow-auto pt-5 pr-2.5 pb-4"
        >
          <DndContext
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={reorderQuestions}
          >
            <SortableContext
              items={questions.map((item) => item.key)}
              strategy={verticalListSortingStrategy}
            >
              {questions.map((q, index) => {
                return (
                  <SortableQuestion
                    key={q.key}
                    index={index}
                    isActive={q.key === activeQuestion?.key}
                    editable={editable}
                    question={q}
                    onClick={selectQuestion}
                    renderDropdownOptions={(question) => (
                      <QuestionDropdownMenu
                        question={question}
                        renderDeleteButton={questions.length > 1}
                        onCopyQuestion={copyQuestion}
                        onDeleteQuestion={deleteQuestion}
                      />
                    )}
                  />
                );
              })}
            </SortableContext>
          </DndContext>
        </div>

        {editable ? (
          <div className="px-6 pb-5">
            <Button
              variant="outline"
              size="xs"
              className="w-full !justify-center"
              onClick={addNewQuestion}
            >
              Add new question
            </Button>
          </div>
        ) : null}
      </div>

      {activeQuestion && (
        <QuestionEditor
          activeQuestion={activeQuestion}
          onQuestionChange={updateQuestion}
          editable={editable}
        />
      )}
    </div>
  );
};

export default SurveyQuestionsForm;
