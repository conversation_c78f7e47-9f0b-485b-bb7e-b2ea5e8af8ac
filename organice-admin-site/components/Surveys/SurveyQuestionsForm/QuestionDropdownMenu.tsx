import React, { FC, useMemo } from "react";

import DropdownMenu from "../../DropdownMenu";
import TreeDotsButton from "../../TreeDotsButton";
import { Question } from "../types";

interface QuestionDropdownMenuProps {
  question: Question;
  renderDeleteButton: boolean;
  onCopyQuestion: (question: Question) => void;
  onDeleteQuestion: (questionId: string) => void;
}

const QuestionDropdownMenu: FC<QuestionDropdownMenuProps> = ({
  question,
  renderDeleteButton,
  onCopyQuestion,
  onDeleteQuestion,
}) => {
  const dropdownOptions = useMemo(() => {
    const options: {
      label: string;
      id: string;
      type?: "danger";
      onClick: VoidFunction;
    }[] = [
      {
        label: "Copy",
        id: "copy_question",
        onClick: () => onCopyQuestion(question),
      },
    ];

    if (renderDeleteButton) {
      options.push({
        label: "Delete",
        id: "delete_question",
        type: "danger" as const,
        onClick: () => onDeleteQuestion(question.key),
      });
    }

    return options;
  }, [onCopyQuestion, onDeleteQuestion, question, renderDeleteButton]);

  return (
    <DropdownMenu menu={dropdownOptions}>
      <TreeDotsButton className="!h-6 w-6 !py-0" itemClassName="bg-black" />
    </DropdownMenu>
  );
};

export default QuestionDropdownMenu;
