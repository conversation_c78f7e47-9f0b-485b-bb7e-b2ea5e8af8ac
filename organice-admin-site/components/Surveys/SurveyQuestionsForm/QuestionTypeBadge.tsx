import cn from "classnames";
import React, { FC, ReactNode } from "react";

import { SurveyQuestionType } from "../../../graphql/client.generated";

import {
  CheckboxSVG,
  MultipleChoiceSVG,
  ParagraphSVG,
  ScaleSVG,
} from "./assets";

export const questionTypeMap: Record<
  SurveyQuestionType,
  {
    icon: ReactNode;
    title: string;
    value: SurveyQuestionType;
  }
> = {
  [SurveyQuestionType.SingleOption]: {
    icon: <MultipleChoiceSVG />,
    title: "Single choice",
    value: SurveyQuestionType.SingleOption,
  },
  [SurveyQuestionType.MultipleOptions]: {
    icon: <CheckboxSVG />,
    title: "Multiple choice",
    value: SurveyQuestionType.MultipleOptions,
  },
  [SurveyQuestionType.Scale]: {
    icon: <ScaleSVG />,
    title: "1 to 5 scale",
    value: SurveyQuestionType.Scale,
  },
  [SurveyQuestionType.Text]: {
    icon: <ParagraphSVG />,
    title: "Paragraph",
    value: SurveyQuestionType.Text,
  },
};

interface QuestionTypeBadgeProps {
  type: SurveyQuestionType;
  className?: string;
}

const QuestionTypeBadge: FC<QuestionTypeBadgeProps> = ({ type, className }) => {
  return (
    <div
      className={cn(
        "align-items inline-flex h-6 items-center gap-1 rounded bg-sky-100 px-2 [&>svg]:h-4 [&>svg]:w-4",
        className
      )}
    >
      {questionTypeMap[type].icon}
      <span className="text-xs text-black">{questionTypeMap[type].title}</span>
    </div>
  );
};

export default QuestionTypeBadge;
