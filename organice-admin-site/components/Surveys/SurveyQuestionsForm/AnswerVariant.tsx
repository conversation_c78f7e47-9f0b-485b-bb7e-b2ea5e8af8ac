import cn from "classnames";

import { CloseButton } from "../../Button";
import ExpandableInput from "../../ExpandableInput";
import Tooltip from "../../Tooltip";

const SLACK_OPTION_LABEL_LIMIT = 150;

interface AnswerVariantProps {
  index: number;
  value: string;
  editable: boolean;
  error?: string;
  deleteTooltip?: string;
  canDelete?: boolean;
  onChange: (value: string) => void;
  onDelete?: VoidFunction;
}

const AnswerVariant: React.FC<AnswerVariantProps> = ({
  index,
  error,
  value,
  editable,
  canDelete,
  deleteTooltip = "Delete choice",
  onChange,
  onDelete,
}) => {
  return (
    <tr>
      <td
        className={cn("w-0 min-w-min py-2 px-3 align-middle", error && "pb-6")}
      >
        {String(index).padStart(2, "0")}
      </td>

      <td className={cn("py-2", error && "pb-6")}>
        <ExpandableInput
          disabled={!editable}
          error={error}
          maxLength={SLACK_OPTION_LABEL_LIMIT}
          placeholder="Enter an answer choice"
          value={value}
          onChange={onChange}
          className="!mr-0 border-none bg-white !px-3 !py-1.5"
        />
      </td>

      <td
        className={cn("w-0 min-w-min py-2 px-3 align-middle", error && "pb-6")}
      >
        {editable && onDelete && (
          <div className="h-7">
            <Tooltip message={deleteTooltip}>
              <CloseButton
                type="secondary"
                onClick={onDelete}
                disabled={!canDelete}
                className={canDelete ? undefined : "cursor-not-allowed"}
              />
            </Tooltip>
          </div>
        )}
      </td>
    </tr>
  );
};

export default AnswerVariant;
