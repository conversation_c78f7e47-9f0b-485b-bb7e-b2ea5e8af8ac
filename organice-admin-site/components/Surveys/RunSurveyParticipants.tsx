import { capitalize } from "lodash";
import React, { FC, useMemo } from "react";

import Select, { Option } from "../Select";

import RadioButtonIcon from "./RadioButtonIcon";

interface RunSurveyParticipantsProps {
  selectedChannel: Option | null;
  setChannel: (channel: Option | null) => void;
  channels: {
    id: string;
    name: string;
  }[];
}

const RunSurveyParticipants: FC<RunSurveyParticipantsProps> = ({
  channels,
  selectedChannel,
  setChannel,
}) => {
  const channelOptions: Option[] = useMemo(
    () =>
      channels.map((channel) => {
        const title = `# ${capitalize(channel.name)}`;

        return {
          title,
          label: title,
          value: channel.id,
        };
      }),
    [channels]
  );

  return (
    <div className="flex flex-col items-stretch gap-8 p-6">
      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
      <div className="flex max-w-[370px] items-start gap-3">
        <RadioButtonIcon active className="shrink-0" />
        <div className="text-md flex flex-col">
          <span className="cursor-pointer font-bold leading-none text-black">
            Static audience
          </span>
          <span className="mt-2 text-main-500">
            Choose a public channel for distributing the survey.
          </span>
          <Select<Option>
            options={channelOptions}
            value={selectedChannel}
            onChange={setChannel}
            placeholder="Select a channel"
            className="mt-4"
          />
        </div>
      </div>

      <div className="h-px w-full bg-main-300" />

      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
      <div className="flex items-start gap-3">
        <RadioButtonIcon active={false} className="shrink-0" />
        <div className="text-md flex flex-col">
          <span className="cursor-not-allowed font-bold leading-none text-black">
            Dynamic audience (soon)
          </span>
          <span className="mt-2 text-main-500">
            Specify the user types to receive the survey.
          </span>
        </div>
      </div>
    </div>
  );
};

export default RunSurveyParticipants;
