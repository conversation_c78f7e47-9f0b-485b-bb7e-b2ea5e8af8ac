import { Column } from "react-datasheet-grid";

import {
  ScaleOption as ScaleOptionFragment,
  SurveyQuestionType,
  SurveyStatus,
  SurveyTemplateQuestionFieldsFragment,
  TextOption as TextOptionFragment,
} from "../../graphql/client.generated";

export interface SurveyInfoItem {
  id: string;
  title: string;
  startMessagePermalink: string;
  channelName?: string | null;
  isAnonymous: boolean;
  status: SurveyStatus;
  completionRate: number;
  createdAt: Date;
  createdBy?: {
    realName?: string | null;
    avatarUrl?: string | null;
  } | null;
  questionsNumber: number;
  participantsCount: number;
}

export type GutterColumn<T> = Partial<
  Pick<
    Column<T>,
    | "title"
    | "basis"
    | "grow"
    | "shrink"
    | "minWidth"
    | "maxWidth"
    | "component"
    | "columnData"
  >
>;

export type TextOption = Omit<TextOptionFragment, "id"> & {
  id?: string;
  key: string;
};

export type ScaleOption = Omit<ScaleOptionFragment, "id"> & {
  id?: string;
  key: string;
};

export type Question = Omit<
  SurveyTemplateQuestionFieldsFragment,
  | "id"
  | "__typename"
  | "type"
  | "singleOptions"
  | "multipleOptions"
  | "scaleOptions"
> & {
  id?: string;
  key: string; // key is dummy parameter used to make actions within questions editor
  type: SurveyQuestionType;
  wasTouched: boolean;
  validation: QuestionValidation | null;
  textOptions: TextOption[];
  scaleOptions: ScaleOption[];
};

export interface QuestionValidation {
  isValid: boolean;
  validationErrors: ValidationErrors | null;
}

export interface ValidationErrors {
  titleError: string | null;
  optionsErrors: OptionError[];
}

export interface OptionError {
  key: string;
  error: string;
}

export interface SurveyRawTemplate {
  id: string;
  title: string;
  questions: SurveyTemplateQuestionFieldsFragment[];
  isDefault?: boolean;
}

export interface SurveyTemplate {
  id: string;
  title: string;
  questions: Question[];
  isDefault?: boolean;
}
