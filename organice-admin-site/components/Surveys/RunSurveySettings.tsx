import React, { ChangeEvent, FC } from "react";

import Checkbox from "../Checkbox";

interface RunSurveySettingsProps {
  isAnonymous: boolean;
  setAnonymous: (isAnonymous: ChangeEvent<HTMLInputElement>) => void;
}

const RunSurveySettings: FC<RunSurveySettingsProps> = ({
  isAnonymous,
  setAnonymous,
}) => {
  return (
    <div className="grid grid-cols-2 gap-5 p-6">
      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
      <div className="flex select-none items-start gap-3">
        <Checkbox
          name="anonymity"
          checked={isAnonymous}
          onChange={setAnonymous}
          checkBoxClassName="!h-6 !w-6 mr-0 shrink-0"
        />
        <div className="text-md flex flex-col items-start">
          {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
          <label
            htmlFor="anonymity-id"
            className="cursor-pointer font-bold leading-none text-black"
          >
            Anonymous survey
          </label>
          <span className="mt-2 text-main-500">
            Make responses to this survey anonymous for participants.
          </span>
        </div>
      </div>
    </div>
  );
};

export default RunSurveySettings;
