import { SurveyQuestionType } from "../../graphql/client.generated";

import { Question } from "./types";

function idGenerator(): () => string {
  let counter = 0;

  return () => {
    counter += 1;

    return String(counter);
  };
}

export const generateId = idGenerator();

export function isQuestionWithChoices({ type }: Question): boolean {
  return (
    type === SurveyQuestionType.MultipleOptions ||
    type === SurveyQuestionType.SingleOption
  );
}
