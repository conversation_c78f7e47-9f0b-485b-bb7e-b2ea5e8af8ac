import {
  EditQuestionInput,
  NewQuestionInput,
  SurveyQuestionType,
  SurveyTemplateQuestionFieldsFragment,
} from "../../graphql/client.generated";

import { DEFAULT_QUESTION } from "./constants";
import { Question, ScaleOption, TextOption } from "./types";
import { generateId } from "./utils";

export function mapQuestionsForForm(
  question: SurveyTemplateQuestionFieldsFragment
): Question {
  const {
    id,
    singleOptions = [],
    multipleOptions = [],
    scaleOptions = [],
    ...restParams
  } = question;

  let mappedTextOptions: TextOption[] = DEFAULT_QUESTION.textOptions;
  let mappedScaleOptions: ScaleOption[] = DEFAULT_QUESTION.scaleOptions;

  if (singleOptions?.length) {
    mappedTextOptions = singleOptions.map((option) => ({
      key: generateId(),
      value: option.value,
    }));
  }

  if (multipleOptions?.length) {
    mappedTextOptions = multipleOptions.map((option) => ({
      key: generateId(),
      value: option.value,
    }));
  }

  if (scaleOptions?.length) {
    mappedScaleOptions = scaleOptions.map((option) => ({
      key: generateId(),
      value: option.value,
      label: option.label,
    }));
  }

  return {
    ...restParams,
    key: generateId(),
    textOptions: mappedTextOptions,
    scaleOptions: mappedScaleOptions,
    wasTouched: false,
    validation: null,
  };
}

export function mapQuestionPayload(
  question: Question,
  isEditing?: false
): NewQuestionInput;
export function mapQuestionPayload(
  question: Question,
  isEditing: true
): EditQuestionInput;
export function mapQuestionPayload(
  question: Question,
  isEditing = false
): NewQuestionInput | EditQuestionInput {
  const payload = {
    id: question.id,
    title: question.title,
    type: question.type,
    required: question.required,
  };

  if (question.type === SurveyQuestionType.Scale) {
    Object.assign(payload, {
      scaleOptions: question.scaleOptions.map((option) => ({
        id: option.id,
        value: option.value,
        label: option.label,
      })),
    });
  }

  if (question.type === SurveyQuestionType.SingleOption) {
    Object.assign(payload, {
      singleOptions: question.textOptions.map((option) =>
        isEditing
          ? {
              id: option.id,
              value: option.value,
            }
          : option.value
      ),
    });
  }

  if (question.type === SurveyQuestionType.MultipleOptions) {
    Object.assign(payload, {
      multipleOptions: question.textOptions.map((option) =>
        isEditing
          ? {
              id: option.id,
              value: option.value,
            }
          : option.value
      ),
    });
  }

  return payload;
}
