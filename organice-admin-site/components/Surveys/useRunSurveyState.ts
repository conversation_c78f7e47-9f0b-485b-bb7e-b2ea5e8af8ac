import { ChangeEvent, useMemo, useReducer } from "react";

import { useMixpanelContext } from "../../context/MixpanelContext";
import { useRunSurveyMutation } from "../../graphql/client.generated";
import { useToast } from "../../hooks/useToast";
import { Option } from "../Select";

import { DEFAULT_QUESTION } from "./constants";
import { mapQuestionPayload, mapQuestionsForForm } from "./transformers";
import { Question, SurveyRawTemplate } from "./types";
import useQuestionValidation from "./useQuestionValidation";
import useSurveys from "./useSurveys";
import { generateId } from "./utils";

interface UseRunSurveyState {
  status: "idle" | "setup";
  activeTab: "questions" | "participants" | "settings";
  formData: {
    surveyTitle: string;
    questions: Question[];
    template: SurveyRawTemplate | null;
    channel: Option | null;
    isAnonymous: boolean;
  };
}

const initialState: UseRunSurveyState = {
  status: "idle",
  activeTab: "questions",
  formData: {
    surveyTitle: "",
    questions: [
      {
        ...DEFAULT_QUESTION,
        key: generateId(),
      },
    ],
    template: null,
    channel: null,
    isAnonymous: false,
  },
};

interface StartFromScratchAction {
  type: "START_FROM_SCRATCH";
}

interface StartSetupAction {
  type: "START_SETUP";
}

interface SetTabAction {
  type: "SET_TAB";
  payload: UseRunSurveyState["activeTab"];
}

interface UpdateFormDateAction {
  type: "UPDATE_FORM_DATA";
  payload: Partial<UseRunSurveyState["formData"]>;
}

type UseRunSurveyStateAction =
  | StartFromScratchAction
  | StartSetupAction
  | SetTabAction
  | UpdateFormDateAction;

const reducer = (
  state: UseRunSurveyState,
  action: UseRunSurveyStateAction
): UseRunSurveyState => {
  switch (action.type) {
    case "START_FROM_SCRATCH":
      return {
        ...state,
        status: "setup",
        formData: initialState.formData,
      };
    case "START_SETUP":
      return {
        ...state,
        status: "setup",
      };
    case "SET_TAB":
      return {
        ...state,
        activeTab: action.payload,
      };
    case "UPDATE_FORM_DATA":
      return {
        ...state,
        formData: {
          ...state.formData,
          ...action.payload,
        },
      };
    default:
      return state;
  }
};

interface UseRunSurveyStateProps {
  onSubmit: () => void;
}

interface UseRunSurveyStateReturn extends UseRunSurveyState {
  submitting: boolean;
  validationError: string;
  handleSubmit: () => void;
  startFromScratch: () => void;
  startSetup: () => void;
  setSurveyName: (surveyTitle: string) => void;
  setActiveTab: (tab: UseRunSurveyState["activeTab"]) => void;
  setTemplate: (template: SurveyRawTemplate | null) => void;
  setChannel: (channel: Option | null) => void;
  setAnonymous: (event: ChangeEvent<HTMLInputElement>) => void;
  setQuestions: (
    updaterFnOrQuestions:
      | Question[]
      | ((prevQuestions: Question[]) => Question[])
  ) => void;
}

function useRunSurveyState({
  onSubmit,
}: UseRunSurveyStateProps): UseRunSurveyStateReturn {
  const { track } = useMixpanelContext();
  const toast = useToast();
  const [state, dispatch] = useReducer(reducer, initialState);

  const { observable } = useSurveys();
  const [runSurvey, { loading }] = useRunSurveyMutation();
  const { validateQuestions, areAllQuestionsValid } = useQuestionValidation();

  const validationError = useMemo(() => {
    if (!state.formData.surveyTitle) {
      return "Please provide a valid survey name";
    }

    if (!state.formData.questions.length) {
      return "Please add at least one question to the survey";
    }

    if (!areAllQuestionsValid(state.formData.questions)) {
      return "Please fill your questions";
    }

    if (!state.formData.channel) {
      return "Please select any channel";
    }

    return "";
  }, [state.formData, areAllQuestionsValid]);

  const startFromScratch = (): void => {
    dispatch({ type: "START_FROM_SCRATCH" });
  };

  const startSetup = (): void => {
    dispatch({ type: "START_SETUP" });
  };

  const setActiveTab = (tab: UseRunSurveyState["activeTab"]): void => {
    dispatch({ type: "SET_TAB", payload: tab });
  };

  const setSurveyName = (surveyTitle: string): void => {
    dispatch({
      type: "UPDATE_FORM_DATA",
      payload: {
        surveyTitle,
      },
    });
  };

  const setTemplate = (template: SurveyRawTemplate | null): void => {
    dispatch({
      type: "UPDATE_FORM_DATA",
      payload: {
        template,
        surveyTitle: template?.title ?? "",
        questions: template?.questions.map(mapQuestionsForForm) ?? [
          DEFAULT_QUESTION,
        ],
      },
    });
  };

  const setChannel = (channel: Option | null): void => {
    dispatch({
      type: "UPDATE_FORM_DATA",
      payload: {
        channel,
      },
    });
  };

  const setAnonymous = (event: ChangeEvent<HTMLInputElement>): void => {
    dispatch({
      type: "UPDATE_FORM_DATA",
      payload: {
        isAnonymous: event.target.checked,
      },
    });
  };

  const setQuestions = (
    updaterFnOrQuestions:
      | Question[]
      | ((prevQuestions: Question[]) => Question[])
  ): void => {
    let nextQuestions: Question[] = [];

    if (typeof updaterFnOrQuestions === "function") {
      nextQuestions = updaterFnOrQuestions(state.formData.questions);
    } else {
      nextQuestions = updaterFnOrQuestions;
    }

    dispatch({
      type: "UPDATE_FORM_DATA",
      payload: {
        questions: nextQuestions,
      },
    });
  };

  const handleSubmit = (): void => {
    if (!state.formData.channel) return;

    const { surveyTitle, template, channel, isAnonymous, questions } =
      state.formData;

    void runSurvey({
      variables: {
        survey: {
          surveyTitle,
          templateId: template?.id,
          channelId: channel.value,
          isAnonymous,
          questions: questions.map((q) => mapQuestionPayload(q)),
        },
      },
      onCompleted(result) {
        track("Created new survey");
        toast("success", `Survey “${result.runSurvey.title}” started`);

        observable.updateQuery((layoutQuery) => ({
          ...layoutQuery,
          surveys: [result.runSurvey, ...layoutQuery.surveys],
        }));

        onSubmit();
      },
      onError() {
        toast("error", "Unable to run survey");
      },
    });
  };

  return {
    ...state,
    formData: {
      ...state.formData,
      questions: validateQuestions(state.formData.questions),
    },
    submitting: loading,
    validationError,
    handleSubmit,
    startFromScratch,
    startSetup,
    setActiveTab,
    setSurveyName,
    setTemplate,
    setChannel,
    setQuestions,
    setAnonymous,
  };
}

export default useRunSurveyState;
