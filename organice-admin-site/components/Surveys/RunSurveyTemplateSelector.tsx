import { RadioGroup } from "@headlessui/react";
import cn from "classnames";
import { useRouter } from "next/router";
import React, { FC, useCallback } from "react";

import Button from "../Button";
import EmptyPageState from "../EmptyPageState";
import Skeleton from "../Loading/Skeleton";

import SurveyTemplateCard from "./SurveyTemplateCard";
import { SurveyRawTemplate } from "./types";

interface RunSurveyTemplateSelectorProps {
  templates: SurveyRawTemplate[];
  loading: boolean;
  selectedTemplate: SurveyRawTemplate | null;
  setTemplate: (template: SurveyRawTemplate | null) => void;
  onStartButtonClick: () => void;
}

const RunSurveyTemplateSelector: FC<RunSurveyTemplateSelectorProps> = ({
  loading,
  templates,
  selectedTemplate,
  setTemplate,
  onStartButtonClick,
}) => {
  const router = useRouter();

  const hasTemplates = !!templates.length;
  const isEmptyTemplates = !hasTemplates && !loading;

  const handleSelectTemplate = useCallback(
    (templateId: string) => {
      setTemplate(templates.find((t) => t.id === templateId) ?? null);
    },
    [templates, setTemplate]
  );

  const handleRedirectToSettings = (): void => {
    void router.push("/settings/surveys");
  };

  return (
    <div className="flex min-h-full flex-col py-8 px-6">
      <h2 className="text-center text-2xl font-semibold">
        Let&apos;s run a new Survey!
      </h2>

      <div className="mt-6 flex flex-col items-center gap-2 text-base">
        <Button onClick={onStartButtonClick}>Start from scratch</Button>
        <p className="text-main-500">or</p>
        <p className="text-md text-center text-main-900">Choose the template</p>
      </div>

      <div className="mt-8">
        {loading && (
          <div className="grid grid-cols-2 gap-5">
            {Array(5)
              .fill(1)
              .map((_, i) => (
                /* eslint-disable-next-line react/no-array-index-key */
                <Skeleton key={i} className="h-[86px] w-full bg-main-200" />
              ))}
          </div>
        )}

        {isEmptyTemplates && (
          <EmptyPageState className="n-2 rounded-md border border-dashed py-8">
            <div className="flex flex-col gap-2">
              <div>
                You don&apos;t have any templates yet. <br /> Create one now.
              </div>
              <Button
                variant="outline"
                onClick={handleRedirectToSettings}
                className="!justify-center"
              >
                Add Template
              </Button>
            </div>
          </EmptyPageState>
        )}

        {hasTemplates && (
          <RadioGroup
            className="grid grid-cols-2 gap-5"
            value={selectedTemplate?.id ?? null}
            onChange={handleSelectTemplate}
          >
            {templates.map((template) => (
              <RadioGroup.Option value={template.id} key={template.id}>
                {({ checked }) => (
                  <SurveyTemplateCard
                    template={template}
                    className={cn("cursor-pointer", {
                      "!border-violet-600": checked,
                    })}
                  />
                )}
              </RadioGroup.Option>
            ))}
          </RadioGroup>
        )}
      </div>
    </div>
  );
};

export default RunSurveyTemplateSelector;
