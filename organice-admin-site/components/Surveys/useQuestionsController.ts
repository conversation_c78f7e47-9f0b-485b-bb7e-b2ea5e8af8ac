import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { useCallback, useEffect, useRef, useState } from "react";

import { DEFAULT_QUESTION } from "./constants";
import { Question } from "./types";
import { generateId } from "./utils";

export interface UseQuestionsControllerProps {
  questions: Question[];
  onChange: (
    updaterFnOrValue: Question[] | ((prevQuestions: Question[]) => Question[])
  ) => void;
}

export interface UseQuestionsControllerReturn {
  questions: Question[];
  activeQuestion: Question | undefined;
  questionsListRef: React.RefObject<HTMLDivElement>;
  addNewQuestion: () => void;
  updateQuestion: (updatedQuestion: Question) => void;
  selectQuestion: (questionId: string) => void;
  copyQuestion: (question: Question) => void;
  deleteQuestion: (questionId: string) => void;
  reorderQuestions: (event: DragEndEvent) => void;
}

const useQuestionsController = ({
  questions,
  onChange,
}: UseQuestionsControllerProps): UseQuestionsControllerReturn => {
  const [activeQuestionKey, setActiveQuestionKey] = useState(questions[0]?.key);
  const questionsListRef = useRef<HTMLDivElement | null>(null);

  const activeQuestion = questions.find((q) => q.key === activeQuestionKey);

  const touchQuestion = useCallback(
    (questionKey: string): void => {
      onChange((prev) =>
        prev.map((q) => ({
          ...q,
          wasTouched:
            q.key === questionKey && !q.wasTouched ? true : q.wasTouched,
        }))
      );
    },
    [onChange]
  );

  const addNewQuestion = useCallback((): void => {
    const newQuestion = { ...DEFAULT_QUESTION, key: generateId() };

    onChange((prev) => [
      ...prev.map((q) => ({
        ...q,
        wasTouched:
          q.key === activeQuestionKey && !q.wasTouched ? true : q.wasTouched,
      })),
      newQuestion,
    ]);
    setActiveQuestionKey(newQuestion.key);

    setTimeout(() => {
      if (questionsListRef.current) {
        questionsListRef.current.scrollTop =
          questionsListRef.current.scrollHeight + 100;
      }
    });
  }, [activeQuestionKey, onChange]);

  const updateQuestion = useCallback(
    (updatedQuestion: Question): void => {
      onChange((prev) =>
        prev.map((q) => (q.key === updatedQuestion.key ? updatedQuestion : q))
      );
    },
    [onChange]
  );

  const selectQuestion = useCallback(
    (questionKey: string): void => {
      touchQuestion(activeQuestionKey);
      setActiveQuestionKey(questionKey);
    },
    [activeQuestionKey, touchQuestion]
  );

  const copyQuestion = useCallback(
    (question: Question): void => {
      const copy = {
        ...question,
        key: generateId(),
        title: `(COPY) ${question.title || "Untitled"}`,
      };

      const originIndex = questions.findIndex((q) => q.key === question.key);
      const updatedQuestions = [...questions];

      updatedQuestions.splice(originIndex + 1, 0, copy);
      onChange(updatedQuestions);
      setActiveQuestionKey(copy.key);
    },
    [questions, onChange]
  );

  const deleteQuestion = useCallback(
    (questionKey: string): void => {
      const activeIndex = questions.findIndex((q) => q.key === questionKey);
      const newIndex = Math.abs(activeIndex - 1);

      setActiveQuestionKey(questions[newIndex].key);
      onChange((prev) => prev.filter((q) => q.key !== questionKey));
    },
    [questions, onChange]
  );

  const reorderQuestions = useCallback(
    (event: DragEndEvent): void => {
      if (!event.over) return;

      if (event.active.id !== event.over.id) {
        onChange((prevQuestions) => {
          const oldIndex = questions.findIndex(
            (q) => q.key === event.active.id
          );
          const newIndex = questions.findIndex((q) => q.key === event.over?.id);

          return arrayMove(prevQuestions, oldIndex, newIndex);
        });
      }
    },
    [questions, onChange]
  );

  useEffect(() => {
    if (!activeQuestion && questions.length > 0) {
      setActiveQuestionKey(questions[0].key);
    }
  }, [questions, activeQuestion]);

  return {
    questions,
    activeQuestion,
    questionsListRef,
    addNewQuestion,
    updateQuestion,
    selectQuestion,
    copyQuestion,
    deleteQuestion,
    reorderQuestions,
  };
};

export default useQuestionsController;
