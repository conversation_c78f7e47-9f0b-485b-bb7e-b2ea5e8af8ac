import cn from "classnames";
import React, { memo } from "react";

import { DataFieldWithType } from "../../context/OrgChartContext";
import { getDepartmentColors } from "../../helpers/colors";
import { FlatMember } from "../../helpers/flattenMember";
import Avatar from "../Avatar";
import { PositionSubtitle } from "../OrgChart/OrgChart";
import TeamsTags from "../TeamsTags";

export type Member = Pick<
  FlatMember,
  | "name"
  | "photo"
  | "jobTitle"
  | "teams"
  | "department"
  | "manager"
  | "positionId"
  | "country"
>;
export interface Props {
  member: FlatMember;
  positionId?: string;
  dataField?: DataFieldWithType;
}

const TreeMemberCard: React.FC<Props> = ({
  member,
  positionId = null,
  dataField,
}) => {
  return (
    <div
      data-prevent-sidebar-close
      className="flex h-full w-full flex-col justify-end overflow-hidden text-sm text-main-900"
    >
      <div
        className={cn(
          "mb-auto  h-[6px] w-full overflow-hidden rounded-t-md",
          { "bg-gray-100": !member.department },
          member.department?.departmentColor &&
            getDepartmentColors(member.department.departmentColor)
        )}
      />
      <div className="mb-2 flex items-center justify-between gap-2 px-2">
        <div
          data-test={`node-position-title-${member.positionId ?? ""}`}
          className="truncate whitespace-nowrap"
        >
          {member.jobTitle ? (
            member.jobTitle
          ) : (
            <span className="text-main-400">Title is hidden</span>
          )}
        </div>
        <div className="nodrag">
          <TeamsTags teams={member.teams} positionId={positionId} />
        </div>
      </div>

      <div className="flex items-center gap-3 px-2 pb-2">
        <Avatar photoUrl={member.photo?.url72} />
        <div>
          <div
            className={cn("truncate", {
              "font-normal !text-main-500": !member.name,
              "font-semibold": member.name,
            })}
          >
            {member.name ?? "Name is hidden"}
          </div>
          <PositionSubtitle member={member} dataField={dataField} />
        </div>
      </div>
    </div>
  );
};

export default memo(TreeMemberCard);
