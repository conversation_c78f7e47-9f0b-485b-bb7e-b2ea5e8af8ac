import { useVirtualizer } from "@tanstack/react-virtual";
import cn from "classnames";
import * as React from "react";
import { FC, ReactElement, ComponentProps, useRef, ReactNode } from "react";

// Note: https://github.com/TanStack/virtual/issues/620
const borderBottomTWClasses =
  "relative !border-0 after:absolute after:bottom-0 after:left-0 after:z-30 after:w-full after:border-b after:border-gray-200";

const Table: FC<ComponentProps<"table">> = ({ className, ...props }) => {
  return (
    <div
      data-slot="table-container"
      className="relative w-full overflow-x-auto"
    >
      <table
        data-slot="table"
        className={cn("caption-bottom w-full text-sm", className)}
        {...props}
      />
    </div>
  );
};

const TableHeader: FC<ComponentProps<"thead">> = ({ className, ...props }) => {
  return (
    <thead
      data-slot="table-header"
      className={cn("[&_tr]:border-b [&_tr]:border-gray-200", className)}
      {...props}
    />
  );
};

const TableBody: FC<ComponentProps<"tbody">> = ({ className, ...props }) => {
  return (
    <tbody
      data-slot="table-body"
      className={cn("[&_tr:last-child]:border-0", className)}
      {...props}
    />
  );
};

const TableRow: FC<ComponentProps<"tr">> = ({ className, ...props }) => {
  return (
    <tr
      data-slot="table-row"
      className={cn(
        "data-[state=selected]:bg-violet-100 border-b border-gray-200 [&_td]:hover:bg-gray-100",
        className
      )}
      {...props}
    />
  );
};

const TableHead: FC<ComponentProps<"th">> = ({ className, ...props }) => {
  return (
    <th
      data-slot="table-head"
      className={cn(
        "whitespace-nowrap px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      )}
      {...props}
    />
  );
};

const TableCell: FC<ComponentProps<"td">> = ({ className, ...props }) => {
  return (
    <td
      data-slot="table-cell"
      className={cn(
        "whitespace-nowrap px-4 align-middle transition-colors [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        className
      )}
      {...props}
    />
  );
};

export interface VirtualizedTableColumn<T> {
  id: string;
  header: ReactNode;
  cell: (item: T) => ReactNode;
  // Note: width is the preferred width of the column, if the width has to be fixed then use minWidth and maxWidth with the same value
  width?: number;
  minWidth?: number;
  maxWidth?: number;
}

interface VirtualizedTableProps<T> {
  data: T[];
  columns: VirtualizedTableColumn<T>[];
  rowHeight?: number;
  headerHeight?: number;
  className?: string;
  stickyHeader?: boolean;
  stickyFirstColumn?: boolean;
  dataTest?: string;
}

const VirtualizedTable = <T,>({
  data,
  columns,
  rowHeight = 40,
  headerHeight = 40,
  className,
  stickyHeader,
  stickyFirstColumn,
  dataTest,
}: VirtualizedTableProps<T>): ReactElement | null => {
  const parentRef = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 10,
  });

  return (
    <div ref={parentRef} className={cn("h-full overflow-auto", className)}>
      <div
        data-slot="table-container"
        className="no-scrollbar relative min-h-full w-full overflow-x-auto overflow-y-hidden"
        style={{
          height: `${rowVirtualizer.getTotalSize() + rowHeight}px`,
        }}
      >
        <table
          data-test={dataTest}
          data-slot="table"
          className="caption-bottom w-full text-sm"
        >
          <colgroup>
            {columns.map((column) => (
              <col
                style={{
                  width: column.width,
                  minWidth: column.minWidth,
                  maxWidth: column.maxWidth,
                }}
              />
            ))}
          </colgroup>
          <TableHeader>
            <TableRow
              className={cn(
                stickyFirstColumn || stickyHeader ? "z-10 bg-white" : undefined,
                stickyFirstColumn
                  ? " [&>th:first-child]:sticky [&>th:first-child]:left-0 [&>th:first-child]:z-20 [&>th:first-child]:bg-white"
                  : undefined,
                stickyHeader && rowVirtualizer.scrollOffset
                  ? borderBottomTWClasses
                  : undefined
              )}
              style={{
                height: `${headerHeight}px`,
                ...(stickyHeader
                  ? {
                      transform: !rowVirtualizer.isScrolling
                        ? `translateY(${rowVirtualizer.scrollOffset}px)`
                        : "translateY(0px)",
                    }
                  : undefined),
              }}
            >
              {columns.map((column) => (
                <TableHead key={column.id}>{column.header}</TableHead>
              ))}
            </TableRow>
          </TableHeader>

          <TableBody>
            {rowVirtualizer.getVirtualItems().map((virtualRow, index) => {
              const row = data[virtualRow.index];

              return (
                <TableRow
                  key={virtualRow.index}
                  data-index={virtualRow.index}
                  style={{
                    height: `${virtualRow.size}px`,
                    transform: `translateY(${
                      virtualRow.start - index * virtualRow.size
                    }px)`,
                  }}
                  className={cn(
                    virtualRow.index !== data.length - 1
                      ? borderBottomTWClasses
                      : undefined,

                    stickyFirstColumn
                      ? "[&_td]:hover:!bg-gray-100 [&>td:first-child]:sticky [&>td:first-child]:left-0 [&>td:first-child]:z-10 [&>td:first-child]:bg-white"
                      : undefined
                  )}
                >
                  {columns.map((column) => (
                    <TableCell
                      className="overflow-hidden"
                      key={`${virtualRow.index}-${column.id}`}
                    >
                      <>{column.cell(row)}</>
                    </TableCell>
                  ))}
                </TableRow>
              );
            })}
          </TableBody>
        </table>
      </div>
    </div>
  );
};

export {
  Table,
  VirtualizedTable,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
};
