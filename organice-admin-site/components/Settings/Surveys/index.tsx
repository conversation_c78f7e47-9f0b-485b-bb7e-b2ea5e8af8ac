import { AnimatePresence } from "framer-motion";
import { useState } from "react";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import {
  SurveyTemplateFieldsFragment,
  useCopySurveyTemplateMutation,
  useDeleteSurveyTemplateMutation,
  useSurveySettingsPageQuery,
} from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import useTruncatedText from "../../../hooks/useTruncatedText";
import Button from "../../Button";
import DropdownMenu, { Menu } from "../../DropdownMenu";
import EmptyPageState from "../../EmptyPageState";
import RemovalModal from "../../RemovalModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../Table/Table";
import TableSkeleton from "../../TableSkeleton";
import Tooltip from "../../Tooltip";
import TreeDotsButton from "../../TreeDotsButton";

import SurveyTemplateModal from "./SurveyTemplateModal";

interface SurveyTemplateProps {
  title: string;
  usageCount: number;
  questionsCount: number;
  dropdownMenu: JSX.Element;
}

const SurveyTemplate: React.FC<SurveyTemplateProps> = ({
  title,
  usageCount,
  questionsCount,
  dropdownMenu,
}) => {
  const { ref, isTextTruncated } = useTruncatedText<HTMLParagraphElement>();

  return (
    <TableRow>
      <TableCell className="py-2">
        <Tooltip
          hide={!isTextTruncated}
          message={title}
          tooltipClassName="max-w-[500px]"
        >
          <p ref={ref} className="break-all line-clamp-1">
            {title}
          </p>
        </Tooltip>
      </TableCell>
      <TableCell className="py-2">{questionsCount} question(s)</TableCell>
      <TableCell className="py-2">Used {usageCount} times</TableCell>
      <TableCell className="py-2 text-right">{dropdownMenu}</TableCell>
    </TableRow>
  );
};

const SurveysSettings: React.FC = () => {
  const { track } = useMixpanelContext();
  const toast = useToast();

  const { data, loading, observable } = useSurveySettingsPageQuery();
  const [deleteSurveyTemplate, { loading: deleteTemplateLoading }] =
    useDeleteSurveyTemplateMutation();
  const [copySurveyTemplate] = useCopySurveyTemplateMutation();

  const [templateToRemoveId, setTemplateToRemoveId] = useState<string | null>(
    null
  );
  const [surveyTemplateModalState, setSurveyTemplateModalState] = useState<{
    template: SurveyTemplateFieldsFragment | null;
    afterSubmit: (template: SurveyTemplateFieldsFragment) => void;
  } | null>(null);

  const deleteTemplate = (): void => {
    if (!templateToRemoveId) {
      return;
    }
    track("Deleted survey template ");
    observable.updateQuery((prevData) => {
      return {
        surveyTemplates: prevData.surveyTemplates.filter(
          (t) => t.id !== templateToRemoveId
        ),
      };
    });
    void deleteSurveyTemplate({
      variables: { templateId: templateToRemoveId },
      onCompleted() {
        toast("success", "Survey template deleted");
        setTemplateToRemoveId(null);
      },
    });
  };

  const duplicateTemplate = (template: SurveyTemplateFieldsFragment): void => {
    track("Copied survey template ");

    void copySurveyTemplate({
      variables: {
        templateId: template.id,
      },
      onCompleted(response) {
        addNewTemplateToCache(response.copySurveyTemplate);
      },
    });
  };

  const addNewTemplateToCache = (
    newTemplate: SurveyTemplateFieldsFragment
  ): void => {
    toast("success", `“${newTemplate.title}” template created`);
    observable.updateQuery((prevData) => {
      return {
        surveyTemplates: [newTemplate, ...prevData.surveyTemplates],
      };
    });
  };

  const dropdownOptions = (template: SurveyTemplateFieldsFragment): Menu[] => {
    let options: Menu[] = [
      {
        label: "View questions",
        id: "view_questions",
        onClick: () => {
          setSurveyTemplateModalState({
            template,
            afterSubmit() {},
          });
        },
      },
      {
        label: "Duplicate",
        id: "duplicate_template",
        onClick: () => duplicateTemplate(template),
      },
    ];

    if (!template.isDefault) {
      options = [
        {
          label: "Edit",
          id: "edit_template",
          onClick: () => {
            setSurveyTemplateModalState({
              template,
              afterSubmit(t) {
                toast("success", `“${t.title}” template updated`);
              },
            });
          },
        },
        options[1],
        {
          label: "Delete",
          id: "delete_template",
          type: "danger",
          onClick: () => setTemplateToRemoveId(template.id),
        },
      ];
    }

    return options;
  };

  if (loading) {
    return <TableSkeleton />;
  }

  if (!data?.surveyTemplates.length) {
    return (
      <EmptyPageState className="rounded-md border border-dashed py-8">
        <div className="flex flex-col gap-2">
          <div>
            You don&apos;t have any surveys created yet. <br /> Please create
            your first template.
          </div>
          <div>
            <Button
              variant="outline"
              onClick={() =>
                setSurveyTemplateModalState({
                  template: null,
                  afterSubmit: addNewTemplateToCache,
                })
              }
            >
              Add Template
            </Button>
          </div>
        </div>
      </EmptyPageState>
    );
  }

  return (
    <>
      <div className="mb-4 flex">
        <div className="mr-8 grow">
          <p className="mb-3 text-sm font-bold text-slate-600 ">All surveys</p>

          <p className="text-slate-500">
            Once you create new surveys, you&#39;ll be able to see them in Slack
            and then use them in your work.
          </p>
        </div>
        <div className="ml-2">
          <Button
            variant="outline"
            onClick={() =>
              setSurveyTemplateModalState({
                template: null,
                afterSubmit: addNewTemplateToCache,
              })
            }
          >
            Add Template
          </Button>
        </div>
      </div>

      <Table className="border-collapse">
        <TableHeader className="font-semibold">
          <TableRow>
            <TableHead className="w-1/2 py-2">Name</TableHead>
            <TableHead className="w-1/5 py-2">Questions</TableHead>
            <TableHead className="w-1/5 py-2">Usage</TableHead>
            <TableHead className="py-2" />
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.surveyTemplates.map((t) => (
            <SurveyTemplate
              key={t.id}
              title={t.title}
              questionsCount={t.questions.length}
              usageCount={t.usageCount}
              dropdownMenu={
                <DropdownMenu menu={dropdownOptions(t)}>
                  <TreeDotsButton itemClassName="bg-black" />
                </DropdownMenu>
              }
            />
          ))}
        </TableBody>
      </Table>

      <AnimatePresence>
        {surveyTemplateModalState ? (
          <SurveyTemplateModal
            currentTemplate={surveyTemplateModalState.template}
            afterSubmit={surveyTemplateModalState.afterSubmit}
            onClose={() => setSurveyTemplateModalState(null)}
          />
        ) : null}
      </AnimatePresence>

      {templateToRemoveId ? (
        <RemovalModal
          title="Are you sure you want to delete this template?"
          deleteLoading={deleteTemplateLoading}
          onClose={() => setTemplateToRemoveId(null)}
          onSubmit={deleteTemplate}
        />
      ) : null}
    </>
  );
};

export default SurveysSettings;
