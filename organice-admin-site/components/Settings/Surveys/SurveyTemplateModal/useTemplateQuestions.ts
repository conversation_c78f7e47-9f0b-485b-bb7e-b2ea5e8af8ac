import { useState } from "react";

import { SurveyTemplateQuestionFieldsFragment } from "../../../../graphql/client.generated";
import { DEFAULT_QUESTION } from "../../../Surveys/constants";
import { mapQuestionsForForm } from "../../../Surveys/transformers";
import { Question } from "../../../Surveys/types";
import useQuestionValidation from "../../../Surveys/useQuestionValidation";
import useQuestionsController, {
  UseQuestionsControllerReturn,
} from "../../../Surveys/useQuestionsController";
import { generateId } from "../../../Surveys/utils";

interface UseTemplateQuestionsReturn extends UseQuestionsControllerReturn {
  areQuestionsValid: boolean;
}

const useTemplateQuestions = (
  template: {
    questions: SurveyTemplateQuestionFieldsFragment[];
  } | null
): UseTemplateQuestionsReturn => {
  const [questions, setQuestions] = useState<Question[]>(() => {
    if (!template?.questions.length) {
      return [
        {
          ...DEFAULT_QUESTION,
          key: generateId(),
        },
      ];
    }

    return template.questions.map(mapQuestionsForForm);
  });

  const { validateQuestions, areAllQuestionsValid } = useQuestionValidation();

  const controller = useQuestionsController({
    questions: validateQuestions(questions),
    onChange: setQuestions,
  });

  return {
    ...controller,
    areQuestionsValid: areAllQuestionsValid(questions),
  };
};

export default useTemplateQuestions;
