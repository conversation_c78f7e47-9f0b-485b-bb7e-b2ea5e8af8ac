import { useCallback, useState } from "react";

import { useMixpanelContext } from "../../../../context/MixpanelContext";
import {
  SurveyTemplateFieldsFragment,
  useCreateSurveyTemplateMutation,
  useEditSurveyTemplateMutation,
} from "../../../../graphql/client.generated";
import { mapQuestionPayload } from "../../../Surveys/transformers";
import { Question } from "../../../Surveys/types";

interface UseTemplateMutationsProps {
  currentTemplate: SurveyTemplateFieldsFragment | null;
  questions: Question[];
  areQuestionsValid: boolean;
  onSuccess: (template: SurveyTemplateFieldsFragment) => void;
  onClose: VoidFunction;
}

interface UseTemplateMutationsReturn {
  templateName: string;
  setTemplateName: (name: string) => void;
  isEditable: boolean;
  isValid: boolean;
  isLoading: boolean;
  onSubmit: () => void;
}

const useTemplateMutations = ({
  currentTemplate,
  questions,
  areQuestionsValid,
  onSuccess,
  onClose,
}: UseTemplateMutationsProps): UseTemplateMutationsReturn => {
  const { track } = useMixpanelContext();

  const [templateName, setTemplateName] = useState(
    currentTemplate?.title ?? ""
  );

  const [createSurveyTemplate, { loading: createTemplateLoading }] =
    useCreateSurveyTemplateMutation();

  const [editSurveyTemplate, { loading: editTemplateLoading }] =
    useEditSurveyTemplateMutation();

  const createTemplate = useCallback((): void => {
    track("Created survey template");

    void createSurveyTemplate({
      variables: {
        template: {
          title: templateName,
          questions: questions.map((q) => mapQuestionPayload(q, false)),
        },
      },
      onCompleted(data) {
        onSuccess(data.createSurveyTemplate);
        onClose();
      },
    });
  }, [
    createSurveyTemplate,
    onClose,
    onSuccess,
    questions,
    templateName,
    track,
  ]);

  const editTemplate = useCallback((): void => {
    if (!currentTemplate) return;

    track("Updated survey template");

    void editSurveyTemplate({
      variables: {
        template: {
          id: currentTemplate.id,
          title: templateName,
          questions: questions.map((q) => {
            return mapQuestionPayload(q, true);
          }),
        },
      },
      onCompleted(data) {
        onSuccess(data.editSurveyTemplate);
        onClose();
      },
    });
  }, [
    currentTemplate,
    editSurveyTemplate,
    onClose,
    onSuccess,
    questions,
    templateName,
    track,
  ]);

  const onSubmit = useCallback((): void => {
    if (currentTemplate) {
      editTemplate();

      return;
    }
    createTemplate();
  }, [createTemplate, currentTemplate, editTemplate]);

  return {
    templateName,
    setTemplateName,
    isEditable: !currentTemplate?.isDefault,
    isValid: areQuestionsValid && !!templateName.trim(),
    isLoading: createTemplateLoading || editTemplateLoading,
    onSubmit,
  };
};

export default useTemplateMutations;
