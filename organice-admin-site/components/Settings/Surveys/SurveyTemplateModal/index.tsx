import cn from "classnames";
import React from "react";

import { SurveyTemplateFieldsFragment } from "../../../../graphql/client.generated";
import Button from "../../../Button";
import ExpandableInput from "../../../ExpandableInput";
import Sidebar from "../../../Sidebar/Sidebar";
import { SurveyQuestionsForm } from "../../../Surveys/SurveyQuestionsForm";
import Tooltip from "../../../Tooltip";

import useTemplateMutations from "./useTemplateMutations";
import useTemplateQuestions from "./useTemplateQuestions";

interface SurveyModalProps {
  currentTemplate: SurveyTemplateFieldsFragment | null;
  afterSubmit: (template: SurveyTemplateFieldsFragment) => void;
  onClose: VoidFunction;
}

const SurveyTemplateModal: React.FC<SurveyModalProps> = ({
  currentTemplate,
  onClose,
  afterSubmit,
}) => {
  const questionsController = useTemplateQuestions(currentTemplate);

  const {
    templateName,
    setTemplateName,
    isEditable,
    isLoading,
    isValid,
    onSubmit,
  } = useTemplateMutations({
    currentTemplate,
    questions: questionsController.questions,
    areQuestionsValid: questionsController.areQuestionsValid,
    onSuccess: afterSubmit,
    onClose,
  });

  return (
    <Sidebar
      size="large"
      menu={[]}
      title={
        <ExpandableInput
          disabled={!isEditable}
          maxLength={200}
          focusInitially
          className={cn("px-4", !isEditable && "font-semibold")}
          placeholder="Enter template name"
          value={templateName}
          onChange={setTemplateName}
        />
      }
      headerClassName="!px-6"
      closeSidebar={onClose}
      footer={
        <div className="flex justify-end gap-5">
          <Button color="secondary" variant="outline" onClick={onClose}>
            {isEditable ? "Cancel" : "Close"}
          </Button>
          {isEditable && (
            <Tooltip message="Please fill your questions" hide={isValid}>
              <Button
                loading={isLoading}
                disabled={!isValid}
                onClick={onSubmit}
              >
                Save
              </Button>
            </Tooltip>
          )}
        </div>
      }
    >
      <form
        className={cn(
          "flex h-full flex-col items-stretch",
          isEditable ? "pt-0" : "pt-1"
        )}
        id="add_field"
      >
        <SurveyQuestionsForm
          controller={questionsController}
          editable={isEditable}
        />
      </form>
    </Sidebar>
  );
};

export default SurveyTemplateModal;
