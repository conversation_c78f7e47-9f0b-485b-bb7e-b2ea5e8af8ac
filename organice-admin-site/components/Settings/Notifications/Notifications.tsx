import cn from "classnames";
import { AnimatePresence } from "framer-motion";
import { useState, FC, useMemo } from "react";

import { useConfirm } from "../../../context/ConfirmContext";
import {
  useNotificationsPageQuery,
  useDeleteNotificationMutation,
  useToggleNotificationActivityMutation,
  NotificationsPageQuery,
  NotificationsFrequency,
} from "../../../graphql/client.generated";
import {
  flattenEditableMember,
  choiceFromCountry,
} from "../../../helpers/flattenMember";
import useGetEditableFlatMemberFieldsChoices from "../../../hooks/useGetEditableFlatMemberFieldsChoices";
import { useToast } from "../../../hooks/useToast";
import useTruncatedText from "../../../hooks/useTruncatedText";
import EmptyNotifications from "../../../public/svg/notifications/emptyNotifications.svg";
import Button from "../../Button";
import DropdownMenu, { Menu } from "../../DropdownMenu";
import EmptyPageState from "../../EmptyPageState";
import EditableNotificationSidebar, {
  PresetNotificationData,
} from "../../Sidebar/NotificationSidebar/EditableNotificationSidebar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../Table/Table";
import TableSkeleton from "../../TableSkeleton";
import Tooltip from "../../Tooltip";
import TreeDotsButton from "../../TreeDotsButton";

export interface SidebarData {
  type: "notification";
  data?: PresetNotificationData;
  notificationId?: string;
}

type NotificationData = NotificationsPageQuery["notifications"][0];

interface NotificationCellProps {
  notification: NotificationData;
  editNotification: () => void;
  deleteNotification: () => void;
  toggleActivity: () => void;
}

const TitleCell: FC<{ notification: NotificationData }> = ({
  notification,
}) => {
  const { ref, isTextTruncated } = useTruncatedText<HTMLParagraphElement>();

  return (
    <div className="flex w-full">
      <Tooltip
        hide={!isTextTruncated}
        message={notification.title}
        tooltipClassName="max-w-[500px]"
      >
        <p ref={ref} className="break-all text-sm line-clamp-1">
          {notification.title}
        </p>
      </Tooltip>
    </div>
  );
};

const ChannelCell: FC<{ notification: NotificationData }> = ({
  notification,
}) => (
  <div className="flex w-full">
    <span className="text-sm text-gray-500">
      {notification.settings.channel ? (
        `#${notification.settings.channel.name}`
      ) : (
        <span className="text-red-500">was deleted</span>
      )}
    </span>
  </div>
);

const FrequencyCell: FC<{ notification: NotificationData }> = ({
  notification,
}) => {
  const frequency = useMemo(() => {
    if (notification.settings.frequency === NotificationsFrequency.Day) {
      return "daily";
    }

    if (notification.settings.frequency === NotificationsFrequency.Week) {
      return "weekly";
    }

    return "monthly";
  }, [notification.settings.frequency]);

  return (
    <div className="flex w-full">
      <span className="text-sm text-gray-500">{frequency}</span>
    </div>
  );
};

const StatusCell: FC<{ notification: NotificationData }> = ({
  notification,
}) => (
  <div className="flex w-full">
    <span
      className={cn("rounded px-2 py-1 text-sm", {
        "bg-green-100 text-green-500": notification.isActive,
        "bg-gray-100 text-gray-400": !notification.isActive,
      })}
    >
      {notification.isActive ? "Active" : "Paused"}
    </span>
  </div>
);

const ActionsCell: FC<NotificationCellProps> = ({
  notification,
  editNotification,
  deleteNotification,
  toggleActivity,
}) => {
  const actions = useMemo(() => {
    const options: Menu[] = [
      {
        label: "Edit",
        id: "edit",
        onClick: () => {
          editNotification();
        },
      },
      {
        label: notification.isActive ? "Pause" : "Activate",
        id: "toggle",
        onClick: () => {
          toggleActivity();
        },
      },
      {
        label: "Delete",
        id: "delete_template",
        type: "danger",
        onClick: () => deleteNotification(),
      },
    ];

    return options;
  }, [
    deleteNotification,
    editNotification,
    notification.isActive,
    toggleActivity,
  ]);

  return (
    <div className="flex w-full justify-center">
      <DropdownMenu menu={actions}>
        <TreeDotsButton itemClassName="bg-black" />
      </DropdownMenu>
    </div>
  );
};

const Notifications: FC = () => {
  const { data, loading } = useNotificationsPageQuery({
    fetchPolicy: "cache-and-network",
  });
  const [deleteNotification] = useDeleteNotificationMutation();
  const [toggleActivity] = useToggleNotificationActivityMutation();
  const { confirm } = useConfirm();
  const toast = useToast();

  const members = useMemo(() => {
    return data?.members.map((member) => flattenEditableMember(member)) ?? [];
  }, [data]);

  const {
    teams: teamsOptions,
    departments: departmentsOptions,
    countries: countriesOptions,
    managers: managerOptions,
  } = useGetEditableFlatMemberFieldsChoices(members);

  const timeOffsTypes = useMemo(() => {
    return (
      data?.timeOffTypes.map((type) => ({
        label: `${type.emoji} ${type.label}`,
        value: type.id,
      })) ?? []
    );
  }, [data]);

  const [sidebarData, setSidebarData] = useState<SidebarData | null>();
  const workspaceCountries = useMemo(() => {
    return (
      data?.holidaysSettings?.countries.map(choiceFromCountry).map((c) => ({
        ...c,
        value: c.value.id,
      })) ?? []
    );
  }, [data?.holidaysSettings]);

  const handleEditNotification = (notification: NotificationData): void => {
    setSidebarData({
      type: "notification",
      notificationId: notification.id,
    });
  };

  const handleDeleteNotification = (notification: NotificationData): void => {
    void confirm({
      message: "Are you sure you want to delete this notification?",
      buttonConfirm: "Delete",
      buttonCancel: "Cancel",
    }).then(() => {
      void deleteNotification({
        variables: { id: notification.id },
        refetchQueries: ["NotificationsPage"],
        onCompleted: () =>
          toast("success", "The notification has been removed successfully"),
      });
    });
  };

  const handleToggleActivity = (notification: NotificationData): void => {
    void toggleActivity({
      variables: { id: notification.id },
    });
  };

  return (
    <>
      {loading && !data ? <TableSkeleton /> : null}
      {!loading && !data?.notifications.length ? (
        <EmptyPageState
          className="rounded-md border border-dashed py-8"
          emptyStateIcon={<EmptyNotifications />}
        >
          <div className="flex flex-col gap-2">
            <div>
              You don&apos;t have any notifications created yet. <br /> Use our
              templates or create your own.
            </div>
            <div>
              <Button
                dataTest="add-notification"
                onClick={() => setSidebarData({ type: "notification" })}
              >
                Add Notification
              </Button>
            </div>
          </div>
        </EmptyPageState>
      ) : null}
      {data?.notifications.length ? (
        <>
          <div className="mb-4 flex">
            <div className="mr-8 grow">
              <p className="mb-3 text-sm font-bold text-slate-600 ">
                Notifications
              </p>

              <p className="text-slate-500">
                Use notifications to automatically send messages to your team
              </p>
            </div>
            <div className="ml-2">
              <Button
                dataTest="add-notification"
                variant="outline"
                className="ml-auto"
                onClick={() => setSidebarData({ type: "notification" })}
              >
                Add Notification
              </Button>
            </div>
          </div>

          <div data-test="notifications" className="flex-1 overflow-hidden">
            <Table className="flex-grow">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/2 py-2">Title</TableHead>
                  <TableHead className="w-1/5 py-2">Channel</TableHead>
                  <TableHead className="w-1/5 py-2">Frequency</TableHead>
                  <TableHead className="py-2">Status</TableHead>
                  <TableHead className="py-2" />
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.notifications.map((notification) => (
                  <TableRow key={notification.id}>
                    <TableCell className="py-2">
                      <TitleCell notification={notification} />
                    </TableCell>
                    <TableCell className="py-2">
                      <ChannelCell notification={notification} />
                    </TableCell>
                    <TableCell className="py-2">
                      <FrequencyCell notification={notification} />
                    </TableCell>
                    <TableCell className="py-2">
                      <StatusCell notification={notification} />
                    </TableCell>
                    <TableCell className="py-2">
                      <ActionsCell
                        notification={notification}
                        editNotification={() =>
                          handleEditNotification(notification)
                        }
                        deleteNotification={() =>
                          handleDeleteNotification(notification)
                        }
                        toggleActivity={() =>
                          handleToggleActivity(notification)
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </>
      ) : null}

      <AnimatePresence>
        {sidebarData ? (
          <EditableNotificationSidebar
            channels={data?.channels ?? []}
            teams={teamsOptions}
            departments={departmentsOptions}
            countries={countriesOptions}
            workspaceCountries={workspaceCountries}
            managers={managerOptions}
            timeOffsTypes={timeOffsTypes}
            closeSidebar={() => setSidebarData(null)}
            presetData={sidebarData.data}
            notificationId={sidebarData.notificationId}
          />
        ) : null}
      </AnimatePresence>
    </>
  );
};

export default Notifications;
