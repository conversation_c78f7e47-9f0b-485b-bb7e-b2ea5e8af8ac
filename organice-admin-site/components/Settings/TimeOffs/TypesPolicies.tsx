import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { AnimatePresence } from "framer-motion";
import React, { useCallback, useEffect, useState } from "react";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import {
  TimeOffRequestType,
  useReorderTimeOffTypesMutation,
  useTimeOffsTypesPoliciesSettingsPageQuery,
} from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import Button, { LinkButton } from "../../Button";
import Skeleton from "../../Loading/Skeleton";
import PolicySidebar from "../../Sidebar/PolicySidebar/PolicySidebar";
import FieldGroupSkeletonBlockContent from "../FieldGroupSkeletonBlockContent";

import PolicyItem from "./PolicyItem";
import TimeOffTypeItem from "./TimeOffTypeItem";
import TypeModal from "./TypeModal";

const TypesPolicies: React.FC = () => {
  const { track } = useMixpanelContext();
  const { data, loading } = useTimeOffsTypesPoliciesSettingsPageQuery();
  const [addTypeModalOpen, setAddTypeModalOpen] = useState(false);
  const [addPolicySidebarOpen, setAddPolicySidebar] = useState(false);
  const [types, setTypes] = useState<TimeOffRequestType[]>([]);
  const [reorderTypes] = useReorderTimeOffTypesMutation();
  const toast = useToast();

  useEffect(() => {
    if (data) {
      setTypes(data.timeOffTypes);
    }
  }, [data]);

  const handleReorderTypes = useCallback(
    ({ active, over }: DragEndEvent): void => {
      if (!over || over.id === active.id) {
        return;
      }

      track("Reordered TimeOff types");

      setTypes((prevTypes) => {
        const oldIndex = prevTypes.findIndex((t) => t.id === active.id);
        const newIndex = prevTypes.findIndex((t) => t.id === over.id);

        const newOrder = arrayMove(prevTypes, oldIndex, newIndex);

        void reorderTypes({
          variables: {
            types: newOrder.map((type) => type.id),
          },
          onError: (error) => {
            toast("error", error.message);
          },
          onCompleted: () => {
            toast("success", "Time Off types reordered");
          },
        });

        return newOrder;
      });
    },
    [reorderTypes, toast, track]
  );

  if (loading) {
    return (
      <Skeleton>
        <FieldGroupSkeletonBlockContent className="mb-8" />
        {[...(Array(6) as [])].map((_el, i) => (
          <div
            // eslint-disable-next-line react/no-array-index-key
            key={i}
            className="flex items-center justify-start py-2"
          >
            <div className="flex items-center gap-2">
              <div className="h-10 w-10 rounded-md bg-slate-100" />
              <div className="h-4 w-[100px] rounded-md bg-slate-100" />
            </div>
            <div className="ml-auto h-6 w-6 rounded-md bg-slate-100" />
          </div>
        ))}
        <hr className="my-8 divide-y divide-slate-300" />
        <FieldGroupSkeletonBlockContent />
      </Skeleton>
    );
  }

  if (!data) {
    return <div />;
  }

  return (
    <>
      <div>
        <div className="mb-4 flex">
          <div className="mr-8 grow">
            <p className="mb-3 text-sm font-bold text-slate-600 ">
              Time off types
            </p>

            <p className="text-slate-500">
              Add custom time off types or remove existing ones
            </p>
          </div>
          <div className="ml-2">
            <Button variant="outline" onClick={() => setAddTypeModalOpen(true)}>
              Add new type
            </Button>
          </div>
        </div>
        <div className="mb-8 box-content bg-main-100">
          <DndContext
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={handleReorderTypes}
          >
            <SortableContext
              items={types.map((type) => type.id)}
              strategy={verticalListSortingStrategy}
            >
              {types.map((eventType) => (
                <TimeOffTypeItem
                  key={eventType.id}
                  type={eventType}
                  isRemovable={types.length > 1}
                />
              ))}
            </SortableContext>
          </DndContext>
        </div>
        {addTypeModalOpen ? (
          <TypeModal closeHandler={() => setAddTypeModalOpen(false)} />
        ) : null}
      </div>

      <div>
        <div className="mb-6 flex">
          <div className="mr-8 grow">
            <p className="mb-3 text-sm font-bold text-slate-600 ">
              Time Offs Policies
            </p>

            <p className="text-slate-500">
              Using time off policies you can configure time off accruals,
              rollovers, and limits for various time off types
            </p>
            {data.timeOffPolicies.length ? (
              <p className="text-slate-500">
                You can assign policies on the{" "}
                <LinkButton href="/calendar/policies">policies page</LinkButton>
              </p>
            ) : null}
          </div>
          <div className="ml-2">
            <Button
              dataTest="add-policy-button"
              variant="outline"
              onClick={() => setAddPolicySidebar(true)}
            >
              Add new policy
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {data.timeOffPolicies.map((policy) => (
            <PolicyItem
              key={policy.id}
              policy={policy}
              types={types}
              defaultPolicyId={
                data.timeOffPolicies.find((p) => p.isDefault)?.id
              }
            />
          ))}
        </div>

        <AnimatePresence>
          {addPolicySidebarOpen ? (
            <PolicySidebar
              defaultPolicyId={
                data.timeOffPolicies.find((p) => p.isDefault)?.id
              }
              types={types}
              closeSidebar={() => setAddPolicySidebar(false)}
            />
          ) : null}
        </AnimatePresence>
      </div>
    </>
  );
};

export default TypesPolicies;
