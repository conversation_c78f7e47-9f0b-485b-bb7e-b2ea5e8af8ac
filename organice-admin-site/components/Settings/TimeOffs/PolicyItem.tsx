import { getDaysAndHoursInfo } from "@organice/core/domain/time-offs";
import { AnimatePresence } from "framer-motion";
import { FC, useState } from "react";
import { AlarmTurnOff, Case } from "solar-icon-set";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import {
  Color,
  useDeleteTimeOffPolicyMutation,
  AccrualsFrequency,
  YearStart,
  DayOfWeek,
} from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import DropdownMenu from "../../DropdownMenu";
import RemovalModal from "../../RemovalModal";
import PolicySidebar from "../../Sidebar/PolicySidebar/PolicySidebar";
import Tooltip from "../../Tooltip";
import TreeDotsButton from "../../TreeDotsButton";

import { dayOfWeekShortLabels, TimeOffPolicyType } from "./consts";

interface Props {
  policy: {
    id: string;
    title: string;
    isDefault?: boolean;
    typePolicies: TimeOffPolicyType[];
    workDays: DayOfWeek[];
    includedWeekendDays: DayOfWeek[];
    notifyAccruals: boolean;
  };
  types: {
    id: string;
    color: Color;
    label: string;
    emoji: string;
  }[];
  defaultPolicyId?: string;
}

const daysOfWeek = [
  DayOfWeek.Monday,
  DayOfWeek.Tuesday,
  DayOfWeek.Wednesday,
  DayOfWeek.Thursday,
  DayOfWeek.Friday,
  DayOfWeek.Saturday,
  DayOfWeek.Sunday,
];

const PolicyItem: FC<Props> = ({ policy, types, defaultPolicyId }) => {
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [removalModalOpen, setRemovalModalOpen] = useState(false);
  const [deletePolicy] = useDeleteTimeOffPolicyMutation({
    refetchQueries: ["TimeOffsTypesPoliciesSettingsPage"],
  });
  const { track } = useMixpanelContext();
  const toast = useToast();

  const removeTypeHandler = (): void => {
    track("Time off type remove", { policy });
    void deletePolicy({
      variables: {
        id: policy.id,
      },
      onError: (error) => {
        toast("error", error.message);
      },
    });
  };

  return (
    <section className="rounded-lg border text-sm">
      <header className="flex items-center border-b px-4 py-2">
        <h2 className="inline font-semibold">{policy.title}</h2>
        {policy.isDefault ? (
          <Tooltip
            className="ml-2"
            message="This leave policy is applied to all employees without any other leave policies"
          >
            <span className="rounded bg-green-50 px-2 py-0.5 text-xs text-green-500">
              Default policy
            </span>
          </Tooltip>
        ) : null}
        <div className="ml-auto">
          <DropdownMenu
            menu={[
              {
                label: "Edit",
                id: "edit_type",
                onClick: () => setEditModalOpen(true),
              },
              {
                label: "Delete",
                id: "delete_type",
                type: "danger",
                onClick: () => setRemovalModalOpen(true),
              },
            ]}
          >
            <TreeDotsButton itemClassName="bg-black" />
          </DropdownMenu>
        </div>
        {removalModalOpen ? (
          <RemovalModal
            title="Are you sure you want to delete this policy?"
            onClose={() => setRemovalModalOpen(false)}
            onSubmit={removeTypeHandler}
          />
        ) : null}

        <AnimatePresence>
          {editModalOpen ? (
            <PolicySidebar
              currentPolicy={policy}
              types={types}
              defaultPolicyId={defaultPolicyId}
              closeSidebar={() => setEditModalOpen(false)}
            />
          ) : null}
        </AnimatePresence>
      </header>
      <main className="flex px-4 py-3">
        <div className="flex-grow space-y-2 pr-6">
          {policy.workDays.length > 0 ? (
            <div>
              <h3>
                <Case className="mr-2 inline-block translate-y-[2px]" />
                Working days
              </h3>
              <span className="mt-1 text-slate-500">
                {policy.workDays
                  .reduce(appendDayOfWeekIntoRanges, [])
                  .map(stringifyDaysOfWeekRange)
                  .join(", ")}
              </span>
            </div>
          ) : null}
          {policy.includedWeekendDays.length > 0 ? (
            <div>
              <h3>
                <AlarmTurnOff className="mr-2 inline-block translate-y-[2px]" />
                Weekends
              </h3>
              <span className="mt-1 text-slate-500">
                {policy.includedWeekendDays
                  .reduce(appendDayOfWeekIntoRanges, [])
                  .map(stringifyDaysOfWeekRange)
                  .join(", ")}{" "}
                • Counted in PTO
              </span>
            </div>
          ) : null}
        </div>

        {policy.typePolicies.length > 0 ? (
          <div className="flex-shrink space-y-2 border-l pl-6">
            {policy.typePolicies.map((typePolicy) => {
              const type = types.find((t) => t.id === typePolicy.type.id);

              if (!type) {
                return null;
              }

              const {
                accrualsQuota,
                maxCapacity,
                onStartQuota,
                accuralsFrequency,
                rollOverToNextYear,
                yearStart,
              } = typePolicy;

              return (
                <div key={typePolicy.type.id}>
                  <h3>
                    {type.emoji} {type.label}
                  </h3>
                  <span className="mt-1 whitespace-pre-line text-slate-500">
                    {[
                      onStartQuota > 0
                        ? `On hire: ${getDaysAndHoursInfo(onStartQuota)}`
                        : null,
                      accrualsQuota && accuralsFrequency
                        ? `${
                            accuralsFrequency === AccrualsFrequency.Week
                              ? "Each week"
                              : "Each month"
                          }: ${getDaysAndHoursInfo(accrualsQuota)}`
                        : null,
                      rollOverToNextYear
                        ? "Rollovers to the next year"
                        : yearStart === YearStart.Calendar
                        ? `On January 1st: resets to ${getDaysAndHoursInfo(
                            onStartQuota
                          )}`
                        : `On anniversary: resets to ${getDaysAndHoursInfo(
                            onStartQuota
                          )}`,
                      maxCapacity > 0
                        ? `Maximum: ${getDaysAndHoursInfo(maxCapacity)}`
                        : null,
                    ]
                      .filter((x): x is string => x != null)
                      .join("\n")}
                  </span>
                </div>
              );
            })}
          </div>
        ) : null}
      </main>
    </section>
  );
};

interface DaysOfWeekRange {
  start: DayOfWeek;
  end: DayOfWeek;
}

function appendDayOfWeekIntoRanges(
  ranges: DaysOfWeekRange[],
  dayOfWeek: DayOfWeek
): DaysOfWeekRange[] {
  if (ranges.length === 0) {
    return [{ start: dayOfWeek, end: dayOfWeek }];
  }

  const tail = ranges.slice(0, -1);
  const head = ranges[ranges.length - 1];

  if (daysOfWeek.indexOf(head.end) + 1 === daysOfWeek.indexOf(dayOfWeek)) {
    return [...tail, { start: head.start, end: dayOfWeek }];
  }

  return [...tail, head, { start: dayOfWeek, end: dayOfWeek }];
}

function stringifyDaysOfWeekRange(range: DaysOfWeekRange): string {
  return range.start === range.end
    ? dayOfWeekShortLabels[range.start]
    : `${dayOfWeekShortLabels[range.start]}–${dayOfWeekShortLabels[range.end]}`;
}

export default PolicyItem;
