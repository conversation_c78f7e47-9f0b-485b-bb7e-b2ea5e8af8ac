import cn from "classnames";
import { compareAsc, format } from "date-fns";
import { AnimatePresence } from "framer-motion";
import pluralize from "pluralize";
import {
  FC,
  ReactNode,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from "react";

import {
  Holiday,
  useDeleteHolidayMutation,
  useHolidaysPageQuery,
  HolidaysPageQuery,
} from "../../../graphql/client.generated";
import { Country, countryList } from "../../../helpers/flattenMember";
import EmptyCountries from "../../../public/svg/holidays/emptyCountries.svg";
import Button from "../../Button";
import DropdownMenu from "../../DropdownMenu";
import EmptyPageState from "../../EmptyPageState";
import {
  SingleSelectFilterDropdown,
  Option as FilterOption,
} from "../../FilterDropdown";
import RemovalModal from "../../RemovalModal";
import { Option } from "../../Select";
import CountriesSidebar from "../../Sidebar/CountriesSidebar/CountriesSidebar";
import EditableHolidaySidebar from "../../Sidebar/EditableHolidaySidebar/EditableHolidaySidebar";
import UpdateHolidaysSidebar from "../../Sidebar/UpdateHolidaysSidebar/UpdateHolidaysSidebar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../Table/Table";
import TableSkeleton from "../../TableSkeleton";
import TreeDotsButton from "../../TreeDotsButton";

const ALL_COUNTRIES = "All";

const DateCell: FC<{ holiday: Holiday }> = ({ holiday }) => (
  <div className="flex w-full flex-wrap gap-x-2">
    {compareAsc(holiday.startDate, holiday.endDate) === -1 ? (
      <>
        <span>{format(holiday.startDate, "d MMM yyyy")} &mdash; </span>
        <span>{format(holiday.endDate, "d MMM yyyy")}</span>
      </>
    ) : (
      <span>{format(holiday.startDate, "d MMM yyyy")}</span>
    )}
  </div>
);

const CountryCell: FC<{ holiday: Holiday }> = ({ holiday }) => (
  <div className="flex w-full whitespace-normal">
    {renderCountryWithEmoji(holiday.country, countryList)}
  </div>
);

const HolidayNameCell: FC<{ holiday: Holiday }> = ({ holiday }) => (
  <div className="flex w-full flex-col gap-y-2">
    <span className="break-words font-bold">{holiday.name}</span>
    <p className="whitespace-normal break-words">{holiday.description}</p>
  </div>
);

const ActionsCell: FC<{
  onEdit: () => void;
  onDelete: () => void;
}> = ({ onEdit, onDelete }) => (
  <div className="flex w-full justify-center">
    <DropdownMenu
      menu={[
        {
          id: "edit",
          label: "Edit",
          onClick: onEdit,
        },
        {
          id: "delete",
          label: "Delete",
          type: "danger",
          onClick: onDelete,
        },
      ]}
    >
      <TreeDotsButton
        aria-label="sidebar-dropdown-menu"
        itemClassName="bg-gray-500"
      />
    </DropdownMenu>
  </div>
);

interface EmptyListProps {
  addHandler: () => void;
  addTitle: string;
  mainText: string;
  className?: string;
}

interface DeleteConfirmation {
  type: "holiday";
  id: string;
  onSubmit: () => void;
}

function getCountryOptions(
  countries: Country[],
  valueIncludesFrom?: string[]
): Option[] {
  let selectableCountries = countries;

  if (valueIncludesFrom?.length) {
    selectableCountries = countries.filter((country) =>
      valueIncludesFrom.includes(country.id)
    );
  }

  return selectableCountries.map(({ id, name, emoji }) => ({
    value: id,
    title: name,
    prefix: emoji,
  }));
}

function renderCountryWithEmoji(
  country: string,
  countries: Country[],
  membersCountries?: HolidaysPageQuery["countries"]["countries"]
): ReactNode {
  const targetCountry = countries.find(({ id }) => id === country);
  const membersCount = membersCountries
    ? membersCountries.find(
        ({ label }) => parseMemberCountry(label) === country
      )?.members.length ?? 0
    : 0;

  return targetCountry ? (
    <div className="flex w-max items-center gap-x-2">
      <span>{targetCountry.emoji}</span>
      <span>{targetCountry.name}</span>
      {membersCount > 0 ? <span>({membersCount})</span> : null}
    </div>
  ) : null;
}

function parseMemberCountry(country: string): string {
  return country.slice(5);
}

const HolidaysEmptyList: FC<EmptyListProps> = ({
  addHandler,
  addTitle,
  mainText,
  className,
}) => (
  <EmptyPageState
    className={cn("rounded-md border border-dashed py-8", className)}
    emptyStateIcon={<EmptyCountries />}
  >
    <div className="flex w-[295px] flex-col items-center gap-y-6">
      <p className="text-center">{mainText}</p>
      <Button className="w-fit" onClick={addHandler}>
        {addTitle}
      </Button>
    </div>
  </EmptyPageState>
);

const HolidaysAndEvents: FC = () => {
  const countryOptions = getCountryOptions(countryList);
  const [addingCountry, setAddingCountry] = useState(false);
  const [updatingHolidays, setUpdatingHolidays] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] =
    useState<DeleteConfirmation | null>(null);
  const [addingHoliday, setAddingHoliday] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<string>(ALL_COUNTRIES);
  const [selectedHoliday, setSelectedHoliday] = useState<Holiday | undefined>();

  const { data: holidaysData, loading } = useHolidaysPageQuery({
    fetchPolicy: "network-only",
  });
  const [deleteHolidayMutation] = useDeleteHolidayMutation();

  const countriesOptions: FilterOption[] = useMemo(() => {
    if (!holidaysData?.holidaysSettings?.countries.length) {
      return [];
    }

    return [
      {
        value: ALL_COUNTRIES,
        label: (
          <div className="flex w-max items-center gap-x-2">
            <span>🌐</span>
            <span className="font-bold">All</span>
          </div>
        ),
      },
      ...(holidaysData.holidaysSettings.countries
        .map((country) => {
          const targetCountry = countryList.find(({ id }) => id === country);
          const membersCount =
            holidaysData.countries.countries.find(
              ({ label }) => parseMemberCountry(label) === country
            )?.members.length ?? 0;

          return targetCountry
            ? {
                value: country,
                label: (
                  <div className="flex w-max items-center gap-x-2">
                    <span>{targetCountry.emoji}</span>
                    <span>{targetCountry.name}</span>
                    {membersCount > 0 ? (
                      <span>
                        ({membersCount} {pluralize("employee", membersCount)})
                      </span>
                    ) : null}
                  </div>
                ),
              }
            : null;
        })
        .filter(Boolean) as FilterOption[]),
    ];
  }, [holidaysData]);

  const selectedCountryOption = useMemo(() => {
    return (
      countriesOptions.find(({ value }) => value === selectedCountry) ??
      countriesOptions[0]
    );
  }, [countriesOptions, selectedCountry]);

  const onCountryFilterChange = useCallback((o: FilterOption) => {
    setSelectedCountry(o.value);
  }, []);

  const preselectedCountriesOptions = holidaysData?.holidaysSettings?.countries
    .length
    ? getCountryOptions(countryList, holidaysData.holidaysSettings.countries)
    : [];

  const membersCountries = holidaysData
    ? holidaysData.countries.countries.map(({ label }) =>
        parseMemberCountry(label)
      )
    : [];

  const filteredHolidays: Holiday[] =
    holidaysData?.holidays.filter(
      ({ country }) =>
        selectedCountry === ALL_COUNTRIES || country === selectedCountry
    ) ?? [];

  useEffect(() => {
    if (!holidaysData?.holidaysSettings?.countries.includes(selectedCountry)) {
      setSelectedCountry(ALL_COUNTRIES);
    }
  }, [
    holidaysData?.holidaysSettings?.countries,
    selectedCountry,
    setSelectedCountry,
  ]);

  if (loading) {
    return <TableSkeleton rowClassName="h-[100px]" />;
  }

  return (
    <>
      {holidaysData?.holidaysSettings?.countries.length ? (
        <div>
          <div className="no-scrollbar mb-4 flex items-center justify-between gap-4 overflow-x-auto  overflow-y-hidden">
            <SingleSelectFilterDropdown
              selectedOption={selectedCountryOption}
              options={countriesOptions}
              onChange={onCountryFilterChange}
            />

            <div>
              <DropdownMenu
                placement="bottom-end"
                hideArrow
                menu={[
                  {
                    id: "manage-countries",
                    label: !holidaysData.holidaysSettings.countries.length
                      ? "Add country"
                      : "Manage countries",
                    onClick: () => setAddingCountry(true),
                  },
                  {
                    id: "update-holidays",
                    label: "Pull holidays",
                    onClick: () => setUpdatingHolidays(true),
                  },
                  {
                    id: "add-holiday",
                    label: "Add holiday or event",
                    onClick: () => setAddingHoliday(true),
                  },
                ]}
              >
                <Button variant="outline" className="w-fit">
                  Actions
                </Button>
              </DropdownMenu>
            </div>
          </div>
          {filteredHolidays.length ? (
            <Table className="relative w-full table-fixed">
              <TableHeader className="sticky top-0 z-10 bg-white">
                <TableRow>
                  <TableHead className="w-[150px] py-2">Date</TableHead>
                  {selectedCountry === ALL_COUNTRIES ? (
                    <TableHead className="w-[175px] py-2">Country</TableHead>
                  ) : null}
                  <TableHead className="py-2">Name</TableHead>
                  <TableHead className="w-[50px] py-2" />
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredHolidays.map((holiday) => (
                  <TableRow key={holiday.id}>
                    <TableCell className="py-2">
                      <DateCell holiday={holiday} />
                    </TableCell>
                    {selectedCountry === ALL_COUNTRIES ? (
                      <TableCell className="py-2">
                        <CountryCell holiday={holiday} />
                      </TableCell>
                    ) : null}
                    <TableCell className="py-2">
                      <HolidayNameCell holiday={holiday} />
                    </TableCell>
                    <TableCell className="py-2">
                      <ActionsCell
                        onEdit={() => {
                          setSelectedHoliday(holiday);
                          setAddingHoliday(true);
                        }}
                        onDelete={() => {
                          setDeleteConfirmation({
                            onSubmit: () => {
                              void deleteHolidayMutation({
                                variables: {
                                  id: holiday.id,
                                },
                                refetchQueries: ["HolidaysPage"],
                              }).finally(() => setDeleteConfirmation(null));
                            },
                            type: "holiday",
                            id: holiday.id,
                          });
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <HolidaysEmptyList
              addHandler={() => setAddingHoliday(true)}
              addTitle="Add holiday or event"
              mainText="You don't have any holidays or events available for this country."
              className="mt-2"
            />
          )}
        </div>
      ) : (
        <HolidaysEmptyList
          addHandler={() => setAddingCountry(true)}
          addTitle="Add country"
          mainText="Add your first country and we will automatically pull its holidays or events."
        />
      )}

      <AnimatePresence>
        {addingCountry ? (
          <CountriesSidebar
            onClose={() => setAddingCountry(false)}
            countryOptions={countryOptions}
            preSelectedCountriesOptions={preselectedCountriesOptions}
            membersCountries={membersCountries}
          />
        ) : null}
        {updatingHolidays ? (
          <UpdateHolidaysSidebar
            onClose={() => setUpdatingHolidays(false)}
            countryOptions={preselectedCountriesOptions}
          />
        ) : null}
        {addingHoliday ? (
          <EditableHolidaySidebar
            holiday={selectedHoliday}
            isHideCountries={
              selectedCountry !== ALL_COUNTRIES && !!selectedHoliday
            }
            onClose={() => {
              setAddingHoliday(false);
              setSelectedHoliday(undefined);
            }}
            countryOptions={preselectedCountriesOptions}
          />
        ) : null}
        {deleteConfirmation ? (
          <RemovalModal
            title={`Are you sure you want to delete the ${deleteConfirmation.type}?`}
            onSubmit={deleteConfirmation.onSubmit}
            onClose={() => {
              setDeleteConfirmation(null);
            }}
          />
        ) : null}
      </AnimatePresence>
    </>
  );
};

export default HolidaysAndEvents;
