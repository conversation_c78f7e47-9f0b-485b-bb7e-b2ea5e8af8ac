import {
  AccrualsFrequency,
  DayOfWeek,
  YearStart,
} from "../../../graphql/client.generated";

export interface TimeOffPolicyType {
  type: {
    id: string;
  };
  onStartQuota: number;
  rollOverToNextYear: boolean;
  yearStart: YearStart;
  accrualsQuota: number;
  accuralsFrequency?: AccrualsFrequency | null;
  nextAccruals?: Date | null;
  maxCapacity: number;
}

export const dayOfWeekLabels: Record<DayOfWeek, string> = {
  [DayOfWeek.Monday]: "Monday",
  [DayOfWeek.Tuesday]: "Tuesday",
  [DayOfWeek.Wednesday]: "Wednesday",
  [DayOfWeek.Thursday]: "Thursday",
  [DayOfWeek.Friday]: "Friday",
  [DayOfWeek.Saturday]: "Saturday",
  [DayOfWeek.Sunday]: "Sunday",
};

export const dayOfWeekShortLabels: Record<DayOfWeek, string> = {
  [DayOfWeek.Monday]: "Mon",
  [DayOfWeek.Tuesday]: "Tue",
  [DayOfWeek.Wednesday]: "Wed",
  [DayOfWeek.Thursday]: "Thu",
  [DayOfWeek.Friday]: "Fri",
  [DayOfWeek.Saturday]: "Sat",
  [DayOfWeek.Sunday]: "Sun",
};
