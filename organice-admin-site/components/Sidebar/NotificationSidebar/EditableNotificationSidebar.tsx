import {
  DndContext,
  DragEndEvent,
  useDraggable,
  useDroppable,
  UniqueIdentifier,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import cn from "classnames";
import { useRouter } from "next/router";
import {
  FC,
  useEffect,
  useCallback,
  useState,
  useRef,
  useMemo,
  PropsWithChildren,
} from "react";
import { InfoCircle, MaximizeSquare3 } from "solar-icon-set";

import {
  useAddNotificationMutation,
  useUpdateNotificationMutation,
  usePostNotificationPreviewMutation,
  useNotificationLazyQuery,
  NotificationsFrequency,
  NotificationBlockType,
  NotificationBlockInput,
  NotificationBlock,
  TextBlock as TextBlockAPI,
  ButtonBlock as ButtonBlockAPI,
  TimeOffsBlock as TimeOffsBlockAPI,
  CelebrationsBlock as CelebrationsBlockAPI,
  HolidaysBlock as HolidaysBlockAP<PERSON>,
  Notification,
  ButtonBlockType,
  TimeOffRequestStatus,
} from "../../../graphql/client.generated";
import {
  Choice,
  Team,
  Position,
  FlatMember,
  Department,
} from "../../../helpers/flattenMember";
import { useToast } from "../../../hooks/useToast";
import ButtonBlockSVG from "../../../public/svg/notifications/buttonBlock.svg";
import CelebrationsBlockSVG from "../../../public/svg/notifications/celebrationsBlock.svg";
import HolidaysBlockSVG from "../../../public/svg/notifications/holidaysBlock.svg";
import TextBlockSVG from "../../../public/svg/notifications/textBlock.svg";
import TimeOffsBlockSVG from "../../../public/svg/notifications/timeOffsBlock.svg";
import PlusSVG from "../../../public/svg/rawPlus.svg";
import Button, { CloseButton } from "../../Button";
import DotBackground from "../../DotBackground";
import DragHandle from "../../DragHandle";
import ExpandableInput from "../../ExpandableInput";
import Field from "../../Field";
import Input from "../../Input";
import Select from "../../Select";
import DaySelect, { dayOptions } from "../../Select/DaySelect";
import TimeSelect, { timeOptions } from "../../Select/TimeSelect";
import TimezoneSelect, { timezoneOptions } from "../../Select/TimezoneSelect";
import { Tab } from "../../Tab";
import Tooltip from "../../Tooltip";
import Sidebar from "../Sidebar";

import ButtonBlock from "./blocks/button";
import CelebrationsBlock from "./blocks/celebrations";
import HolidaysBlock from "./blocks/holidays";
import TextBlock from "./blocks/text";
import TimeOffsBlock from "./blocks/timeOffs";

const isTextBlock = (block: NotificationBlock): block is TextBlockAPI => {
  return block.type === NotificationBlockType.Text;
};

const isButtonBlock = (block: NotificationBlock): block is ButtonBlockAPI => {
  return block.type === NotificationBlockType.Button;
};

const isTimeOffsBlock = (
  block: NotificationBlock
): block is TimeOffsBlockAPI => {
  return block.type === NotificationBlockType.TimeOffs;
};

const isCelebrationsBlock = (
  block: NotificationBlock
): block is CelebrationsBlockAPI => {
  return block.type === NotificationBlockType.Celebrations;
};

const isHolidaysBlock = (
  block: NotificationBlock
): block is HolidaysBlockAPI => {
  return block.type === NotificationBlockType.Holidays;
};

interface Props {
  presetData?: PresetNotificationData;
  notificationId?: string;
  // TODO: Select component should accept Choice type
  channels: {
    id: string;
    name: string;
  }[];
  teams: Choice<Team>[];
  departments: Choice<Department>[];
  managers: Choice<Position | "WITHOUT_MANAGER" | FlatMember>[];
  countries: Choice<string>[];
  workspaceCountries: Choice<string>[];
  timeOffsTypes: Choice<string>[];
  closeSidebar: () => void;
}

export interface PresetNotificationData {
  title: string;
  blocks: Notification["blocks"];
  settings: {
    frequency: NotificationsFrequency;
    day: number | null;
    time: string | null;
  };
}

export enum TabBarNotification {
  Blocks = "Blocks",
  Settings = "Settings",
}

interface SettingsState {
  frequency: NotificationsFrequency;
  day: number | null;
  time: string;
  timezone: string;
  channelId: string | null;
}

const DEFAULT_TIME = "9 am";
const DEFAULT_TIMEZONE = "GMT +00:00";

interface BlockButton {
  id: NotificationBlockType;
  label: string;
  tooltip: JSX.Element;
}

interface DraggableButtonProps {
  button: BlockButton;
  addBlock: (type: NotificationBlockType) => void;
}

interface SortableBlockProps {
  block: NotificationBlockInput & {
    id: UniqueIdentifier;
  };
  teams: Choice<Team>[];
  departments: Choice<Department>[];
  managers: Choice<Position | "WITHOUT_MANAGER" | FlatMember>[];
  countries: Choice<string>[];
  workspaceCountries: Choice<string>[];
  timeOffsTypes: Choice<string>[];
  deleteBlock: (id: UniqueIdentifier) => void;
  updateBlock: (id: UniqueIdentifier, block: NotificationBlockInput) => void;
}

const CTABlock: FC = () => {
  return (
    <div className="relative z-10 flex h-[160px] w-full flex-col items-center justify-center gap-2 rounded border border-gray-300 bg-gray-100 text-main-500">
      <MaximizeSquare3 size={32} />
      <div>Add your first block</div>
    </div>
  );
};

const SortableBlock: FC<SortableBlockProps> = ({
  block,
  teams,
  departments,
  managers,
  countries,
  workspaceCountries,
  timeOffsTypes,
  deleteBlock,
  updateBlock,
}) => {
  const { id } = block;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Translate.toString(transform),
    transition,
  };

  const label = useMemo(() => {
    if (block.text) {
      return "Text";
    }

    if (block.button) {
      return "Button";
    }

    if (block.timeOffs) {
      return "Time Offs";
    }

    if (block.celebrations) {
      return "Celebrations";
    }

    if (block.holidays) {
      return "Holidays";
    }

    return "";
  }, [block]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="relative cursor-default rounded border border-gray-300 bg-white p-4 shadow-primary"
    >
      <div className="mb-4 flex items-center gap-2">
        <DragHandle listeners={listeners} isDragging={isDragging} />
        <div className="flex-1 font-bold">{label}</div>
        <CloseButton type="secondary" onClick={() => deleteBlock(id)} />
      </div>
      {block.text ? (
        <TextBlock
          block={{
            id: block.id,
            text: block.text,
          }}
          updateBlock={updateBlock}
        />
      ) : null}
      {block.button ? (
        <ButtonBlock
          block={{
            id: block.id,
            button: block.button,
          }}
          updateBlock={updateBlock}
        />
      ) : null}
      {block.timeOffs ? (
        <TimeOffsBlock
          block={{
            id: block.id,
            timeOffs: block.timeOffs,
          }}
          teams={teams}
          departments={departments}
          managers={managers}
          countries={countries}
          timeOffsTypes={timeOffsTypes}
          updateBlock={updateBlock}
        />
      ) : null}
      {block.celebrations ? (
        <CelebrationsBlock
          block={{
            id: block.id,
            celebrations: block.celebrations,
          }}
          teams={teams}
          departments={departments}
          managers={managers}
          countries={countries}
          updateBlock={updateBlock}
        />
      ) : null}
      {block.holidays ? (
        <HolidaysBlock
          block={{
            id: block.id,
            holidays: block.holidays,
          }}
          countries={workspaceCountries}
          updateBlock={updateBlock}
        />
      ) : null}
    </div>
  );
};

const DraggableButton: FC<DraggableButtonProps> = ({ button, addBlock }) => {
  const { id, label, tooltip } = button;
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({ id });

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "relative flex cursor-default items-center gap-2 border-b border-gray-300 bg-white px-4 py-4",
        {
          "z-10 border": isDragging,
        }
      )}
      style={style}
      {...attributes}
    >
      <DragHandle listeners={listeners} isDragging={isDragging} />
      <div>{label}</div>
      {!isDragging ? (
        <Tooltip
          message={tooltip}
          className="mb-1 h-[16px]"
          tooltipClassName=""
          placement="right"
        >
          <InfoCircle />
        </Tooltip>
      ) : null}
      <button
        className="ml-auto rounded p-1.5 hover:bg-gray-100"
        type="button"
        onClick={() => addBlock(button.id)}
      >
        <PlusSVG />
      </button>
    </div>
  );
};

interface ValidationErrorProps {
  errors: {
    id: string;
    error: string;
  }[];
}

const ValidationError = ({ errors }: ValidationErrorProps): JSX.Element => {
  return (
    <div className="flex flex-col gap-2 text-left">
      <ul className="ml-4 list-disc text-red-500">
        {errors.map((error) => (
          <li key={error.id}>{error.error}</li>
        ))}
      </ul>
    </div>
  );
};

interface DroppableCanvasProps extends PropsWithChildren {
  id: string;
}

const DroppableCanvas: FC<DroppableCanvasProps> = ({ id, children }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      aria-label="Droppable region"
      className="relative h-full overflow-auto"
    >
      {children}
      <DotBackground
        className={cn("absolute top-0 left-0 z-[-1]", {
          "bg-violet-300": isOver,
        })}
      />
    </div>
  );
};

const BLOCK_BUTTONS: BlockButton[] = [
  {
    id: NotificationBlockType.Text,
    label: "Text",
    tooltip: (
      <>
        <div className="mb-2 w-full max-w-[250px] text-left">
          You can use this block type to send any text content you need, such as
          reminders, calls to action, or other important information
        </div>
        <TextBlockSVG />
      </>
    ),
  },
  {
    id: NotificationBlockType.Button,
    label: "Button",
    tooltip: (
      <>
        <div className="mb-2 w-full max-w-[250px] text-left">
          You can use this block type to add interactive buttons to your
          notifications, such as{" "}
          <strong className="whitespace-nowrap">Request Time Off</strong>,{" "}
          <strong className="whitespace-nowrap">Give Kudos</strong> or even
          custom links
        </div>
        <ButtonBlockSVG />
      </>
    ),
  },
  {
    id: NotificationBlockType.TimeOffs,
    label: "Time Offs",
    tooltip: (
      <>
        <div className="mb-2 w-full max-w-[250px] text-left">
          You can use this block type to display the time off requests you have
          in the system
        </div>
        <TimeOffsBlockSVG />
      </>
    ),
  },
  {
    id: NotificationBlockType.Celebrations,
    label: "Celebrations",
    tooltip: (
      <>
        <div className="mb-2 w-full max-w-[250px] text-left">
          You can use this block type to display the celebrations you have in
          the system
        </div>
        <CelebrationsBlockSVG />
      </>
    ),
  },
  {
    id: NotificationBlockType.Holidays,
    label: "Holidays",
    tooltip: (
      <>
        <div className="mb-2 w-full max-w-[250px] text-left">
          You can use this block type to display the holidays you have in the
          system
        </div>
        <HolidaysBlockSVG />
      </>
    ),
  },
];

const frequencyOptions = [
  { title: "Daily", value: NotificationsFrequency.Day },
  { title: "Weekly", value: NotificationsFrequency.Week },
  { title: "Monthly", value: NotificationsFrequency.Month },
];

const blocksToNotificationBlockInput = (
  blocks: NotificationBlock[]
): (NotificationBlockInput & {
  id: UniqueIdentifier;
})[] => {
  return blocks.map((b, i) => {
    // NOTE: id can not be zero https://stackoverflow.com/a/73936369
    const id = i + 1;

    if (isTextBlock(b)) {
      return {
        id,
        text: {
          text: b.text,
        },
      };
    }

    if (isButtonBlock(b)) {
      return {
        id,
        button: {
          buttonType: b.buttonType,
          title: b.title,
          url: b.url,
        },
      };
    }

    if (isTimeOffsBlock(b)) {
      return {
        id,
        timeOffs: {
          status: b.status,
          managers: b.managers,
          departments: b.departments,
          teams: b.teams,
          countries: b.countries,
          types: b.types,
        },
      };
    }

    if (isCelebrationsBlock(b)) {
      return {
        id,
        celebrations: {
          managers: b.managers,
          departments: b.departments,
          teams: b.teams,
          countries: b.countries,
          type: b.celebrationsType,
        },
      };
    }

    if (isHolidaysBlock(b)) {
      return {
        id,
        holidays: {
          countries: b.countries,
          includeDescription: b.includeDescription,
          type: b.holidaysType,
        },
      };
    }

    throw new Error("Unknown block type");
  });
};

function isValidUrl(url: string): boolean {
  try {
    return Boolean(new URL(url));
  } catch (e) {
    return false;
  }
}

const EditableNotificationSidebar: FC<Props> = ({
  notificationId,
  closeSidebar,
  presetData,
  channels,
  teams,
  departments,
  managers,
  countries,
  workspaceCountries,
  timeOffsTypes,
}) => {
  const router = useRouter();
  const toast = useToast();
  // TODO: add loading interface
  const [getNotification] = useNotificationLazyQuery();
  const notificationLoaded = useRef(false);
  const [title, setTitle] = useState<string>(presetData?.title ?? "");
  /**
   * NOTE: Default settings for a new notifications are:
   * frequency: Week
   * day: current day of a week
   */
  const [settings, setSettings] = useState<SettingsState>({
    frequency: presetData?.settings.frequency ?? NotificationsFrequency.Week,
    day: presetData?.settings.day ?? (new Date().getDay() || 7),
    time: presetData?.settings.time ?? DEFAULT_TIME,
    timezone: DEFAULT_TIMEZONE,
    channelId: null,
  });
  const DayIconPrefix = useCallback(
    () => (
      <Tooltip
        message="Select a day of the month to post. If the month has fewer days, the notification will be sent on the last day"
        className="mb-1 h-[16px]"
      >
        <InfoCircle />
      </Tooltip>
    ),
    []
  );

  const [blocks, setBlocks] = useState<
    (NotificationBlockInput & {
      id: UniqueIdentifier;
    })[]
  >(presetData ? blocksToNotificationBlockInput(presetData.blocks) : []);

  useEffect(() => {
    if (notificationId && !notificationLoaded.current) {
      void getNotification({
        variables: {
          id: notificationId,
        },
        onCompleted: ({ notification }) => {
          notificationLoaded.current = true;

          setBlocks(blocksToNotificationBlockInput(notification.blocks));
          setSettings({
            frequency: notification.settings.frequency,
            day: notification.settings.day ?? null,
            time: notification.settings.time,
            timezone: notification.settings.timezone,
            channelId: notification.settings.channel?.id ?? null,
          });
          setTitle(notification.title);
        },
      });
    }
  }, [notificationId, getNotification]);

  const addBlock = useCallback(
    (type: NotificationBlockType) => {
      setBlocks((prev) => {
        // NOTE: id can not be zero https://stackoverflow.com/a/73936369
        const id = prev.length + 1;

        if (type === NotificationBlockType.Text) {
          return [
            ...prev,
            {
              id,
              text: {
                text: "",
              },
            },
          ];
        }

        if (type === NotificationBlockType.Button) {
          return [
            ...prev,
            {
              id,
              button: {
                buttonType: ButtonBlockType.Link,
                title: "",
                url: "",
              },
            },
          ];
        }

        if (type === NotificationBlockType.TimeOffs) {
          return [
            ...prev,
            {
              id,
              timeOffs: {
                status: TimeOffRequestStatus.Approved,
                managers: [],
                departments: [],
                teams: [],
                countries: [],
                types: [],
              },
            },
          ];
        }

        if (type === NotificationBlockType.Celebrations) {
          return [
            ...prev,
            {
              id,
              celebrations: {
                managers: [],
                departments: [],
                teams: [],
                countries: [],
              },
            },
          ];
        }

        return [
          ...prev,
          {
            id,
            holidays: {
              countries: [],
              includeDescription: true,
            },
          },
        ];
      });
    },
    [setBlocks]
  );

  const handleBlockDrop = (event: DragEndEvent): void => {
    const { active, over } = event;

    if (over?.id === "canvas") {
      addBlock(active.id as NotificationBlockType);
    }
  };

  const handleBlocksSort = (event: DragEndEvent): void => {
    const { active, over } = event;

    if (over && active.id && active.id !== over.id) {
      /**
       * NOTE: we sorting existing blocks
       */
      setBlocks((prev) => {
        const activeItem = prev.find((b) => b.id === active.id);
        const overItem = prev.find((b) => b.id === over.id);

        if (!activeItem || !overItem) {
          return prev;
        }
        const oldIndex = prev.indexOf(activeItem);
        const newIndex = prev.indexOf(overItem);

        return arrayMove(prev, oldIndex, newIndex);
      });
    }
  };

  const deleteBlock = useCallback((id: UniqueIdentifier) => {
    setBlocks((prev) => prev.filter((b) => b.id !== id));
  }, []);

  const updateBlock = useCallback(
    (id: UniqueIdentifier, block: NotificationBlockInput) =>
      setBlocks((prev) => {
        const index = prev.findIndex((b) => b.id === id);

        if (index === -1) {
          return prev;
        }

        return [
          ...prev.slice(0, index),
          {
            id,
            ...block,
          },
          ...prev.slice(index + 1),
        ];
      }),
    []
  );

  const channelsOptions = channels.map((ch) => ({
    title: `${ch.name}`,
    value: ch.id,
    prefix: "#",
  }));
  const [activeTab, setActiveTab] = useState<TabBarNotification>(
    TabBarNotification.Blocks
  );

  const tabs = [
    { id: TabBarNotification.Blocks, label: "Blocks" },
    { id: TabBarNotification.Settings, label: "Publish Settings" },
  ];

  const toggleTab = useCallback((tab: TabBarNotification) => {
    setActiveTab(tab);
  }, []);

  const handleCloseSidebar = useCallback(() => {
    closeSidebar();
  }, [closeSidebar]);

  const [addNotification] = useAddNotificationMutation();
  const [updateNotification] = useUpdateNotificationMutation();
  const [postNotificationPreview] = usePostNotificationPreviewMutation();

  const validationErrors = useMemo(() => {
    const errors: {
      id: string;
      error: string;
    }[] = [];

    if (!title.trim()) {
      errors.push({ id: "title", error: "Notification title is required" });
    }

    if (!settings.channelId) {
      errors.push({ id: "channel", error: "Channel is required" });
    }

    if (!blocks.length) {
      errors.push({ id: "blocksLengths", error: "Add at least one block" });
    }

    blocks.forEach((b) => {
      if (
        b.text &&
        !b.text.text.trim() &&
        !errors.find((e) => e.id === "textBlock")
      ) {
        errors.push({
          id: "textBlock",
          error: "Text block's content can not be empty",
        });
      }

      if (b.button) {
        if (
          !b.button.title.trim() &&
          !errors.find((e) => e.id === "buttonTitle")
        ) {
          errors.push({
            id: "buttonTitle",
            error: "Button title can not be empty",
          });
        }

        if (
          b.button.buttonType === ButtonBlockType.Link &&
          (!b.button.url?.trim() || !isValidUrl(b.button.url.trim())) &&
          !errors.find((e) => e.id === "buttonUrl")
        ) {
          errors.push({
            id: "buttonUrl",
            error: "Button URL should be a valid URL",
          });
        }
      }
    });

    return errors;
  }, [title, settings, blocks]);

  const nextButtonValidationErrors = useMemo(
    () => validationErrors.filter((e) => e.id === "blocksLengths"),
    [validationErrors]
  );

  const testNotificationErrors = useMemo(
    () =>
      validationErrors.filter((e) => e.id !== "channel" && e.id !== "title"),
    [validationErrors]
  );

  const postTestNotificationHandler = useCallback(() => {
    if (testNotificationErrors.length > 0) {
      return;
    }

    void postNotificationPreview({
      variables: {
        data: {
          frequency: settings.frequency,
          blocks: blocks.map((b) => ({
            ...b,
            id: undefined,
          })),
        },
      },
      onCompleted: () => {
        toast("success", "Preivew notification sent successfully");
      },
    });
  }, [
    postNotificationPreview,
    toast,
    settings.frequency,
    blocks,
    testNotificationErrors,
  ]);

  const handleOnNextClick = useCallback(
    () => setActiveTab(TabBarNotification.Settings),
    []
  );

  const submitHandler = useCallback(() => {
    if (validationErrors.length || !settings.channelId) {
      return;
    }

    if (notificationId) {
      void updateNotification({
        variables: {
          id: notificationId,
          data: {
            title,
            settings: {
              frequency: settings.frequency,
              time: settings.time,
              timezone: settings.timezone,
              channelId: settings.channelId,
              day: settings.day ?? undefined,
            },
            blocks: blocks.map((b) => ({
              ...b,
              id: undefined,
            })),
          },
        },
        onCompleted: () => {
          closeSidebar();
          toast("success", "Notification updated successfully");
        },
      });
    } else {
      addNotification({
        variables: {
          data: {
            title,
            settings: {
              frequency: settings.frequency,
              time: settings.time,
              timezone: settings.timezone,
              channelId: settings.channelId,
              day: settings.day ?? undefined,
            },
            blocks: blocks.map((b) => ({
              ...b,
              id: undefined,
            })),
          },
        },
        refetchQueries: ["NotificationsPage"],
        onCompleted: () => {
          if (router.pathname !== "/settings/notifications") {
            void router.push("/settings/notifications");
          } else {
            closeSidebar();
          }

          toast("success", "Notification created successfully");
        },
      }).catch(() => {
        toast(
          "error",
          "An error occurred while creating the notification. Please try again later or contact support using the in-app chat."
        );
      });
    }
  }, [
    notificationId,
    closeSidebar,
    toast,
    router,
    updateNotification,
    addNotification,
    title,
    settings,
    blocks,
    validationErrors.length,
  ]);

  return (
    <Sidebar
      size="large"
      menu={[]}
      title={
        <ExpandableInput
          dataTest="notification-title-input"
          maxLength={200}
          focusInitially
          placeholder="Enter notification title"
          value={title}
          onChange={setTitle}
        />
      }
      closeSidebar={handleCloseSidebar}
      footer={
        <div className="flex justify-end gap-x-5">
          <Button
            dataTest="sidebar-cancel"
            variant="outline"
            color="secondary"
            onClick={handleCloseSidebar}
          >
            Cancel
          </Button>

          <Tooltip
            hide={testNotificationErrors.length === 0}
            message={ValidationError({ errors: testNotificationErrors })}
          >
            <Button
              disabled={testNotificationErrors.length > 0}
              dataTest="sidebar-preview"
              variant="outline"
              color="primary"
              onClick={postTestNotificationHandler}
            >
              Send a Preview
            </Button>
          </Tooltip>

          {activeTab === TabBarNotification.Blocks &&
          settings.channelId == null ? (
            <Tooltip
              message={ValidationError({ errors: nextButtonValidationErrors })}
              hide={nextButtonValidationErrors.length === 0}
            >
              <Button
                onClick={handleOnNextClick}
                disabled={nextButtonValidationErrors.length > 0}
                dataTest="sidebar-next"
              >
                Next
              </Button>
            </Tooltip>
          ) : (
            <Tooltip
              message={ValidationError({ errors: validationErrors })}
              hide={validationErrors.length === 0}
            >
              <Button
                disabled={!!validationErrors.length}
                onClick={submitHandler}
                dataTest={notificationId ? "sidebar-save" : "sidebar-create"}
              >
                {notificationId ? "Save" : "Create"}
              </Button>
            </Tooltip>
          )}
        </div>
      }
    >
      <div className="grid h-full grid-rows-[60px_1fr] overflow-auto">
        <nav
          data-test="notification-sidebar-navigation"
          className="flex gap-2 border-b px-4 py-2"
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.id as string}
              isActive={() => activeTab === tab.id}
              onClick={() => toggleTab(tab.id)}
            >
              {tab.label}
            </Tab>
          ))}
        </nav>
        {activeTab === TabBarNotification.Blocks ? (
          <DndContext onDragEnd={handleBlockDrop}>
            <div className="grid h-full grid-cols-[30%_70%] overflow-hidden">
              <div
                className="border-r border-gray-300"
                data-test="block-buttons"
              >
                {BLOCK_BUTTONS.map((button) => (
                  <DraggableButton
                    key={button.id}
                    button={button}
                    addBlock={addBlock}
                  />
                ))}
              </div>
              <DroppableCanvas id="canvas">
                <DndContext
                  modifiers={[restrictToVerticalAxis]}
                  onDragEnd={handleBlocksSort}
                >
                  <SortableContext
                    items={blocks}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="flex flex-col gap-6 p-6">
                      {!blocks.length ? <CTABlock /> : null}
                      {blocks.map((block) => (
                        <SortableBlock
                          key={block.id}
                          block={block}
                          teams={teams}
                          departments={departments}
                          managers={managers}
                          countries={countries}
                          workspaceCountries={workspaceCountries}
                          timeOffsTypes={timeOffsTypes}
                          updateBlock={updateBlock}
                          deleteBlock={deleteBlock}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              </DroppableCanvas>
            </div>
          </DndContext>
        ) : null}
        {activeTab === TabBarNotification.Settings ? (
          <div className="max-w-[764px] px-6">
            <Field
              label="Channel"
              description="Select a public channel where notifications will be posted"
              className="border-b"
            >
              <div className="w-1/2 pr-2">
                <Select
                  data-test="notification-channel-select"
                  placeholder="Select a channel"
                  allowClear
                  value={
                    channelsOptions.find(
                      (ch) => ch.value === settings.channelId
                    ) ?? null
                  }
                  options={channelsOptions}
                  onChange={(o) =>
                    setSettings((s) => ({
                      ...s,
                      channelId: o?.value ?? null,
                    }))
                  }
                />
              </div>
            </Field>

            <Field
              label="Time of posting"
              description="Select the time when notifications will be posted"
              className="border-b"
            >
              <div className="grid grid-cols-2 gap-4">
                <TimeSelect
                  placeholder="Select time"
                  allowClear={false}
                  value={
                    timeOptions.find((o) => o.value === settings.time) ?? null
                  }
                  onChange={(o) =>
                    setSettings((s) => ({
                      ...s,
                      time: o?.value ?? DEFAULT_TIME,
                    }))
                  }
                />
                <TimezoneSelect
                  placeholder="Select time"
                  allowClear={false}
                  value={
                    timezoneOptions.find(
                      (o) => o.value === settings.timezone
                    ) ?? null
                  }
                  onChange={(o) =>
                    setSettings((s) => ({
                      ...s,
                      timezone: o?.value ?? DEFAULT_TIMEZONE,
                    }))
                  }
                />
              </div>
            </Field>

            <Field
              label="Frequency"
              description="Select how often notifications will be published"
            >
              <div className="grid grid-cols-2 gap-4">
                <Select
                  placeholder="Select frequency"
                  allowClear={false}
                  value={
                    frequencyOptions.find(
                      (o) => o.value === settings.frequency
                    ) ?? null
                  }
                  options={frequencyOptions}
                  onChange={(o) => {
                    let day = 1;

                    if (o?.value === NotificationsFrequency.Week) {
                      day = new Date().getDay() || 7;
                    }

                    if (o?.value === NotificationsFrequency.Month) {
                      day = new Date().getDate();
                    }

                    setSettings((s) => ({
                      ...s,
                      frequency: o?.value ?? NotificationsFrequency.Week,
                      day,
                    }));
                  }}
                />

                {settings.frequency === NotificationsFrequency.Week ? (
                  <DaySelect
                    placeholder="Select day"
                    allowClear={false}
                    value={
                      dayOptions.find(
                        (o) => o.value === `${settings.day ?? 1}`
                      ) ?? null
                    }
                    onChange={(o) =>
                      setSettings((s) => ({
                        ...s,
                        day: o?.value ? parseInt(o.value, 10) : null,
                      }))
                    }
                  />
                ) : null}
                {settings.frequency === NotificationsFrequency.Month ? (
                  <div className="mt-2.5 flex w-full items-center gap-2">
                    <Input
                      type="number"
                      min="1"
                      max="31"
                      value={settings.day ? `${settings.day}` : "1"}
                      onChange={(value) => {
                        let day = value ? parseInt(value, 10) : null;

                        if (day && day > 31) {
                          day = 31;
                        }

                        setSettings((s) => ({
                          ...s,
                          day,
                        }));
                      }}
                      wrapperClassnames="w-full"
                      getBeforeIconBlock={DayIconPrefix}
                    />
                  </div>
                ) : null}
              </div>
            </Field>
          </div>
        ) : null}
      </div>
    </Sidebar>
  );
};

export default EditableNotificationSidebar;
