import { LinkButton } from "../../Button";
import { LinkOptions } from "../../Users/<USER>";

import TextField from "./TextField";
import { FieldProps } from "./types";

const LinkField = <Field extends string | null>({
  fieldData,
  isReadonly,
  columnData,
  ...rest
}: FieldProps<Field, LinkOptions<Field>>): JSX.Element => {
  if (isReadonly) {
    return (
      <div className="truncate">
        <LinkButton href={columnData.getLink(fieldData)}>
          {fieldData}
        </LinkButton>
      </div>
    );
  }

  return (
    <TextField
      isReadonly={false}
      fieldData={fieldData}
      columnData={columnData}
      {...rest}
    />
  );
};

export default LinkField;
