import { SlackProfile } from "../../../helpers/flattenMember";
import { LinkButton } from "../../Button";
import { LinkOptions } from "../../Users/<USER>";

import { FieldProps } from "./types";

const SlackField = <Field extends SlackProfile>({
  fieldData,
  columnData,
}: FieldProps<Field, LinkOptions<Field>>): JSX.Element | null => {
  const link = columnData.getLink(fieldData);

  return (
    <div className="truncate">
      {link ? (
        <LinkButton href={columnData.getLink(fieldData)}>
          {fieldData.displayName}
        </LinkButton>
      ) : (
        fieldData.displayName
      )}
    </div>
  );
};

export default SlackField;
