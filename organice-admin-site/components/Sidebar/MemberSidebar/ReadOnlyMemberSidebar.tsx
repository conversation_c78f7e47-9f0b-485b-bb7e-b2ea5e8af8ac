import cn from "classnames";
import { FC, useMemo } from "react";

import { getDepartmentColors } from "../../../helpers/colors";
import { EditableFlatMember } from "../../../helpers/flattenMember";
import useGetUserFields, { UserField } from "../../../hooks/useGetUserFields";
import FieldsGroup from "../Fields/FieldsGroup";
import {
  FieldsGroup as FieldsGroupType,
  MemberFieldPolicy,
} from "../Fields/types";
import Sidebar from "../Sidebar";

interface Props {
  memberData: {
    member: EditableFlatMember;
    memberFields: MemberFieldPolicy[];
  };

  closeSidebar: () => void;
}

const ReadOnlyMemberSidebar: FC<Props> = ({ memberData, closeSidebar }) => {
  const { member, memberFields } = memberData;

  const getUseFields = useGetUserFields({
    memberFields: memberFields.map((f) => ({ ...f, isPublic: true })),
    members: [],
    teams: [],
    possibleMembers: [],
    possiblePositions: [],
    possibleDepartments: [],
    nonFilledFields: [],
    currentMember: {
      isAdmin: false,
    },
    onCreateTeam: () => {},
  });

  const departmentColor = useMemo(
    () =>
      member.department?.value.departmentColor
        ? getDepartmentColors(member.department.value.departmentColor)
        : undefined,
    [member]
  );

  const fields = getUseFields({
    isReadonly: true,
  })
    .filter((field) => field.isPublic)
    .map((field) => ({
      ...field,
      tooltipMessage: undefined,
    }));

  const headerGroup: FieldsGroupType<UserField[]> = {
    key: "header",
    label: "",
    color: departmentColor,
    fields: [],
  };
  const contactsGroup: FieldsGroupType<UserField[]> = {
    key: "contact",
    label: "Contact information",
    fields: [],
  };

  const orgChartGroup: FieldsGroupType<UserField[]> = {
    key: "org",
    label: "Org chart information",
    fields: [],
  };

  const aboutMeGroup: FieldsGroupType<UserField[]> = {
    key: "about",
    label: "About me",
    fields: [],
  };

  const groups = [headerGroup, contactsGroup, orgChartGroup, aboutMeGroup];

  fields.forEach((field) => {
    if (["photo", "name", "jobTitle"].includes(field.key)) {
      headerGroup.fields.push(field);
    } else if (["email", "slack", "organicePhone"].includes(field.key)) {
      contactsGroup.fields.push(field);
    } else if (["manager", "teams", "department"].includes(field.key)) {
      orgChartGroup.fields.push(field);
    } else {
      if (field.key === "birthday" && member.birthday?.hideBirthday) {
        return;
      }
      aboutMeGroup.fields.push(field);
    }
  });

  return (
    <Sidebar closeSidebar={closeSidebar} menu={[]} hasIntercom={false}>
      <div className={cn("h-6", departmentColor)} />
      <form
        id="position"
        data-test="sidebar-form"
        className="flex flex-col divide-y divide-gray-300 pb-6 children:px-4"
      >
        {groups.map((group, index) => {
          return (
            <FieldsGroup
              key={group.key}
              data={member}
              label={group.label}
              className={cn(group.color, {
                "pt-6": index,
              })}
              fields={group.fields}
              setFieldData={() => {}}
            />
          );
        })}
      </form>
    </Sidebar>
  );
};

export default ReadOnlyMemberSidebar;
