import { BalanceChangedType } from "@organice/core/domain/activityLog";
import pluralize from "pluralize";
import { FC } from "react";

import { BalanceLogType } from "../../../../../graphql/client.generated";
import { getMemberName, Member } from "../../../CalendarSidebar/utils";

import ActivityLogItem from "./ActivityLogItem";

interface Props {
  createdAt: Date;
  type: BalanceLogType;
  increase?: number;
  absolute?: number;
  requestType: {
    label: string;
    emoji: string;
  };
  member: Member;
  initiator?: Member;
  isLast?: boolean;
}

const BalanceLog: FC<Props> = ({
  createdAt,
  type,
  requestType,
  increase,
  absolute,
  member,
  initiator,
  isLast,
}) => {
  const content = {
    [BalanceChangedType.accruals]: increase ? (
      <div>
        <b>{getMemberName(member)}</b> accured <b>{increase}</b>{" "}
        <b>{pluralize("day", increase)}</b> of{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>
      </div>
    ) : null,
    [BalanceChangedType.reset]: (
      <div>
        <b>{getMemberName(member)}</b>{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>{" "}
        balance was reset to 0
      </div>
    ),
    [BalanceChangedType.manual]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> manually updated{" "}
        <b>{getMemberName(member)}</b>{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>{" "}
        balance to <b>{absolute}</b>
      </div>
    ) : null,
    [BalanceChangedType.yearStart]: increase ? (
      <div>
        <b>{getMemberName(member)}</b> accured <b>{increase}</b>{" "}
        <b>{pluralize("day", increase)}</b> of{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>{" "}
        at the PTO year
      </div>
    ) : null,
    [BalanceChangedType.rollOver]: absolute ? (
      <div>
        <b>{getMemberName(member)}</b>{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>{" "}
        balance of <b>{absolute}</b> was rolled over to the next year
      </div>
    ) : null,
    [BalanceChangedType.teamJoin]: (
      <div>
        <b>{getMemberName(member)}</b> accured <b>{increase}</b>{" "}
        <b>{pluralize("day", increase)}</b> of{" "}
        <b>
          {requestType.emoji} {requestType.label}
        </b>{" "}
        after joining the organization
      </div>
    ),
    [BalanceChangedType.requestUpdate]:
      increase && initiator ? (
        <div>
          <b>{getMemberName(member)}</b>{" "}
          <b>
            {requestType.emoji} {requestType.label}
          </b>{" "}
          balance was {increase > 0 ? "increased" : "decreased"} by{" "}
          <b>{increase}</b> <b>{pluralize("day", increase)}</b> after{" "}
          <b>{getMemberName(initiator)}</b> had approved or had updated their
          request.
        </div>
      ) : null,
    [BalanceChangedType.requestDelete]:
      increase && initiator ? (
        <div>
          <b>{getMemberName(member)}</b>{" "}
          <b>
            {requestType.emoji} {requestType.label}
          </b>{" "}
          balance increased by <b>{increase}</b>{" "}
          <b>{pluralize("day", increase)}</b> after{" "}
          <b>{getMemberName(initiator)}</b> had deleted their request
        </div>
      ) : null,
  };

  return (
    <ActivityLogItem
      type="balance"
      createdAt={createdAt}
      content={content[type]}
      isLast={isLast}
    />
  );
};

export default BalanceLog;
