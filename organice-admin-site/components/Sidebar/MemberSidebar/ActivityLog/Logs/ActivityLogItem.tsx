import { format } from "date-fns";
import { FC, ReactNode } from "react";
import { ClockSquare } from "solar-icon-set";

import ActivityLogIcon from "./ActivityLogIcon";

interface Props {
  type?: "balance" | "timeOff" | "slackStatus";
  createdAt: Date;
  content: ReactNode;
  isLast?: boolean;
}

const LOG_TIME_FORMAT = "h:mm a";

const ActivityLogItem: FC<Props> = ({ type, createdAt, content, isLast }) => {
  return (
    <div className="flex items-stretch gap-4 px-4">
      <div className="size-7 relative">
        {!isLast && (
          <div className="absolute left-1/2 bottom-[-3px] z-[0] h-[calc(100%-25px)] w-px -translate-x-1/2 bg-gray-200" />
        )}
        <div className="relative z-[1]">
          <ActivityLogIcon type={type} />
        </div>
      </div>

      <div className="min-w-0 flex-1 pb-4">
        <div className="mb-2 text-sm leading-relaxed text-gray-900">
          {content}
        </div>
        <div className="inline-flex gap-2 text-xs text-gray-500">
          <ClockSquare />
          {format(createdAt, LOG_TIME_FORMAT)}
        </div>
      </div>
    </div>
  );
};

export default ActivityLogItem;
