import { SlackStatusChangedType } from "@organice/core/domain/activityLog";
import { FC } from "react";

import { SlackStatusLogType } from "../../../../../graphql/client.generated";
import { getM<PERSON>berN<PERSON>, Member } from "../../../CalendarSidebar/utils";

import ActivityLogItem from "./ActivityLogItem";

interface Props {
  createdAt: Date;
  type: SlackStatusLogType;
  previousStatus?: string;
  newStatus: string;
  member: Member;
  isLast?: boolean;
}

const SlackStatusLog: FC<Props> = ({
  createdAt,
  type,
  previousStatus,
  newStatus,
  member,
  isLast,
}) => {
  const content = {
    [SlackStatusChangedType.set]: (
      <div>
        <b>{getMemberName(member)}</b> status was set to <b>{newStatus}</b>
      </div>
    ),

    [SlackStatusChangedType.revert]: (
      <div>
        <b>{getMemberName(member)}</b> status was reverted
        {previousStatus && (
          <>
            {" "}
            back to <b>{previousStatus}</b>
          </>
        )}
      </div>
    ),
  };

  return (
    <ActivityLogItem
      type="slackStatus"
      createdAt={createdAt}
      content={content[type]}
      isLast={isLast}
    />
  );
};

export default SlackStatusLog;
