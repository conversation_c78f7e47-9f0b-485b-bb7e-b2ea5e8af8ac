import { FC } from "react";
import { ChatLine, Calendar, RefreshSquare } from "solar-icon-set";

interface ActivityLogIconProps {
  type?: "balance" | "timeOff" | "slackStatus";
  className?: string;
}
const color = "#94A3B8";

const ActivityLogIcon: FC<ActivityLogIconProps> = ({ type, className }) => {
  const getIconContent = (): JSX.Element | null => {
    if (type === "balance") {
      return <RefreshSquare color={color} size={30} className={className} />;
    }

    if (type === "timeOff") {
      return <Calendar color={color} size={30} className={className} />;
    }

    if (type === "slackStatus") {
      return <ChatLine color={color} size={30} className={className} />;
    }

    return null;
  };

  return getIconContent();
};

export default ActivityLogIcon;
