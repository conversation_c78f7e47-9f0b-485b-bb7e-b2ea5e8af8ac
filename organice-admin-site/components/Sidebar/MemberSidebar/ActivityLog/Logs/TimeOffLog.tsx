import { TimeOffRequestChangedType } from "@organice/core/domain/activityLog";
import formatDate from "@organice/core/utils/formatDate";
import { isSameDay } from "date-fns";
import { FC } from "react";

import { TimeOffLogType } from "../../../../../graphql/client.generated";
import { getMemberName, Member } from "../../../CalendarSidebar/utils";

import ActivityLogItem from "./ActivityLogItem";

interface Props {
  createdAt: Date;
  type: TimeOffLogType;
  request: {
    type: {
      label: string;
      emoji: string;
    };
    startDate: Date;
    endDate: Date;
  };
  member: Member;
  initiator?: Member;
  isLast?: boolean;
}

const TimeOffLog: FC<Props> = ({
  createdAt,
  type,
  request,
  member,
  initiator,
  isLast,
}) => {
  const interval = isSameDay(request.startDate, request.endDate)
    ? `on ${formatDate(request.startDate)}`
    : `from ${formatDate(request.startDate)} to ${formatDate(request.endDate)}`;

  const content = {
    /**
     * NOTE: initiator is set when an admin created a request
     * on behalf of a member
     */
    [TimeOffRequestChangedType.create]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> created a{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        <b>{interval}</b> for <b>{getMemberName(member)}</b>
      </div>
    ) : (
      <div>
        <b>{getMemberName(member)}</b> requested for a{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        <b>{interval}</b>
      </div>
    ),

    [TimeOffRequestChangedType.update]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> edited <b>{getMemberName(member)}</b>{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        request. The updated request is{" "}
      </div>
    ) : null,
    [TimeOffRequestChangedType.approve]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> approved{" "}
        <b>{getMemberName(member)}</b>{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        request <b>{interval}</b>
      </div>
    ) : null,
    [TimeOffRequestChangedType.reject]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> rejected{" "}
        <b>{getMemberName(member)}</b>{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        request <b>{interval}</b>
      </div>
    ) : null,
    [TimeOffRequestChangedType.delete]: initiator ? (
      <div>
        <b>{getMemberName(initiator)}</b> deleted <b>{getMemberName(member)}</b>{" "}
        <b>
          {request.type.emoji} {request.type.label}
        </b>{" "}
        request <b>{interval}</b>
      </div>
    ) : null,
  };

  return (
    <ActivityLogItem
      type="timeOff"
      createdAt={createdAt}
      content={content[type]}
      isLast={isLast}
    />
  );
};

export default TimeOffLog;
