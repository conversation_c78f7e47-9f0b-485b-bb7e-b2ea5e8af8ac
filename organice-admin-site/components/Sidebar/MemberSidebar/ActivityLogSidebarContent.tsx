import pluralize from "pluralize";
import { FC, useState, useCallback, useMemo } from "react";
import { NotebookMinimalistic } from "solar-icon-set";

import {
  useCalendarSidebarQuery,
  TimeOffRequestType,
} from "../../../graphql/client.generated";
import { Choice } from "../../../helpers/flattenMember";
import {
  selectedDateToRange,
  DATES_OPTIONS,
} from "../../../helpers/getSelectedDataToRange";
import DateSeparator from "../../DateSeparator";
import MultipleFiltersDropdown, {
  MultipleSelectFilter,
  FilterType,
  SelectFilter,
} from "../../MultipleFiltersDropdown";
import SkeletonEvents from "../../SkeletonEvents";
import {
  getMemberName,
  groupLogsByDate,
  getTotalLogsCount,
} from "../CalendarSidebar/utils";
import SidebarAvatar from "../SidebarAvatar";

import BalanceLog from "./ActivityLog/Logs/BalanceLog";
import SlackStatusLog from "./ActivityLog/Logs/SlackStatusLog";
import TimeOffLog from "./ActivityLog/Logs/TimeOffLog";

interface Props {
  memberId: string;
}

const defaultRange = "ThisMonth";

const EVENT_TYPE_OPTIONS: Choice<string>[] = [
  { label: "Leave Request", value: "timeOffRequestChanged" },
  { label: "Time Off Balance", value: "balanceChanged" },
  { label: "Status Update", value: "slackStatusChanged" },
];

const ActivityLogTab: FC<Props> = ({ memberId: id }) => {
  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>(defaultRange);
  const [range, setRange] = useState<[Date, Date]>(
    selectedDateToRange(selectedDate)
  );

  const { data, loading, previousData } = useCalendarSidebarQuery({
    variables: {
      memberId: id,
      from: range[0],
      to: range[1],
      types: selectedEventTypes.length
        ? selectedEventTypes
        : EVENT_TYPE_OPTIONS.map((option) => option.value),
    },
    fetchPolicy: "cache-and-network",
  });

  const {
    logs = [],
    member,
    timeOffTypes: types = [],
  } = data ?? previousData ?? {};
  const logGroups = groupLogsByDate(logs);
  const totalCount = getTotalLogsCount(logGroups);
  const getType = (typeId: string): TimeOffRequestType | null =>
    types.find((t) => t.id === typeId) ?? null;

  const filterFields = useMemo(
    () => [
      {
        id: "eventType",
        filterName: "Event Type",
        filterType: FilterType.MultipleSelect,
        filterValue: selectedEventTypes,
        columnData: {
          choices: EVENT_TYPE_OPTIONS,
          isMulti: true,
          placeholder: "Select event types",
        },
        onFilterApply: (value) => {
          setSelectedEventTypes(value.map((v) => v.value));
        },
      } as MultipleSelectFilter<string>,

      {
        id: "date",
        filterName: "Date",
        filterType: FilterType.Select,
        filterValue: selectedDate,
        columnData: {
          choices: DATES_OPTIONS,
          placeholder: "Select date",
        },
        onFilterApply: (value) => {
          if (value) {
            setSelectedDate(value.value);
            setRange(selectedDateToRange(value.value));
          }
        },
      } as SelectFilter<string>,
    ],
    [selectedEventTypes, selectedDate]
  );

  const onResetFilters = useCallback(() => {
    setSelectedDate(defaultRange);
    setRange(selectedDateToRange(defaultRange));
    setSelectedEventTypes([]);
  }, []);

  if (!member) {
    return null;
  }

  return (
    <div className="flex h-full flex-col bg-white">
      <SidebarAvatar
        photoUrl={member.photo512Url}
        realName={member.realName}
        title={member.title}
      />
      <div className="mb-4 flex items-center justify-between px-4">
        <div className="text-sm font-light text-gray-600">
          Showing {totalCount} {pluralize("activity", totalCount)}
        </div>
        <MultipleFiltersDropdown
          filters={filterFields}
          onResetFilters={onResetFilters}
        />
      </div>

      {loading ? (
        <SkeletonEvents className="px-4" />
      ) : !logs.length ? (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="mb-2 text-gray-400">
            <NotebookMinimalistic className="mx-auto h-12 w-12" />
          </div>
          <div className="text-sm text-gray-500">
            {getMemberName(member)} has no activity logs yet
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto">
          {logGroups.map((group) => (
            <div key={group.dateLabel} className="mb-6 last:mb-0">
              <DateSeparator dateLabel={group.dateLabel} />
              <div>
                {group.groupedLogs.map((log, index) => {
                  const isLast = index === group.groupedLogs.length - 1;

                  if ("timeOffLogType" in log) {
                    const type = getType(log.data.request.type.id);

                    if (!type) return null;

                    return (
                      <TimeOffLog
                        key={log.id}
                        createdAt={log.createdAt}
                        type={log.timeOffLogType}
                        request={{
                          ...log.data.request,
                          type,
                        }}
                        member={member}
                        initiator={log.data.initiator ?? undefined}
                        isLast={isLast}
                      />
                    );
                  }

                  if ("balanceLogType" in log) {
                    const type = getType(log.data.type.id);

                    if (!type) return null;

                    return (
                      <BalanceLog
                        key={log.id}
                        createdAt={log.createdAt}
                        type={log.balanceLogType}
                        increase={log.data.increase ?? undefined}
                        absolute={log.data.absolute ?? undefined}
                        requestType={type}
                        member={member}
                        initiator={log.data.initiator ?? undefined}
                        isLast={isLast}
                      />
                    );
                  }

                  if ("slackStatusLogType" in log) {
                    const slackData = log.data as {
                      previousStatus?: string | null;
                      newStatus: string;
                    };

                    return (
                      <SlackStatusLog
                        key={log.id}
                        createdAt={log.createdAt}
                        type={log.slackStatusLogType}
                        previousStatus={slackData.previousStatus ?? undefined}
                        newStatus={slackData.newStatus}
                        member={member}
                        isLast={isLast}
                      />
                    );
                  }

                  return null;
                })}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ActivityLogTab;
