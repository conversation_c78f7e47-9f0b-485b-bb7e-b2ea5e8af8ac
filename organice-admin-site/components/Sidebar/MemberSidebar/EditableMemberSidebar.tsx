import { useApolloClient } from "@apollo/client";
import { FC, useMemo, useState, useEffect, useCallback } from "react";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import { useSessionContext } from "../../../context/SessionContext";
import {
  useEditPositionMutation,
  useMemberSidebarQuery,
} from "../../../graphql/client.generated";
import { flattenEditableMember } from "../../../helpers/flattenMember";
import useSyncChildrenDepartmentInCache from "../../../hooks/useSyncChildrenDepartmentInCache";
import { useToast } from "../../../hooks/useToast";
import { Menu } from "../../DropdownMenu";
import GoToOrgChartButton from "../../GoToOrgChartButton";
import { Tab } from "../../Tab";
import EditableCalendarSidebarContent from "../CalendarSidebar/EditableCalendarSidebarContent";
import Sidebar from "../Sidebar";
import TimeOffSidebar from "../TimeOffSidebar/TimeOffSidebar";

import ActivityLogSidebarContent from "./ActivityLogSidebarContent";
import EditableMemberSidebarContent, {
  MemberSidebarData,
} from "./EditableMemberSidebarContent";

export interface SidebarTab {
  id: "profile" | "timeOffs" | "activityLog";
  isActive?: boolean;
}

const TAB_LABELS = {
  profile: "Profile",
  timeOffs: "Time Offs",
  activityLog: "Activity",
};

interface Props {
  id: string;
  tabs: SidebarTab[];
  mode: "edit" | "read";
  setExpandedIds?: (ids: string[]) => void;
  closeSidebar: () => void;
  removeNode?: () => void;
  showGoToOrgChartBtn?: boolean;
  changeTab: (tabId: SidebarTab["id"]) => void;
}

const EditableMemberSidebar: FC<Props> = ({
  tabs,
  id,
  mode,
  setExpandedIds,
  closeSidebar,
  showGoToOrgChartBtn,
  changeTab,
  removeNode,
}) => {
  const isProfileTabActive = useMemo(() => {
    return tabs.find((tab) => tab.id === "profile")?.isActive;
  }, [tabs]);

  const isActivityLogTabActive = useMemo(() => {
    return tabs.find((tab) => tab.id === "activityLog")?.isActive;
  }, [tabs]);
  const apolloClient = useApolloClient();
  const { cache } = apolloClient;
  const { track } = useMixpanelContext();
  const toast = useToast();
  const { session } = useSessionContext();
  const [editPosition] = useEditPositionMutation();
  const [memberData, setMemberData] = useState<MemberSidebarData | null>(null);
  const syncChildrenDepartmentInCacheFn = useSyncChildrenDepartmentInCache();

  const [editingEvent, setEditingEvent] = useState<string | null>(null);

  const { data, refetch: refetchMember } = useMemberSidebarQuery({
    /**
     * NOTE: refresh even loaded data on sidebar open to make sure
     * that teams, departments and mmbers are always up to date
     */
    fetchPolicy: "cache-and-network",
    variables: {
      memberId: id,
    },
    skip: !isProfileTabActive,
  });

  const onDepartmentOrManagerUpdated = useCallback(async () => {
    await refetchMember();

    if (!memberData?.position?.id) {
      return;
    }
    syncChildrenDepartmentInCacheFn(memberData.position.id);
  }, [
    syncChildrenDepartmentInCacheFn,
    refetchMember,
    memberData?.position?.id,
  ]);

  useEffect(() => {
    if (!data) {
      return;
    }

    if (!isProfileTabActive) {
      setMemberData(null);

      return;
    }

    setMemberData({
      workspaceURL: data.myWorkspace.url,
      member: {
        ...flattenEditableMember(data.member),
        notes: data.member.notes ?? "",
      },
      hideBirthday: data.member.hideBirthday ?? false,
      position: data.member.position
        ? {
            id: data.member.position.id,
            assignable: data.member.position.assignable,
          }
        : null,
      memberFields: data.myWorkspace.memberFields ?? [],
      nonFilledFields: data.member.nonFilledFields ?? [],
      teams: data.teams.teams,
      members: data.members,
    });
  }, [data, isProfileTabActive]);

  const sidebarActions = useMemo(() => {
    const actions = [];

    if (isProfileTabActive && session?.me.isAdmin) {
      if (memberData?.position?.assignable) {
        actions.push({
          label: "Unassign",
          id: "unassign",
          onClick: () => {
            track("Member is unassigned from position");

            editPosition({
              variables: {
                position: {
                  id: memberData.position!.id,
                  unassign: true,
                },
              },
              onCompleted: () => {
                /**
                 * NOTE: users page uses UpdatableMemberFields fragment
                 * that won't be updated by editPosition mutation automatically
                 * so we have manually to update it
                 */

                cache.modify({
                  id: cache.identify({
                    __typename: "Member",
                    id: memberData.member.id,
                  }),
                  fields: {
                    position: () => null,
                  },
                });
              },
            })
              .then(() => {
                toast(
                  "success",
                  "Member successfully unassigned from Position"
                );
              })
              .catch((e) => {
                toast("error", (e as Error).message);
              });
          },
        });
      }

      if (removeNode) {
        actions.push({
          label: "Close position",
          id: "delete",
          type: "danger" as Menu["type"],
          onClick: () => {
            removeNode();
          },
        });
      }
    }

    return actions;
  }, [
    isProfileTabActive,
    memberData,
    editPosition,
    toast,
    track,
    session?.me.isAdmin,
    cache,
    removeNode,
  ]);

  if (editingEvent) {
    return (
      <TimeOffSidebar
        id={editingEvent}
        onClose={() => {
          setEditingEvent(null);
          closeSidebar();
        }}
        onBack={() => setEditingEvent(null)}
      />
    );
  }

  return (
    <Sidebar
      size="medium"
      closeSidebar={closeSidebar}
      actions={
        showGoToOrgChartBtn &&
        !!memberData?.position?.id && (
          <GoToOrgChartButton id={memberData.position.id} />
        )
      }
      title={
        tabs.length > 1 ? (
          <div className="flex gap-2 text-sm" data-test="member-sidebar-tabs">
            {tabs.map((tab) => (
              <Tab
                key={tab.id}
                isActive={() => !!tab.isActive}
                onClick={() => {
                  changeTab(tab.id);
                }}
              >
                {TAB_LABELS[tab.id]}
              </Tab>
            ))}
          </div>
        ) : (
          <div className="text-lg font-semibold">Profile</div>
        )
      }
      menu={sidebarActions}
    >
      {isProfileTabActive && memberData ? (
        <EditableMemberSidebarContent
          mode={mode}
          memberData={memberData}
          setExpandedIds={setExpandedIds}
          onManagerUpdated={onDepartmentOrManagerUpdated}
          onDepartmentUpdated={onDepartmentOrManagerUpdated}
        />
      ) : null}
      {!isProfileTabActive && !isActivityLogTabActive ? (
        <EditableCalendarSidebarContent
          memberId={id}
          openEditTimeOffSidebar={setEditingEvent}
        />
      ) : null}
      {isActivityLogTabActive ? (
        <ActivityLogSidebarContent memberId={id} />
      ) : null}
    </Sidebar>
  );
};

export default EditableMemberSidebar;
