import {
  balanceByEOY,
  getNextAccruals,
  getDaysAndHoursInfo,
} from "@organice/core/domain/time-offs";
import formatDate from "@organice/core/utils/formatDate";
import { FC, useCallback } from "react";
import { InfoCircle, SortHorizontal } from "solar-icon-set";

import {
  AccrualsFrequency,
  YearStart,
} from "../../../graphql/client.generated";
import Notification from "../../Notification";
import Select from "../../Select";
import { TimeOffTypeLabel } from "../../Settings/TimeOffs/TimeOffTypeItem";
import Toggle from "../../Toggle";
import Tooltip from "../../Tooltip";

import DaysHoursInput from "./DaysHoursInput";
import { PolicyTypeInput } from "./types";

const frequencyOptions = [
  {
    title: "Weekly",
    value: AccrualsFrequency.Week,
  },
  {
    title: "Monthly",
    value: AccrualsFrequency.Month,
  },
];

const yearStartOptions = [
  {
    title: "1st of January",
    value: YearStart.Calendar,
  },
  {
    title: "Employee start date",
    value: YearStart.StartDate,
  },
];

const coefficients = {
  [AccrualsFrequency.Month]: 12,
  [AccrualsFrequency.Week]: 52,
};

interface TimeOffPolicyTypeItemProps {
  policyType: PolicyTypeInput;
  updatePolicy: (policy: PolicyTypeInput) => void;
}

const TimeOffPolicyTypeItem: FC<TimeOffPolicyTypeItemProps> = ({
  policyType,
  updatePolicy,
}) => {
  const disabled = !policyType.isDeductible;
  const coefficient = policyType.accuralsFrequency
    ? coefficients[policyType.accuralsFrequency]
    : 1;

  const getNow = (): Date => new Date();
  const registerSelectRef = useCallback((node: HTMLDivElement | null) => {
    node?.setAttribute("data-prevent-sidebar-close", "true");
  }, []);

  const handleAccrualsQuotaChange = useCallback(
    (value: number, coeff = 1) => {
      const updates: Partial<PolicyTypeInput> = {
        accrualsQuota: value / coeff,
      };

      updatePolicy({
        ...policyType,
        ...updates,
      });
    },
    [policyType, updatePolicy]
  );

  return (
    <div>
      <div className="flex items-center gap-2">
        <div className="flex flex-1 items-center gap-2">
          <TimeOffTypeLabel type={policyType} />
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-slate-500">
            Deductible
            <Tooltip
              message="Deductible leaves have a fixed allowance that employees can use. Available leave days can be edited on the policies page."
              className="mb-1 h-4"
            >
              <InfoCircle />
            </Tooltip>
          </div>
          <Toggle
            checked={policyType.isDeductible}
            onChange={() => {
              updatePolicy({
                ...policyType,
                isDeductible: !policyType.isDeductible,
              });
            }}
          />
        </div>
      </div>
      <div className="mt-8 flex flex-col gap-6">
        <div className="flex">
          <div className="w-3/5 pr-10 text-sm">
            <p className="mb-2 font-bold">Accrual period</p>
            <p className="text-gray-500">
              Leave days are accrued by the end of the selected period.
            </p>
          </div>
          <div className="mt-5 w-2/5">
            <Select
              registerSelectRef={registerSelectRef}
              placeholder="Select type"
              allowClear
              value={
                policyType.accuralsFrequency
                  ? frequencyOptions.find(
                      (f) => f.value === policyType.accuralsFrequency
                    )!
                  : null
              }
              className="text-sm"
              options={frequencyOptions}
              onChange={(option) =>
                updatePolicy({
                  ...policyType,
                  accuralsFrequency: option?.value ?? null,
                })
              }
              disabled={disabled}
            />
          </div>
        </div>

        {policyType.accuralsFrequency ? (
          <div className="flex items-center gap-0.5">
            <div className="flex flex-col gap-5 rounded bg-violet-100 p-4 py-3">
              <div className="flex">
                <span className="text-sm">Number of days per year</span>
                <span className="ml-auto rounded bg-white py-0.5 px-2 text-xs first-letter:capitalize">
                  Year
                </span>
              </div>
              <DaysHoursInput
                value={Math.floor(policyType.accrualsQuota * coefficient)}
                onChange={(value) =>
                  handleAccrualsQuotaChange(value, coefficient)
                }
                disabled={disabled}
              />
            </div>
            <div>
              <SortHorizontal size={16} className="!text-gray-500" />
            </div>
            <div className="flex flex-col gap-5 rounded bg-slate-100 p-4 py-3">
              <div className="flex">
                <span className="text-sm">
                  Number of days per {policyType.accuralsFrequency}
                </span>
                <span className="ml-auto rounded bg-white py-0.5 px-2 text-xs first-letter:capitalize">
                  {policyType.accuralsFrequency}
                </span>
              </div>
              <DaysHoursInput
                value={policyType.accrualsQuota}
                onChange={(value) => handleAccrualsQuotaChange(value)}
                disabled={disabled}
              />
            </div>
          </div>
        ) : null}

        {policyType.accrualsQuota && policyType.accuralsFrequency ? (
          <Notification type="info">
            <>
              <p>
                From now until the end of this year, each employee will receive{" "}
                <strong>
                  {getDaysAndHoursInfo(
                    balanceByEOY(
                      policyType.accrualsQuota,
                      policyType.accuralsFrequency,
                      getNow
                    )
                  )}
                </strong>{" "}
                of leave.
              </p>
              <p>
                The next accruals cycle starts on{" "}
                <strong>
                  {formatDate(
                    getNextAccruals(policyType.accuralsFrequency, getNow())
                  )}
                </strong>
                .
              </p>
            </>
          </Notification>
        ) : null}
        <hr className="divide-y divide-slate-300" />

        <div className="flex">
          <div className="w-3/5 pr-10 text-sm">
            <p className="mb-2 font-bold">PTO max capacity</p>
            <p className="text-gray-500">
              The maximum number of leave days an employee can have. Leave 0 if
              there is no limit.
            </p>
          </div>
          <DaysHoursInput
            className="mt-7 w-2/5"
            value={policyType.maxCapacity}
            onChange={(value) => {
              updatePolicy({
                ...policyType,
                maxCapacity: value,
              });
            }}
            disabled={disabled}
          />
        </div>

        <div className="flex">
          <div className="w-3/5 pr-10 text-sm">
            <p className="mb-2 font-bold">On PTO year start quota</p>
            <p className="text-gray-500">
              Employees receive this number of leave when their new PTO year
              starts or when they join the organization
            </p>
          </div>
          <DaysHoursInput
            className="mt-7 w-2/5"
            value={policyType.onStartQuota}
            onChange={(value) => {
              const updates: Partial<PolicyTypeInput> = {
                onStartQuota: value,
              };

              updatePolicy({
                ...policyType,
                ...updates,
              });
            }}
            disabled={disabled}
          />
        </div>

        <div className="flex items-center gap-2">
          <div className="flex-1 text-sm font-bold">
            Rollover unused leave days to the next PTO year
          </div>
          <Toggle
            checked={policyType.rollOverToNextYear}
            onChange={() => {
              updatePolicy({
                ...policyType,
                rollOverToNextYear: !policyType.rollOverToNextYear,
              });
            }}
            disabled={disabled}
          />
        </div>

        <div className="flex">
          <div className="w-3/5 pr-10 text-sm">
            <p className="mb-2 font-bold">PTO year start</p>
            <p className="text-gray-500">
              The PTO year can start on the <strong>1st of January</strong> or
              can be calculated dynamically for every employee{" "}
              <strong>based on their start date</strong>
            </p>
          </div>
          <div className="mt-5 w-2/5">
            <Select
              placeholder="Select type"
              value={
                yearStartOptions.find((y) => y.value === policyType.yearStart)!
              }
              className="text-sm"
              options={yearStartOptions}
              onChange={(option) => {
                if (option) {
                  updatePolicy({
                    ...policyType,
                    yearStart: option.value,
                  });
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimeOffPolicyTypeItem;
