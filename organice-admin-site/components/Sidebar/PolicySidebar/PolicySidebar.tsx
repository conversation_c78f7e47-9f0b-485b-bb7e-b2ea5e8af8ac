import cn from "classnames";
import { FC, useState, useCallback } from "react";

import { useMixpanelContext } from "../../../context/MixpanelContext";
import {
  Color,
  useUpdateTimeOffPolicyMutation,
  useAddTimeOffPolicyMutation,
  YearStart,
  DayOfWeek,
} from "../../../graphql/client.generated";
import { useToast } from "../../../hooks/useToast";
import Button from "../../Button";
import Input from "../../Input";
import MultiDaySelect from "../../Select/MultiDaySelect";
import { TimeOffPolicyType } from "../../Settings/TimeOffs/consts";
import Toggle from "../../Toggle";
import Sidebar from "../Sidebar";

import TimeOffPolicyTypeItem from "./TimeOffPolicyTypeItem";
import { PolicyTypeInput } from "./types";

interface SidebarProps {
  currentPolicy?: {
    id: string;
    title: string;
    isDefault?: boolean;
    workDays: DayOfWeek[];
    includedWeekendDays: DayOfWeek[];
    notifyAccruals: boolean;
    typePolicies: TimeOffPolicyType[];
  };
  defaultPolicyId?: string;
  types: {
    id: string;
    color: Color;
    label: string;
    emoji: string;
  }[];

  closeSidebar: () => void;
}

const PolicySidebar: FC<SidebarProps> = ({
  currentPolicy,
  types,
  defaultPolicyId,
  closeSidebar,
}) => {
  const { track } = useMixpanelContext();
  const toast = useToast();
  const [addPolicy] = useAddTimeOffPolicyMutation();
  const [updatePolicy] = useUpdateTimeOffPolicyMutation();

  const [title, setTitle] = useState<string>(
    currentPolicy
      ? currentPolicy.title
      : defaultPolicyId
      ? ""
      : "Default policy"
  );
  const [isDefault, setIsDefault] = useState<boolean>(
    currentPolicy ? !!currentPolicy.isDefault : !defaultPolicyId
  );

  const [workDays, setWorkDays] = useState<DayOfWeek[]>(
    currentPolicy
      ? currentPolicy.workDays
      : [
          DayOfWeek.Monday,
          DayOfWeek.Tuesday,
          DayOfWeek.Wednesday,
          DayOfWeek.Thursday,
          DayOfWeek.Friday,
        ]
  );

  const [includedWeekendDays, setIncludedWeekendDays] = useState<DayOfWeek[]>(
    currentPolicy ? currentPolicy.includedWeekendDays : []
  );

  const [notifyAccruals, setNotifyAccruals] = useState<boolean>(
    currentPolicy ? !!currentPolicy.notifyAccruals : true
  );

  const [policyTypes, setPolicyTypes] = useState<PolicyTypeInput[]>(
    types.map((type) => {
      const typePolicy = currentPolicy?.typePolicies.find(
        (p) => p.type.id === type.id
      );

      const isDeductible = !!typePolicy;

      return {
        ...type,

        isDeductible,
        onStartQuota: typePolicy?.onStartQuota ?? 0,
        rollOverToNextYear: !!typePolicy?.rollOverToNextYear,
        yearStart: typePolicy?.yearStart ?? YearStart.Calendar,
        maxCapacity: typePolicy?.maxCapacity ?? 0,
        accrualsQuota: typePolicy?.accrualsQuota ?? 0,
        accuralsFrequency: typePolicy?.accuralsFrequency ?? null,
      };
    })
  );

  const [selectedTab, setSelectedTab] = useState<string>("default");
  const selectedTabData =
    policyTypes.find((type) => type.id === selectedTab) ?? null;

  const defaultTab = {
    id: "default",
    label: "General Settings",
    emoji: "",
    isDeductible: true,
  };
  const navTabs: Pick<
    PolicyTypeInput,
    "id" | "label" | "emoji" | "isDeductible"
  >[] = [defaultTab, ...policyTypes];

  const updatePolicyType = useCallback((policyType: PolicyTypeInput): void => {
    setPolicyTypes((prev) =>
      prev.map((p) => {
        if (p.id === policyType.id) {
          return policyType;
        }

        return p;
      })
    );
  }, []);

  const isFormValid =
    workDays.length > 0 &&
    !!title &&
    (defaultPolicyId === currentPolicy?.id
      ? isDefault
      : isDefault || defaultPolicyId) &&
    !policyTypes.find((p) => {
      return (
        p.isDeductible &&
        ((p.accrualsQuota && !p.accuralsFrequency) ||
          (!p.accrualsQuota && p.accuralsFrequency))
      );
    });

  const submitHandler = (): void => {
    if (!isFormValid) {
      return;
    }

    if (currentPolicy) {
      track("Time off policy edit");
      void updatePolicy({
        variables: {
          id: currentPolicy.id,
          policy: {
            title,
            isDefault,
            includedWeekendDays,
            workDays,
            notifyAccruals,
            typePolicies: policyTypes
              .filter((p) => p.isDeductible)
              .map((p) => ({
                typeId: p.id,
                onStartQuota: p.onStartQuota,
                rollOverToNextYear: p.rollOverToNextYear,
                yearStart: p.yearStart,
                accrualsQuota: p.accrualsQuota,
                accuralsFrequency: p.accuralsFrequency,
                maxCapacity: p.maxCapacity,
              })),
          },
        },
        onCompleted() {
          toast("success", `Leave policy ${title} has been updated`);
          closeSidebar();
        },
        refetchQueries: ["TimeOffsTypesPoliciesSettingsPage"],
      });
    } else {
      track("Time off type create");
      void addPolicy({
        variables: {
          policy: {
            title,
            isDefault,
            includedWeekendDays,
            workDays,
            notifyAccruals,
            typePolicies: policyTypes
              .filter((p) => p.isDeductible)
              .map((p) => ({
                typeId: p.id,
                onStartQuota: p.onStartQuota,
                rollOverToNextYear: p.rollOverToNextYear,
                yearStart: p.yearStart,
                accrualsQuota: p.accrualsQuota,
                accuralsFrequency: p.accuralsFrequency,
                maxCapacity: p.maxCapacity,
              })),
          },
        },
        refetchQueries: ["TimeOffsTypesPoliciesSettingsPage"],
        onCompleted() {
          toast("success", `Leave policy ${title} has been created`);
          closeSidebar();
        },
      });
    }
  };

  return (
    <Sidebar
      menu={[]}
      headerClassName="border-b border-b-gray-300"
      title={
        <Input
          data-test="policy-title-input"
          maxLength={200}
          autoFocus
          placeholder="Enter policy title"
          value={title}
          onChange={setTitle}
          wrapperClassnames="w-full mr-12"
        />
      }
      footer={
        <div className="flex justify-end gap-x-3">
          <Button color="secondary" variant="outline" onClick={closeSidebar}>
            Cancel
          </Button>
          <Button
            dataTest="save-policy-button"
            disabled={!isFormValid}
            onClick={submitHandler}
          >
            Save Changes
          </Button>
        </div>
      }
      size="large"
      closeSidebar={closeSidebar}
    >
      <div className="grid h-full grid-cols-[300px_auto]">
        <div className="border-r border-gray-300 p-4" data-test="block-buttons">
          {navTabs.map((tab) => (
            <button
              key={tab.id}
              type="button"
              className={cn(
                "min-h-12 flex w-full items-center gap-[10px] rounded-md p-3 text-left transition-colors hover:bg-slate-100",
                { "bg-slate-100": selectedTab === tab.id }
              )}
              onClick={() => setSelectedTab(tab.id)}
            >
              <span className="order-2">{tab.label}</span>
              {tab.id !== defaultTab.id ? (
                <>
                  <span
                    className={cn(
                      "order-1 max-h-[10px] min-h-[10px] min-w-[10px] max-w-[10px] rounded-full bg-slate-400",
                      tab.isDeductible && "bg-emerald-500"
                    )}
                  />
                  <span className="order-3 ml-auto">{tab.emoji}</span>
                </>
              ) : null}
            </button>
          ))}
        </div>

        <div className="relative flex flex-col gap-6 p-8">
          {!selectedTabData ? (
            <>
              <div className="-mx-4 -mt-4 flex gap-2 rounded-md bg-slate-100 px-4 py-3">
                <div className="flex-1 text-sm">
                  <p className="mb-2 font-bold">Default Policy</p>
                  <p className="text-slate-500">
                    Any employee without a custom policy will extend this policy
                  </p>
                </div>
                <Toggle
                  checked={isDefault}
                  onChange={() => {
                    setIsDefault(!isDefault);
                  }}
                />
              </div>

              <div className="flex">
                <div className="flex-1 text-sm">
                  <p className="mb-2 font-bold">Working days</p>
                  <p className="text-slate-500">Select regular working days</p>
                  <MultiDaySelect
                    data-test="select-work-days"
                    value={workDays}
                    disabledOptions={includedWeekendDays}
                    onChange={setWorkDays}
                    isMulti
                  />
                </div>
              </div>

              <div className="flex">
                <div className="flex-1 text-sm">
                  <p className="mb-2 font-bold">
                    Include weekends into counted time off
                  </p>
                  <p className="text-slate-500">
                    Selected weekend days will be included in the calculated
                    time off duration
                  </p>
                  <MultiDaySelect
                    data-test="select-included-weekend-days"
                    value={includedWeekendDays}
                    disabledOptions={workDays}
                    onChange={setIncludedWeekendDays}
                    isMulti
                  />
                </div>
              </div>

              <div className="flex">
                <div className="flex-1 text-sm">
                  <p className="mb-2 font-bold">Notify about new accruals</p>
                  <p className="text-slate-500">
                    Enable notification to be sent to your employees when they
                    receive new accruals.
                  </p>
                </div>
                <Toggle
                  checked={notifyAccruals}
                  onChange={() => {
                    setNotifyAccruals(!notifyAccruals);
                  }}
                />
              </div>
            </>
          ) : (
            <TimeOffPolicyTypeItem
              policyType={selectedTabData}
              updatePolicy={updatePolicyType}
            />
          )}
        </div>
      </div>
    </Sidebar>
  );
};

export default PolicySidebar;
