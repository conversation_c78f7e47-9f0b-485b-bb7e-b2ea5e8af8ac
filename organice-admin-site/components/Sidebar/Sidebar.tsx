import cn from "classnames";
import { motion } from "framer-motion";
import { FC, ReactNode, useCallback, useEffect, useMemo } from "react";
import { useIntercom } from "react-use-intercom";

import { useOnClickOutside } from "../../hooks/useOnClickOutside";
import { CloseButton } from "../Button";
import DropdownMenu, { Menu } from "../DropdownMenu";
import TreeDotsButton from "../TreeDotsButton";

interface Props {
  children: ReactNode;
  closeSidebar: () => void;
  menu: Menu[];
  headerClassName?: string | Record<string, boolean>;
  footerClassName?: string;
  actions?: JSX.Element | boolean;
  title?: string | JSX.Element | null;
  size?: "small" | "medium" | "large";
  footer?: JSX.Element | boolean;
  hasIntercom?: boolean;
}

// Note: we don't know if component is wrapped into Intercom provider
// that is why we have to do this trick to avoid useIntercom error
const HideIntercom: FC = () => {
  const { shutdown, boot } = useIntercom();

  useEffect(() => {
    shutdown();

    return () => boot();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

const Sidebar: FC<Props> = ({
  closeSidebar,
  menu,
  children,
  headerClassName,
  footerClassName,
  actions,
  title,
  footer,
  size = "small",
  hasIntercom = true,
}) => {
  const sidebarRef = useOnClickOutside(() => {
    closeSidebar();
  }, "[data-prevent-sidebar-close]");
  const registerDropdownRef = useCallback((node: HTMLDivElement | null) => {
    node?.setAttribute("data-prevent-sidebar-close", "true");
  }, []);

  const showHeader = Boolean(title ?? actions ?? menu.length);

  useEffect(() => {
    const onCloseListener = (e: KeyboardEvent): void => {
      if (e.code === "Escape") {
        e.preventDefault();
        closeSidebar();
      }
    };

    document.addEventListener("keydown", onCloseListener);

    return () => {
      document.removeEventListener("keydown", onCloseListener);
    };
  }, [closeSidebar]);

  const animationDuration = useMemo(() => {
    if (size === "small") {
      return 0.3;
    }

    if (size === "medium") {
      return 0.4;
    }

    return 0.5;
  }, [size]);

  return (
    <>
      {hasIntercom ? <HideIntercom /> : null}
      {size === "large" ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: { delay: 0.1, duration: animationDuration },
          }}
          exit={{
            opacity: 0,
            transition: { delay: 0.1, duration: animationDuration },
          }}
        >
          <div
            role="button"
            tabIndex={0}
            aria-label="Close sidebar"
            onClick={closeSidebar}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                closeSidebar();
              }
            }}
            className="fixed top-0 left-0 z-10 h-full w-full bg-black opacity-75"
          />
        </motion.div>
      ) : null}
      <motion.aside
        data-test="sidebar-aside"
        ref={sidebarRef}
        initial={{ x: "100%" }}
        animate={{
          x: 0,
          transition: { delay: 0.1, duration: animationDuration },
        }}
        exit={{
          x: "100%",
          transition: { delay: 0.1, duration: animationDuration },
        }}
        className={cn(
          "fixed right-0 top-1 z-[9999] h-full overflow-hidden rounded-tl-md border border-r-0 border-main-300 bg-white shadow-primary",
          {
            "w-[300px]": size === "small",
            "w-[400px]": size === "medium",
            "w-[980px]": size === "large",
          }
        )}
      >
        <div className="flex h-full flex-col">
          {showHeader && (
            <div
              className={cn(
                "item-center sticky top-0 z-10 flex justify-between border-b border-r  border-main-300 bg-white px-4 py-4",
                headerClassName
              )}
            >
              {title && title}
              <div className="ml-auto flex items-center gap-0.5">
                {actions && actions}
                {menu.length > 0 ? (
                  <DropdownMenu
                    menu={menu}
                    data-test="sidebar-header-actions"
                    registerDropdownRef={registerDropdownRef}
                  >
                    <TreeDotsButton
                      aria-label="sidebar-dropdown-menu"
                      itemClassName="bg-gray-500"
                    />
                  </DropdownMenu>
                ) : null}
                <CloseButton onClick={closeSidebar} />
              </div>
            </div>
          )}

          <div
            data-test="sidebar-body"
            className="relative flex-1 overflow-y-auto border-r
          border-r-main-300 scrollbar:w-[12px] scrollbar-track:bg-gray-100 scrollbar-thumb:border-[4px]"
          >
            {!showHeader && (
              <CloseButton
                onClick={closeSidebar}
                className="absolute right-6 top-[26px] z-[11]"
              />
            )}

            {children}
          </div>
          {footer ? (
            <div
              className={cn(
                "sticky bottom-0 border-t  border-t-main-300   bg-main-100 px-6 py-5",
                footerClassName
              )}
            >
              {footer}
            </div>
          ) : null}
        </div>
      </motion.aside>
    </>
  );
};

export default Sidebar;
