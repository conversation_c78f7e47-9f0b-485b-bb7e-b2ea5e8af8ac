import cn from "classnames";
import { format, isSameDay } from "date-fns";
import React from "react";

import {
  TimeOffRequestType,
  TimeOffRequestStatus,
} from "../../../../../graphql/client.generated";
import EventIcon from "../../../../EventIcon";

const UPCOMING_EVENTS_DATE_FORMAT = "MMM d";

interface UpcomingTimeOffProps {
  index: number;
  event: {
    endDate: Date;
    startDate: Date;
    type: TimeOffRequestType;
    status: TimeOffRequestStatus;
    openEditTimeOffSidebar: () => void;
    rejectReason?: string | null;
  };
}

const TimeOffEvent: React.FC<UpcomingTimeOffProps> = ({ index, event }) => {
  const interval = isSameDay(event.startDate, event.endDate);
  const { status, rejectReason } = event;
  const isRejected = status === TimeOffRequestStatus.Rejected;
  const statusLabels = {
    [TimeOffRequestStatus.Approved]: "Approved",
    [TimeOffRequestStatus.Pending]: "Pending",
    [TimeOffRequestStatus.Rejected]: "Rejected",
  };

  const statusClassName = {
    [TimeOffRequestStatus.Approved]: "",
    [TimeOffRequestStatus.Pending]: "bg-stripped",
    [TimeOffRequestStatus.Rejected]: "bg-red-100",
  };

  return (
    <div
      className={cn("flex flex-col", {
        "border-main-300 before:mx-auto before:my-2 before:w-[96%] before:border-t":
          index,
      })}
    >
      <button
        className={cn(
          "group -mx-4 flex min-h-[46px] flex-col gap-2 py-2 px-4",
          {
            "hover:bg-slate-100": !isRejected,
          }
        )}
        onClick={event.openEditTimeOffSidebar}
        type="button"
        disabled={isRejected}
      >
        <div className="flex w-full items-center justify-between">
          <div
            className={cn({
              "group-hover:font-medium group-hover:text-violet-600":
                !isRejected,
            })}
          >
            {event.type.label}
          </div>
          <div className="flex items-center gap-2">
            <div className="text-main-400 line-clamp-1">
              {interval
                ? format(event.startDate, UPCOMING_EVENTS_DATE_FORMAT)
                : `${format(
                    event.startDate,
                    UPCOMING_EVENTS_DATE_FORMAT
                  )} - ${format(event.endDate, UPCOMING_EVENTS_DATE_FORMAT)}`}
            </div>
            <EventIcon
              className={statusClassName[status]}
              label={statusLabels[status]}
              emoji={event.type.emoji}
              color={event.type.color}
            />
          </div>
        </div>
        {isRejected && rejectReason && (
          <div className="text-left">
            <span className="font-semibold">Reason:</span> {rejectReason}
          </div>
        )}
      </button>
    </div>
  );
};

export default TimeOffEvent;
