import { FC } from "react";

import {
  CalendarSidebarQuery,
  TimeOffRequestType,
  CelebrationEventType,
  HolidayEventType,
} from "../../../../../graphql/client.generated";
import { getMemberName } from "../../utils";

import CelebrationEvent from "./CelebrationEvent";
import HolidayEvent from "./HolidayEvent";
import TimeOffEvent from "./TimeOffEvent";

interface Props {
  member?: CalendarSidebarQuery["member"];
  events: CalendarSidebarQuery["memberEvents"];
  timeOffTypes: TimeOffRequestType[];
  celebrationTypes: CelebrationEventType[];
  holidayTypes: HolidayEventType[];
  openEditTimeOffSidebar: (id: string) => void;
}

const Events: FC<Props> = ({
  member,
  events,
  timeOffTypes,
  celebrationTypes,
  holidayTypes,
  openEditTimeOffSidebar,
}) => {
  return (
    <div className="text-sm">
      {member && !events.length ? (
        <>{getMemberName(member)} has not taken any time off yet</>
      ) : null}
      {events.map((event, index) => {
        if ("isOfficial" in event) {
          const type = holidayTypes.find((t) => t.id === event.type.id);

          if (!type) {
            return null;
          }

          return (
            <HolidayEvent
              key={`${event.id}-${type.label}`}
              index={index}
              event={{
                ...event,
                type,
              }}
            />
          );
        }

        if ("status" in event) {
          const type = timeOffTypes.find((t) => t.id === event.type.id);

          if (!type) {
            return null;
          }

          return (
            <TimeOffEvent
              key={`${event.id}-${type.label}`}
              index={index}
              event={{
                ...event,
                type,
                openEditTimeOffSidebar: () => openEditTimeOffSidebar(event.id),
              }}
            />
          );
        }

        const type = celebrationTypes.find((t) => t.id === event.type.id);

        if (!type) {
          return null;
        }

        return (
          <CelebrationEvent
            key={`${event.id}-${type.label}`}
            index={index}
            event={{
              ...event,
              type,
            }}
          />
        );
      })}
    </div>
  );
};

export default Events;
