import cn from "classnames";
import pluralize from "pluralize";
import { FC } from "react";

import {
  CalendarSidebarQuery,
  TimeOffRequestType,
  TimeOffEvent,
  useUpdateTimeOffBalanceMutation,
} from "../../../../../graphql/client.generated";
import { useToast } from "../../../../../hooks/useToast";
import { getMemberName } from "../../utils";

import PolicyItem from "./PolicyItem";

interface Props {
  events: TimeOffEvent[];
  member?: CalendarSidebarQuery["member"];
  timeOffPolicies: CalendarSidebarQuery["timeOffPolicies"];
  types: TimeOffRequestType[];
}

const BalanceTab: FC<Props> = ({ member, timeOffPolicies, types, events }) => {
  const getType = (typeId: string): TimeOffRequestType | null =>
    types.find((t) => t.id === typeId) ?? null;

  const toast = useToast();
  const memberPolicy =
    member?.timeOffTypePolicy ??
    timeOffPolicies.find((p) => p.isDefault) ??
    null;

  const [updateTimeOffBalance] = useUpdateTimeOffBalanceMutation();

  const durationPerTypeId = events.reduce<Record<string, number>>(
    (acc, event) => {
      acc[event.type.id] =
        (acc[event.type.id] > 0 ? acc[event.type.id] : 0) +
        (event.duration ?? 0);

      return acc;
    },
    {}
  );

  const typeIdsWOPolicy = Object.keys(durationPerTypeId).filter((typeId) =>
    memberPolicy
      ? !memberPolicy.typePolicies.some((tp) => tp.type.id === typeId)
      : true
  );

  return (
    <div className="flex flex-col text-sm">
      {member && !memberPolicy && !typeIdsWOPolicy.length ? (
        <>{getMemberName(member)} has not taken any time off yet</>
      ) : null}
      {memberPolicy?.typePolicies.length ? (
        <div className="rounded border border-main-300">
          <div className="rounded-t bg-gray-100 p-3 text-base font-bold">
            {memberPolicy.title}
          </div>
          <div className="px-3">
            {memberPolicy.typePolicies.map((typePolicy, index) => {
              const stats = member?.timeOffs.find(
                (t) => t.typeId === typePolicy.type.id
              );

              return (
                <PolicyItem
                  key={typePolicy.type.id}
                  index={index}
                  policy={memberPolicy}
                  taken={
                    typePolicy.type.id in durationPerTypeId
                      ? durationPerTypeId[typePolicy.type.id]
                      : 0
                  }
                  available={stats?.balance ?? 0}
                  getType={getType}
                  typePolicy={typePolicy}
                  nextResetAt={stats?.nextResetAt ?? null}
                  onChange={(value) => {
                    if (member) {
                      void updateTimeOffBalance({
                        variables: {
                          balance: {
                            memberId: member.id,
                            balance: value,
                            typeId: typePolicy.type.id,
                          },
                        },
                      }).then(() => {
                        toast("success", "The balance has been updated");
                      });
                    }
                  }}
                />
              );
            })}
          </div>
        </div>
      ) : null}
      {typeIdsWOPolicy.map((typeId, index) => {
        const type = getType(typeId);

        if (!type) {
          return null;
        }
        const { emoji, label } = type;

        return (
          <div
            className={cn("flex flex-col", {
              "border-main-300 before:mx-auto before:my-2 before:w-[96%] before:border-t":
                index,
            })}
          >
            <div className="flex h-[46px] items-center justify-between">
              <div className="font-bold">
                {emoji} {label}
              </div>
              <div>
                <span className="text-gray-500">Taken </span>{" "}
                <strong>{durationPerTypeId[typeId]}</strong>{" "}
                <span className="text-gray-500">
                  {pluralize("day", durationPerTypeId[typeId])}
                </span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default BalanceTab;
