import cn from "classnames";
import pluralize from "pluralize";
import { FC, useState } from "react";

import {
  DayOfWeek,
  TimeOffRequestType,
  CalendarSidebarQuery,
} from "../../../../../graphql/client.generated";
import Input from "../../../../Input";
import TypePolicyShortInfo from "../../../../TypePolicyShortInfo";

interface Props {
  index: number;
  getType: (id: string) => TimeOffRequestType | null;
  taken?: number;
  available?: number;
  typePolicy: CalendarSidebarQuery["timeOffPolicies"][0]["typePolicies"][0];
  onChange: (value: number) => void;
  policy: {
    workDays: DayOfWeek[];
    includedWeekendDays: DayOfWeek[];
  };
  nextResetAt: Date | null;
}

const PolicyItem: FC<Props> = ({
  getType,
  typePolicy,
  index,
  taken,
  available,
  policy,
  onChange,
  nextResetAt,
}) => {
  const type = getType(typePolicy.type.id);
  const [balance, setBalance] = useState(
    available ? available.toString() : "0"
  );

  if (!type) {
    return null;
  }

  const { emoji, label } = type;

  return (
    <div
      className={cn("py-3", {
        "border-t border-main-300": index,
      })}
    >
      <div className="mb-3 font-bold">{`${emoji} ${label}`} </div>
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center gap-1">
          <span className="text-gray-500">Available</span>
          <Input
            placeholder=""
            wrapperClassnames="w-[80px]"
            value={balance}
            onChange={(newBalance) => {
              setBalance(newBalance);
            }}
            onBlur={(e) => {
              const days = e.target.value;
              const daysNumber = days ? parseFloat(days) : 0;

              if (!isNaN(daysNumber) && available !== daysNumber) {
                onChange(daysNumber);
                setBalance(daysNumber.toString());
              }
            }}
          />
          <span className="text-gray-500">{pluralize("day", available)}</span>
        </div>
        <div>
          <span className="text-gray-500">Taken</span>{" "}
          <strong>{taken ?? "0"}</strong>{" "}
          <span className="text-gray-500">{pluralize("day", taken)}</span>
        </div>
      </div>
      <TypePolicyShortInfo
        typePolicy={{
          ...typePolicy,
          accuralsFrequency: typePolicy.accuralsFrequency ?? null,
        }}
        policy={{
          workDays: policy.workDays,
          includedWeekendDays: policy.includedWeekendDays,
        }}
        nextResetAt={nextResetAt}
      />
    </div>
  );
};

export default PolicyItem;
