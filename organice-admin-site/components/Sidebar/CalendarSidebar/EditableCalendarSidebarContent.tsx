import { startOfYear, endOfYear, subYears } from "date-fns";
import { FC, useState } from "react";

import {
  useCalendarSidebarQuery,
  TimeOffEvent,
} from "../../../graphql/client.generated";
import SkeletonEvents from "../../SkeletonEvents";
import { Tab } from "../../Tab";
import SidebarAvatar from "../SidebarAvatar";

import BalanceTab from "./Tabs/Balance";
import EventsTab from "./Tabs/Events";

export enum Tabs {
  TimeOff = "TimeOff",
  Balance = "Balance",
  Logs = "Logs",
}

const tabs = [
  {
    id: Tabs.TimeOff,
    label: "Time Offs",
  },
  {
    id: Tabs.Balance,
    label: "Balance",
  },
];

interface Props {
  memberId: string;
  tab?: Tabs;
  openEditTimeOffSidebar: (id: string) => void;
}

const EditableCalendarSidebarContent: FC<Props> = ({
  memberId: id,
  tab,
  openEditTimeOffSidebar,
}) => {
  const [activeTab, setActiveTab] = useState<Tabs>(tab ?? Tabs.TimeOff);
  const now = new Date();

  const { data, loading } = useCalendarSidebarQuery({
    variables: {
      memberId: id,
      from: startOfYear(subYears(now, 1)),
      to: endOfYear(now),
    },
  });

  const events = data?.memberEvents ?? [];
  const member = data?.member;
  const timeOffs = (data?.memberEvents ?? [])
    .filter((e): e is TimeOffEvent => "status" in e)
    .filter((e) => e.status === "APPROVED");

  return (
    <>
      <SidebarAvatar
        photoUrl={data?.member.photo512Url}
        realName={member?.realName}
        title={member?.title}
      />
      <div className="w-full border-b border-r-gray-300 px-4">
        <div className="flex gap-7">
          {tabs.map((t) => (
            <Tab
              key={t.id as string}
              isActive={() => activeTab === t.id}
              onClick={() => setActiveTab(t.id)}
              tabStyle="underline"
            >
              {t.label}
            </Tab>
          ))}
        </div>
      </div>
      <div className="p-4">
        {loading ? (
          <SkeletonEvents />
        ) : (
          <>
            {activeTab === Tabs.TimeOff ? (
              <EventsTab
                member={member}
                events={events}
                timeOffTypes={data?.timeOffTypes ?? []}
                celebrationTypes={data?.celebrationTypes ?? []}
                holidayTypes={data?.holidayTypes ?? []}
                openEditTimeOffSidebar={openEditTimeOffSidebar}
              />
            ) : null}

            {activeTab === Tabs.Balance ? (
              <BalanceTab
                member={member}
                events={timeOffs}
                timeOffPolicies={data?.timeOffPolicies ?? []}
                types={data?.timeOffTypes ?? []}
              />
            ) : null}
          </>
        )}
      </div>
    </>
  );
};

export default EditableCalendarSidebarContent;
