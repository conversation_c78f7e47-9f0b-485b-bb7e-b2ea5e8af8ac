import { format, isToday, isYesterday } from "date-fns";
import { groupBy } from "lodash";

import { CalendarSidebarQuery } from "../../../graphql/client.generated";

export interface Member {
  id: string;
  realName?: string | null;
  displayName?: string | null;
}

export function getMemberName(member: Member): string {
  return member.realName
    ? member.realName
    : member.displayName
    ? `@${member.displayName}`
    : "Unknown";
}

export type LogItem = CalendarSidebarQuery["logs"][0];

export interface LogGroup {
  date: Date;
  dateLabel: string;
  groupedLogs: LogItem[];
}

export function groupLogsByDate(logs: LogItem[]): LogGroup[] {
  const sortedLogs = [...logs].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const groupedByDate = groupBy(sortedLogs, (log) =>
    format(new Date(log.createdAt), "yyyy-MM-dd")
  );

  const logGroups: LogGroup[] = Object.entries(groupedByDate).map(
    ([, groupedLogs]) => {
      const date = new Date(groupedLogs[0].createdAt);

      const dateLabel = getDateLabel(date);

      return {
        date,
        dateLabel,
        groupedLogs,
      };
    }
  );

  return logGroups.sort((a, b) => b.date.getTime() - a.date.getTime());
}

export function getDateLabel(date: Date): string {
  if (isToday(date)) {
    return "Today";
  }

  if (isYesterday(date)) {
    return "Yesterday";
  }

  return format(date, "d MMMM, yyyy");
}

export function getTotalLogsCount(logGroups: LogGroup[]): number {
  return logGroups.reduce(
    (total, group) => total + group.groupedLogs.length,
    0
  );
}
