import React from "react";

import Avatar from "../Avatar";

interface Props {
  photoUrl?: string | null;
  realName?: string | null;
  title?: string | null;
}

const SidebarAvatar: React.FC<Props> = ({ photoUrl, realName, title }) => {
  return (
    <div className="flex items-center gap-4 border-r-gray-300 p-4">
      <Avatar size="l" photoUrl={photoUrl} />
      <div>
        <h2 className="text-lg font-bold">{realName}</h2>
        <p className="text-sm text-gray-500">{title}</p>
      </div>
    </div>
  );
};

export default SidebarAvatar;
