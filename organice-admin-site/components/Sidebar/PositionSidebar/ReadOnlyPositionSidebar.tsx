import cn from "classnames";
import React, { useMemo } from "react";

import { getDepartmentColors } from "../../../helpers/colors";
import { EditableFlatPosition } from "../../../hooks/useFlattenPosition";
import useGetPositionFields, {
  PositionField,
} from "../../../hooks/useGetPositionFields";
import FieldsGroup from "../Fields/FieldsGroup";
import { FieldsGroup as FieldsGroupType } from "../Fields/types";
import Sidebar from "../Sidebar";

interface Props {
  position: EditableFlatPosition;
  closeSidebar: () => void;
}

const ReadOnlyPositionSidebar: React.FC<Props> = ({
  position,
  closeSidebar,
}) => {
  const departmentColor = useMemo(
    () =>
      position.department?.value.departmentColor
        ? getDepartmentColors(position.department.value.departmentColor)
        : undefined,
    [position]
  );

  const getPositionFields = useGetPositionFields({
    positionFields: [
      {
        id: "photo",
        type: "photo",
        label: "",
      },
      {
        id: "jobTitle",
        type: "jobTitle",
        label: "Job title",
      },
      {
        id: "manager",
        type: "manager",
        label: "Manager",
      },
      {
        id: "teams",
        type: "teams",
        label: "Teams",
      },
      {
        id: "department",
        type: "department",
        label: "Department",
      },
      {
        id: "hiringManager",
        type: "user",
        label: "Hiring manager",
      },
      {
        id: "jobDescriptionLink",
        type: "link",
        label: "Link to a job post",
      },
    ],
    members: [],
    teams: [],
    possibleMembers: [],
    possiblePositions: [],
    possibleDepartments: [],
    referralSettings: {
      isEnable: false,
      channel: null,
      message: null,
      nextDigestAt: null,
    },

    currentMember: {
      isAdmin: false,
    },

    onCreateTeam: () => {},
  });

  const fields = getPositionFields({
    isReadonly: true,
  });

  const headerGroup: FieldsGroupType<PositionField[]> = {
    key: "header",
    label: "",
    color: departmentColor,
    fields: fields.filter((f) => ["photo", "jobTitle"].includes(f.key)),
  };

  const orgChartGroup: FieldsGroupType<PositionField[]> = {
    key: "org",
    label: "Org chart information",
    fields: fields.filter((f) =>
      ["manager", "teams", "department"].includes(f.key)
    ),
  };

  const hiringGroup: FieldsGroupType<PositionField[]> = {
    key: "hiring",
    label: "Hiring",
    fields: fields.filter((f) =>
      ["hiringManager", "jobDescriptionLink"].includes(f.key)
    ),
  };

  const groups = [headerGroup, orgChartGroup, hiringGroup];

  return (
    <Sidebar closeSidebar={closeSidebar} menu={[]} hasIntercom={false}>
      <div className={cn("h-6", departmentColor)} />
      <div className="flex flex-col divide-y divide-gray-300 pb-6 children:px-4">
        {groups.map((group, index) => {
          return (
            <FieldsGroup
              key={group.key}
              data={position}
              label={group.label}
              className={cn(group.color, {
                "pt-6": index,
              })}
              fields={group.fields}
              setFieldData={() => {}}
            />
          );
        })}
      </div>
    </Sidebar>
  );
};

export default ReadOnlyPositionSidebar;
