import cn from "classnames";
import { FC } from "react";

interface DateSeparatorProps {
  dateLabel: string;
  className?: string;
}

const DateSeparator: FC<DateSeparatorProps> = ({ dateLabel, className }) => {
  return (
    <div className={cn("sticky top-0 z-10 mb-2 bg-white", className)}>
      <div className="relative flex items-center">
        <div className="flex-grow border-t border-gray-200" />
        <span className="flex-shrink-0 rounded-full border border-gray-200 bg-gray-100 px-3 py-1 text-sm">
          {dateLabel}
        </span>
        <div className="flex-grow border-t border-gray-200" />
      </div>
    </div>
  );
};

export default DateSeparator;
