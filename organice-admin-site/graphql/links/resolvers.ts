import assert from "assert";

import {
  Department as DomainDepartment,
  Position as DomainPosition,
  RootNode as DomainRootNode,
  WorkspaceRepository,
  ReadonlyDeep,
  Workspace,
  Link as DomainLink,
  Team as DomainTeam,
  Member as DomainMember,
} from "@organice/core/domain";
import {
  getNode,
  getParentDepartment,
  getNodes,
} from "@organice/core/domain/org-chart";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import { uniqBy, sortBy } from "lodash";

import { getLogger } from "../../helpers/getLogger";
import {
  collectMemberFieldPolicy,
  getDataField,
  collectMemberFields,
  isSimplifiedUserField,
  isSimplifiedManagerField,
  UserField,
  ManagerField,
} from "../app/resolvers/_common";

import {
  Department,
  Member,
  Position,
  TextField,
  RootNode,
  Team,
  type Resolvers,
  type QueryResolvers,
  SubordinateResolvers,
  OrgTreeNodeResolvers,
  IMemberFieldResolvers,
} from "./server.generated";

const IMemberField: IMemberFieldResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if ("position" in obj) {
      return "ManagerField";
    }

    if ("urlurl72" in obj || "url512" in obj) {
      return "PhotoField";
    }

    if ("teams" in obj) {
      return "TeamsField";
    }

    if ("date" in obj) {
      return "DateField";
    }

    if ("department" in obj) {
      return "DepartmentField";
    }

    if ("link" in obj) {
      return "LinkField";
    }

    if ("member" in obj) {
      return "UserField";
    }

    if ("text" in obj) {
      return "TextField";
    }

    throw new Error("Unknown field type");
  },
};

const OrgTreeNode: OrgTreeNodeResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === "position") {
      return "Position";
    }

    if (obj.type === "department") {
      return "Department";
    }

    return "RootNode";
  },
};

const Query = {
  async member(_parent, { id, linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { members } = workspace;
    const { visibleFields, visibleNodes } = link;

    const member = members.find((m) => m.id === id);

    assert(member, `Expected member with id "${id}" to exist`);

    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.memberId === member.id
    );

    assert(memberPosition, `Expected member with id "${id}" having a position`);

    assert(
      visibleNodes.includes(memberPosition.id),
      `Expected member with id "${id}" to exist`
    );

    const formatedMember = formatMember({
      workspace,
      member,
      visibleFields,
      visibleNodes,
    });

    return {
      ...formatedMember,
      hideBirthday: member.hideBirthday,
      position: formatPosition(
        workspace,
        memberPosition,
        visibleFields,
        visibleNodes
      ),
    };
  },

  async link(_parent, { id: linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);

    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const dataField = getDataField(workspace, link.chartLayout.dataField);

    return {
      id: link.id,
      title: link.title,
      visibleViews: link.visibleViews,
      expandedNodes: link.expandedNodes,
      memberFields: collectMemberFieldPolicy(workspace),
      clusterHorizontal: link.chartLayout.clusterHorizontal,
      dataField,
    };
  },

  async teams(_parent, { linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);

    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleFields, visibleNodes } = link;

    if (!visibleFields.includes("teams")) {
      return {
        teams: [],
      };
    }

    const teams = workspace.teams
      .map((team) =>
        formatTeam(workspace, team, visibleFields, visibleNodes, true)
      )
      .sort(
        (a, b) =>
          Date.parse((b as unknown as { updateAt: string }).updateAt) -
          Date.parse((a as unknown as { updateAt: string }).updateAt)
      );

    return { teams };
  },

  async positionsWithoutTeam(_parent, { linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleNodes, visibleFields } = link;

    if (!visibleFields.includes("teams")) {
      return {
        positions: [],
      };
    }

    const {
      orgTree: { rootNode },
    } = workspace;
    const positions = getNodes(rootNode, (node): node is DomainPosition => {
      if (node.type !== "position" || node.teamIds.length) {
        return false;
      }

      if (visibleNodes.includes(node.id)) {
        return true;
      }
      const parent = getNode(
        rootNode,
        (n): n is DomainPosition | DomainDepartment | DomainRootNode =>
          n.id === node.parentId
      );

      if (
        parent &&
        parent.type !== "root" &&
        woEmptySubtrees(parent, visibleNodes).length
      ) {
        return true;
      }

      return false;
    });

    return {
      positions: positions.map((position) =>
        formatPosition(workspace, position, visibleFields, visibleNodes, true)
      ),
    };
  },

  async positions(_parentNode, { linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleNodes, visibleFields } = link;

    const {
      orgTree: { rootNode },
    } = workspace;

    const positions = getNodes(
      rootNode,
      (n): n is DomainPosition => n.type === "position"
    );

    return positions.map((position) =>
      formatPosition(workspace, position, visibleFields, visibleNodes, true)
    );
  },

  async allNodes(_parent, { linkId, nodesIds: _nodesIds, withDepartments }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleNodes, visibleFields, expandedNodes } = link;
    let nodes: ReadonlyDeep<
      (DomainDepartment | DomainPosition | DomainRootNode)[]
    > = [workspace.orgTree.rootNode];

    const nodesIds = _nodesIds.length ? _nodesIds : expandedNodes;

    const touchedNodes = getTouchedNodes(
      workspace.orgTree.rootNode,
      nodesIds,
      visibleNodes
    );

    nodes = [...nodes, ...touchedNodes];

    if (withDepartments) {
      const departments = getNodes(
        workspace.orgTree.rootNode,
        (n): n is DomainDepartment => n.type === "department"
      );

      const departmentsSubtrees = getTouchedNodes(
        workspace.orgTree.rootNode,
        departments.map((d) => d.id),
        visibleNodes
      );

      nodes = [...nodes, ...departmentsSubtrees];
    }

    const uniqNodes = uniqBy(nodes, "id");

    return uniqNodes.map((node) =>
      node.type === "position"
        ? formatPosition(workspace, node, visibleFields, visibleNodes, true)
        : node.type === "department"
        ? formatDepartment(workspace, node, visibleNodes, visibleNodes, true)
        : formatRootNode(
            workspace.orgTree.rootNode,
            workspace,
            visibleFields,
            visibleNodes
          )
    );
  },

  async openNodes(_parent, { linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleNodes, visibleFields } = link;

    const openPositionNodes = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition => n.type === "position" && n.memberId === null
    );

    const nodes = [
      ...openPositionNodes,
      ...openPositionNodes.map((n) => {
        return getNode(
          workspace.orgTree.rootNode,
          (node): node is DomainPosition | DomainDepartment | DomainRootNode =>
            node.id === n.parentId
        )!;
      }),
    ];

    const uniqNodes = uniqBy(nodes, "id");

    return uniqNodes.map((node) =>
      node.type === "position"
        ? formatPosition(workspace, node, visibleFields, visibleNodes, true)
        : node.type === "department"
        ? formatDepartment(workspace, node, visibleNodes, visibleNodes, true)
        : formatRootNode(
            workspace.orgTree.rootNode,
            workspace,
            visibleFields,
            visibleNodes
          )
    );
  },

  async expandedNode(_parent, { id, linkId }) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await getWorkspaceByLink(repository, linkId);
    const link = getDomainLink(workspace, linkId);
    const { visibleFields } = link;
    const { visibleNodes } = link;

    const expandedNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment | DomainPosition =>
        n.id === id && n.type !== "root"
    );

    assert(expandedNode, "Expanded node not found");

    return expandedNode.type === "position"
      ? formatPosition(
          workspace,
          expandedNode,
          visibleFields,
          visibleNodes,
          true
        )
      : formatDepartment(
          workspace,
          expandedNode,
          visibleFields,
          visibleNodes,
          true
        );
  },
} satisfies QueryResolvers<void>;

Object.entries(Query).forEach(([key, resolver]) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
  (Query as any)[key] = (
    parent: unknown,
    args: unknown,
    context: unknown,
    info: unknown
  ) => {
    const logger = getLogger({});

    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
    return (resolver as any)(parent, args, context, info).catch(
      (error: unknown) => {
        logger.error(error);

        throw error;
      }
    );
  };
});

const Subordinate: SubordinateResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === "position") {
      return "Position";
    }

    if (obj.type === "department") {
      return "Department";
    }

    return null;
  },
};

const resolvers: Resolvers<void> = {
  IMemberField,
  OrgTreeNode,
  Query,
  Subordinate,
};

export default resolvers;

function formatMember({
  workspace,
  member,
  visibleFields,
  visibleNodes,
  formatted = [],
}: {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<DomainMember>;
  visibleFields: readonly string[];
  visibleNodes: readonly string[];
  formatted?: ReadonlyDeep<Member>[];
}): ReadonlyDeep<Member> {
  let formattedMember: ReadonlyDeep<Member> | undefined = formatted.find(
    (m) => m.id === member.id
  );

  if (formattedMember) {
    return formattedMember;
  }

  const fields = collectMemberFields<Member, Position, Department, Team>(
    workspace,
    member,
    {
      formatDepartment(department) {
        return formatDepartment(
          workspace,
          department,
          visibleFields,
          visibleNodes
        );
      },
      formatTeam(team) {
        return formatTeam(workspace, team, visibleFields, visibleNodes, false);
      },
    }
  );

  formattedMember = {
    id: member.id,
    fields,
  };

  const updatedFields = fields.map((field) => {
    if (isSimplifiedUserField(field)) {
      if (field.memberId === member.id) {
        return {
          id: field.id,
          key: field.key,
          member: {
            ...formattedMember,
            fields: fields.filter((f) => {
              return (
                !isSimplifiedUserField(f) ||
                (isSimplifiedUserField(f) && f.memberId !== member.id)
              );
            }),
          },
        } as UserField<Member>;
      }

      if (field.memberId) {
        return {
          id: field.id,
          key: field.key,
          member: formatMember({
            workspace,
            member: workspace.members.find((m) => m.id === field.memberId)!,
            visibleFields,
            visibleNodes,
            formatted: [...formatted, formattedMember],
          }),
        } as UserField<Member>;
      }

      return {
        id: field.id,
        key: field.key,
        member: undefined,
      } as UserField<Member>;
    }

    if (isSimplifiedManagerField(field)) {
      if (field.positionId) {
        const position = getNode(
          workspace.orgTree.rootNode,
          (n): n is DomainPosition =>
            n.type === "position" && n.id === field.positionId
        )!;

        return {
          id: field.id,
          key: field.key,
          position: formatPosition(
            workspace,
            position,
            visibleFields,
            visibleNodes,
            false,
            {
              formatMember: (props) => {
                return formatMember({
                  ...props,
                  formatted: [...formatted, formattedMember],
                });
              },
              formatDepartment,
            }
          ),
          withoutManagerManual: field.withoutManagerManual,
        } as ManagerField<Position>;
      }

      return {
        id: field.id,
        key: field.key,
        position: undefined,
        withoutManagerManual: field.withoutManagerManual,
      } as ManagerField<Position>;
    }

    return field;
  });

  return {
    ...formattedMember,
    fields: updatedFields.filter((field) => visibleFields.includes(field.key)),
  };
}

function formatDepartment(
  workspace: ReadonlyDeep<Workspace>,
  department: ReadonlyDeep<DomainDepartment>,
  visibleFields: readonly string[],
  visibleNodes: readonly string[],
  includeSubordinates = false
): ReadonlyDeep<Department> {
  const subordinates = includeSubordinates
    ? woEmptySubtrees(
        {
          ...department,
          subordinates: department.subordinates,
        },
        visibleNodes
      )
    : null;
  const isHidden = !visibleNodes.includes(department.id);

  return {
    id: department.id,
    timestamp: department.timestamp,
    type: "department",
    parentId: department.parentId,
    title: isHidden ? undefined : department.title,
    departmentColor: department.color,
    managerId: department.managerId,
    subordinates: subordinates
      ? subordinates.map((subordinate) =>
          subordinate.type === "department"
            ? formatDepartment(
                workspace,
                subordinate,
                visibleFields,
                visibleNodes
              )
            : formatPosition(
                workspace,
                subordinate,
                visibleFields,
                visibleNodes
              )
        )
      : null,
    subordinatesNumber: subordinates ? subordinates.length : 0,
    isHidden,
  };
}

function formatPosition(
  workspace: ReadonlyDeep<Workspace>,
  position: ReadonlyDeep<DomainPosition>,
  visibleFields: readonly string[] = [],
  visibleNodes: readonly string[] = [],
  includeSubordinates = false,
  formatters: {
    formatMember: typeof formatMember;
    formatDepartment: typeof formatDepartment;
  } = {
    formatMember,
    formatDepartment,
  }
): ReadonlyDeep<Position> {
  const member = workspace.members.find((m) => m.id === position.memberId);
  const department = getParentDepartment(workspace, position.id);
  const subordinates = includeSubordinates
    ? woEmptySubtrees(
        {
          ...position,
          subordinates: position.subordinates,
        },

        visibleNodes
      )
    : null;

  const isHidden = !visibleNodes.includes(position.id);

  const { jobDescriptionLink = "", hiringManagerId = null } =
    position.reference ?? {};

  const formattedPosition: ReadonlyDeep<Position> = {
    id: position.id,
    timestamp: position.timestamp,
    type: "position",
    parentId: position.parentId,
    member:
      member &&
      formatters.formatMember({
        workspace,
        member,
        visibleFields,
        visibleNodes,
      }),
    title: visibleFields.includes("jobTitle") ? position.title : "",

    department: visibleFields.includes("department")
      ? department
        ? formatters.formatDepartment(
            workspace,
            department,
            visibleFields,
            visibleNodes
          )
        : undefined
      : undefined,
    subordinates: subordinates
      ? subordinates.map((subordinate) =>
          subordinate.type === "department"
            ? formatters.formatDepartment(
                workspace,
                subordinate,
                visibleFields,
                visibleNodes
              )
            : formatPosition(
                workspace,
                subordinate,
                visibleFields,
                visibleNodes
              )
        )
      : null,
    subordinatesNumber: subordinates ? subordinates.length : 0,
    teams: visibleFields.includes("teams")
      ? position.teamIds.map((teamId) => {
          const team = workspace.teams.find((t) => t.id === teamId);

          assert(
            team,
            `Team with id "${teamId}" does not exist at workspace "${workspace.id}"`
          );

          return formatTeam(
            workspace,
            team,
            visibleFields,
            visibleNodes,
            false
          );
        })
      : [],
    isHidden: false,
    reference: {
      jobDescriptionLink,
      hiringManagerId,
    },
  };

  if (isHidden) {
    return {
      ...formattedPosition,
      title: undefined,
      teams: [],
      department: undefined,
      member: undefined,
      isHidden: true,
    };
  }

  return {
    ...formattedPosition,
  };
}

function woEmptySubtrees(
  node: ReadonlyDeep<DomainDepartment | DomainPosition | DomainRootNode>,
  visibleNodes: readonly string[]
): ReadonlyDeep<DomainDepartment | DomainPosition>[] {
  const subordinates = node.subordinates;
  const visibleSubordinates = subordinates.filter((subordinate) => {
    const subTree = getNodes(
      subordinate,
      (n): n is DomainDepartment | DomainPosition => true
    );

    return subTree.some((n) => visibleNodes.includes(n.id));
  });

  return visibleSubordinates;
}

async function getWorkspaceByLink(
  repository: WorkspaceRepository,
  linkId: string
): Promise<ReadonlyDeep<Workspace>> {
  const workspace = await repository.getWorkspaceByLink(linkId);

  if (!workspace) {
    throw new Error("Workspace not found");
  }

  return workspace;
}

function formatRootNode(
  rootNode: ReadonlyDeep<DomainRootNode>,
  workspace: ReadonlyDeep<Workspace>,
  visibleFields: readonly string[],
  visibleNodes: readonly string[]
): ReadonlyDeep<RootNode> {
  const subordinates = woEmptySubtrees(
    {
      ...rootNode,
      subordinates: rootNode.subordinates,
    },
    visibleNodes
  );

  return {
    id: rootNode.id,
    type: "root",
    title: workspace.name,
    logoUrl: workspace.iconUrl ?? "",
    subordinates: subordinates.map((subordinate) =>
      subordinate.type === "position"
        ? formatPosition(workspace, subordinate, visibleFields, visibleNodes)
        : formatDepartment(workspace, subordinate, visibleFields, visibleNodes)
    ),
    subordinatesNumber: subordinates.length,
  };
}

function getDomainLink(
  workspace: ReadonlyDeep<Workspace>,
  linkId: string
): ReadonlyDeep<DomainLink> {
  const link = workspace.links.find((l) => l.id === linkId);

  assert(link, "Link not found");

  return link;
}

function getFullBranchWoEmptySubtrees(
  node: ReadonlyDeep<DomainPosition | DomainDepartment>,
  rootNode: ReadonlyDeep<DomainRootNode>,
  visibleNodes: readonly string[],
  result: ReadonlyDeep<DomainPosition | DomainDepartment>[] = []
): ReadonlyDeep<DomainPosition | DomainDepartment>[] {
  const parent = getNode(
    rootNode,
    (n): n is DomainPosition | DomainDepartment =>
      n.type !== "root" && n.id === node.parentId
  );
  let currentResult = [...result];

  currentResult.push(node);
  currentResult = currentResult.concat(woEmptySubtrees(node, visibleNodes));

  if (parent) {
    currentResult.push(parent);
    currentResult = currentResult.concat(
      getFullBranchWoEmptySubtrees(
        parent,
        rootNode,
        visibleNodes,
        currentResult
      )
    );
  }

  return currentResult;
}

function getTouchedNodes(
  rootNode: ReadonlyDeep<DomainRootNode>,
  nodesIds: ReadonlyDeep<string[]>,
  visibleNodes: readonly string[]
): ReadonlyDeep<DomainPosition | DomainDepartment>[] {
  const nodes = nodesIds
    .map((id) =>
      getNode(
        rootNode,
        (n): n is DomainPosition | DomainDepartment =>
          n.type !== "root" && n.id === id
      )
    )
    .filter((x): x is DomainPosition | DomainDepartment => x !== null);

  const duplicatedNodes = nodes.flatMap((n) =>
    getFullBranchWoEmptySubtrees(n, rootNode, visibleNodes)
  );

  return uniqBy(
    duplicatedNodes.concat(woEmptySubtrees(rootNode, visibleNodes)),
    "id"
  );
}

function formatTeam(
  workspace: ReadonlyDeep<Workspace>,
  team: DomainTeam,
  visibleFields: readonly string[],
  visibleNodes: readonly string[],
  includePositions: boolean
): ReadonlyDeep<Team> {
  if (!includePositions) {
    return {
      ...team,
      positions: [],
    };
  }

  const positions = getNodes(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition =>
      n.type === "position" && n.teamIds.includes(team.id)
  );

  const formattedPositions = positions.map((p) =>
    formatPosition(workspace, p, visibleFields, visibleNodes, true)
  );

  return {
    ...team,
    positions: sortBy(formattedPositions, (pos) => {
      const name: TextField | undefined = pos.member?.fields.find(
        (f) => "text" in f && f.key === "name"
      );

      return name?.text?.toLowerCase();
    }),
  };
}
