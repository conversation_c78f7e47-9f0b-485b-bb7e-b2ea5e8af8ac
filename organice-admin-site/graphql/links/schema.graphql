scalar Date

enum CacheControlScope {
  PUBLIC
  PRIVATE
}

# the response cache is calculated based on the following strategy:
#  https://www.apollographql.com/docs/apollo-server/performance/caching/#calculating-cache-behavior
# so it is important to set @cacheControl(inheritMaxAge: true) to nested type fields

directive @cacheControl(
  maxAge: Int
  scope: CacheControlScope
  inheritMaxAge: Boolean
) on FIELD_DEFINITION | OBJECT | INTERFACE | UNION

enum Color {
  BLUE
  CYAN
  SKY
  EMERALD
  LIME
  TEAL
  GREEN
  YELLOW
  AMBER
  ORANGE
  RED
  ROSE
  VIOLET
  FUCHSIA
  PINK
}

type MemberFieldPolicy {
  id: ID!
  label: String!
  type: String!
}

type Link {
  id: ID!
  title: String!
  visibleViews: [String]!
  expandedNodes: [String!]!
  memberFields: [MemberFieldPolicy!] @cacheControl(inheritMaxAge: true)
  clusterHorizontal: Boolean!
  dataField: MemberFieldPolicy!
}

type Query {
  openNodes(linkId: ID!): [OrgTreeNode!]! @cacheControl(maxAge: 60)
  member(linkId: ID!, id: ID!): Member! @cacheControl(maxAge: 60)
  link(id: ID!): Link! @cacheControl(maxAge: 60)
  allNodes(
    linkId: ID!
    nodesIds: [String!]!
    withDepartments: Boolean
  ): [OrgTreeNode!]! @cacheControl(maxAge: 60)
  # NOTE: this is a cache only query, we define it here only to get
  # codegen to generate the types for it
  allKnownNodes: [OrgTreeNode!]!
  expandedNode(linkId: ID!, id: ID!): Subordinate! @cacheControl(maxAge: 60)
  teams(linkId: ID!): Teams! @cacheControl(maxAge: 60)
  positions(linkId: ID!): [Position!]!
  positionsWithoutTeam(linkId: ID!): PositionsWithoutTeam!
    @cacheControl(maxAge: 60)
}

type Teams {
  teams: [Team!]! @cacheControl(inheritMaxAge: true)
}

union Subordinate = Department | Position
union OrgTreeNode = Department | Position | RootNode

type RootNode {
  id: ID!
  type: String!
  title: String!
  logoUrl: String!
  subordinates: [Subordinate!]! @cacheControl(inheritMaxAge: true)
  subordinatesNumber: Int!
}

type Team {
  id: String!
  label: String!
  color: Color!
  positions: [Position!]! @cacheControl(inheritMaxAge: true)
  managerId: ID
}

type Position {
  id: ID!
  type: String!
  parentId: ID!
  title: String
  member: Member @cacheControl(inheritMaxAge: true)
  teams: [Team!]! @cacheControl(inheritMaxAge: true)
  department: Department @cacheControl(inheritMaxAge: true)
  subordinates: [Subordinate!] @cacheControl(inheritMaxAge: true)
  subordinatesNumber: Int!
  timestamp: Float!
  isHidden: Boolean!
  reference: PositionReference! @cacheControl(inheritMaxAge: true)
  managedDepartmentId: ID
}

type PositionReference {
  jobDescriptionLink: String
  hiringManagerId: String
}

type PositionsWithoutTeam {
  positions: [Position!]! @cacheControl(inheritMaxAge: true)
}

type Department {
  id: ID!
  type: String!
  parentId: ID!
  title: String
  departmentColor: Color!
  subordinates: [Subordinate!] @cacheControl(inheritMaxAge: true)
  subordinatesNumber: Int!
  timestamp: Float!
  isHidden: Boolean!
  managerId: ID
}

interface IMemberField {
  id: String!
  key: String!
}

type PhotoField implements IMemberField {
  id: String!
  key: String!
  url512: String
  url72: String
}

type TextField implements IMemberField {
  id: String!
  key: String!
  text: String
}

type DateField implements IMemberField {
  id: String!
  key: String!
  date: Date
}

type ManagerField implements IMemberField {
  id: String!
  key: String!
  position: Position @cacheControl(inheritMaxAge: true)
  withoutManagerManual: Boolean
}

type TeamsField implements IMemberField {
  id: String!
  key: String!
  teams: [Team!]! @cacheControl(inheritMaxAge: true)
}

type DepartmentField implements IMemberField {
  id: String!
  key: String!
  department: Department @cacheControl(inheritMaxAge: true)
}

type Member {
  id: ID!
  hideBirthday: Boolean
  fields: [IMemberField!]! @cacheControl(inheritMaxAge: true)
  position: Position @cacheControl(inheritMaxAge: true)
}

type UserField implements IMemberField {
  id: String!
  key: String!
  member: Member @cacheControl(inheritMaxAge: true)
}

type LinkField implements IMemberField {
  id: String!
  key: String!
  link: String
}
