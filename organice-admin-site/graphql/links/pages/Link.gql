fragment RootNodeFields on RootNode {
  id
  type
  rootTitle: title
  logoUrl
  rootSubordinates: subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
  subordinatesNumber
}

fragment TeamFields on Team {
  id
  label
  color
  managerId
}

fragment PositionFields on Position {
  id
  type
  parentId
  title
  isHidden
  managedDepartmentId
  department {
    id
    title
    departmentColor
    managerId
  }
  teams {
    ...TeamFields
  }
  member {
    id
    fields {
      id
      key
      ... on TextField {
        text
      }
      ... on DateField {
        date
      }
      ... on PhotoField {
        url72
      }
      ... on TeamsField {
        teams {
          ...TeamFields
        }
      }
      ... on LinkField {
        link
      }
      ... on UserField {
        member {
          id
          fields {
            id
            key
            ... on TextField {
              text
            }
            ... on PhotoField {
              url72
            }
          }
        }
      }
      ... on DepartmentField {
        department {
          id
          title
          departmentColor
          managerId
        }
      }
      ... on ManagerField {
        position {
          id
          title
          member {
            id
            fields {
              id
              key
              ... on TextField {
                text
              }
              ... on DateField {
                date
              }
              ... on PhotoField {
                url72
              }
            }
          }
        }
        withoutManagerManual
      }
    }
  }
  subordinatesNumber
  subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
  reference {
    jobDescriptionLink
    hiringManagerId
  }
}

fragment DepartmentFields on Department {
  id
  type
  parentId
  managerId
  title
  isHidden
  departmentColor
  subordinatesNumber
  subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
}

query LinkPage($linkId: ID!, $nodesIds: [String!]!, $withDepartments: Boolean) {
  link(id: $linkId) {
    id
    title
    visibleViews
    expandedNodes
    memberFields {
      id
      label
      type
    }
    clusterHorizontal
    # TODO: test what will happen if country field is not defined
    dataField {
      id
      label
      type
    }
  }
  allNodes(
    linkId: $linkId
    nodesIds: $nodesIds
    withDepartments: $withDepartments
  ) {
    ... on Position {
      ...PositionFields
    }
    ... on Department {
      ...DepartmentFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

query LinkPageAllKnownNodes {
  allKnownNodes @client {
    ... on Position {
      ...PositionFields
    }
    ... on Department {
      ...DepartmentFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

query HiringPage($linkId: ID!) {
  openNodes(linkId: $linkId) {
    ... on Position {
      ...PositionFields
    }
    ... on Department {
      ...DepartmentFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

query Teams($linkId: ID!) {
  teams(linkId: $linkId) {
    teams {
      id
      label
      color
      positions {
        ...PositionFields
      }
    }
  }
  positionsWithoutTeam(linkId: $linkId) {
    positions {
      ...PositionFields
    }
  }
}

query Country($linkId: ID!) {
  positions(linkId: $linkId) {
    ...PositionFields
  }
}

fragment MemberSidebarMemberFields on Member {
  id
  position {
    id
    managedDepartmentId
  }
  hideBirthday
  fields {
    id
    key
    ... on LinkField {
      link
    }
    ... on UserField {
      member {
        id
        fields {
          id
          key
          ... on TextField {
            text
          }
          ... on PhotoField {
            url72
          }
        }
      }
    }
    ... on TextField {
      text
    }
    ... on DateField {
      date
    }
    ... on PhotoField {
      url72
      url512
    }
    ... on TeamsField {
      teams {
        ...TeamFields
      }
    }
    ... on DepartmentField {
      department {
        id
        title
        departmentColor
      }
    }
    ... on ManagerField {
      position {
        id
        title
        member {
          id
          fields {
            id
            key
            ... on TextField {
              text
            }
            ... on PhotoField {
              url72
            }
          }
        }
      }
      withoutManagerManual
    }
  }
}

query MemberSidebar($linkId: ID!, $memberId: ID!) {
  member(linkId: $linkId, id: $memberId) {
    ...MemberSidebarMemberFields
  }
}

query FlattenPositionHiringManager($linkId: ID!, $memberId: ID!) {
  member(linkId: $linkId, id: $memberId) {
    id
    fields {
      id
      key
      ... on TextField {
        text
      }
      ... on PhotoField {
        url72
        url512
      }
    }
  }
}

query ExpandedNode($linkId: ID!, $id: ID!) {
  expandedNode(linkId: $linkId, id: $id) {
    ... on Position {
      ...PositionFields
      subordinates {
        ... on Position {
          ...PositionFields
        }
        ... on Department {
          ...DepartmentFields
        }
      }
    }
    ... on Department {
      ...DepartmentFields
      subordinates {
        ... on Position {
          ...PositionFields
        }
        ... on Department {
          ...DepartmentFields
        }
      }
    }
  }
}
