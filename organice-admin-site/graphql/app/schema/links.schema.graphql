extend type Query {
  links: [Link!]!
  link(id: ID!): Link!
}

extend type Mutation {
  toggleNodeVisibility(id: ID!, linkId: ID!): Link!
  share(title: String!): Link!
  updateLink(id: ID!, link: UpdateLinkInput!): Link!
  syncExpandedNodes(linkId: ID!, nodes: [String!]!): Link!
  unshare(id: ID!): Void
  updateLinkChartLayout(linkId: ID!, layout: ChartLayoutInput!): Link!
}

type Link {
  id: ID!
  title: String!
  expandedNodes: [String!]!
  visibleNodes: [String!]!
  visibleFields: [String!]!
  visibleViews: [String!]!
  createdAt: DateTime
  clusterHorizontal: Boolean
  dataField: MemberFieldPolicy!
}

input UpdateLinkInput {
  title: String
  visibleFields: [String!]
  visibleViews: [String!]
}
