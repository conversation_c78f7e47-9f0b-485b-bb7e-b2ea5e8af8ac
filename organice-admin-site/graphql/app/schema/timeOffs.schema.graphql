enum TimeOffRequestStatus {
  PENDING
  APPROVED
  REJECTED
}

enum YearStart {
  Calendar
  StartDate
}

type TimeOffs {
  id: String!
  channel: Channel
  isEnable: Boolean!
  createDiscussionChannelWhenMultipleApprovers: Boolean!
  changeSlackStatus: Boolean!
}

type UpcomingEvent {
  date: Date!
  events: [EventWithMember!]!
}

type EventWithMember {
  event: CalendarEvent!
  member: Member
}

type CalendarRow {
  member: Member!
  events: [CalendarEvent!]
}

union CalendarEvent = TimeOffEvent | CelebrationEvent | HolidayEvent

type TimeOffEvent {
  id: ID!
  startDate: Date!
  endDate: Date!
  duration: Int
  type: TimeOffRequestType!
  status: TimeOffRequestStatus!
  rejectReason: String
  editable: Boolean!
  deletable: Boolean!
}

type CelebrationEventType {
  id: ID!
  color: Color!
  label: String!
  emoji: String!
}

type CelebrationEvent {
  id: ID!
  startDate: Date!
  endDate: Date!
  duration: Int
  type: CelebrationEventType!
}

type HolidayEventType {
  id: ID!
  color: Color!
  label: String!
  emoji: String!
}

type HolidayEvent {
  id: ID!
  startDate: Date!
  endDate: Date!
  duration: Int
  type: HolidayEventType!
  title: String!
  description: String
  isOfficial: Boolean!
}

enum UpcomingGroupBy {
  DAY
  WEEK
  MONTH
}

type TimeOffRequestType {
  id: ID!
  color: Color!
  label: String!
  emoji: String!
  policies: [TimeOffPolicy!]
}

type TimeOffRequest {
  id: ID!
  approvers: [Member!]!
  handledBy: Member
  startDate: Date!
  endDate: Date!
  requester: Member!
  type: TimeOffRequestType!
  status: TimeOffRequestStatus!
  rejectReason: String
  comment: String
  editable: Boolean!
  deletable: Boolean!
}

extend type Query {
  timeOffRequest(id: ID!): TimeOffRequest
  timeOffs: TimeOffs!
  timeOffTypes: [TimeOffRequestType!]!
  celebrationTypes: [CelebrationEventType!]!
  holidayTypes: [HolidayEventType!]!
  upcomingEvents(
    to: Date!
    groupBy: UpcomingGroupBy!
    eventTypeIds: [ID!]
  ): [UpcomingEvent!]!
  # TODO: rename
  calendarRowGroups(
    from: Date!
    to: Date!
    eventTypeIds: [ID!]
  ): [CalendarRow!]!
  memberEvents(
    id: ID!
    from: Date!
    to: Date!
    eventTypeIds: [ID!]
  ): [CalendarEvent!]!
  timeOffPolicies: [TimeOffPolicy!]!
  calendarFeed(id: ID!): CalendarFeed!
  calendarFeeds: [CalendarFeed!]!
}

input TimeOffTypeInput {
  label: String!
  emojiUnicode: String!
  emojiShortcode: String!
  color: Color!
}

input AddTimeOffInput {
  requester: ID!
  requestType: ID!
  startDate: Date!
  endDate: Date!
  comment: String
}

input UpdateTimeOffInput {
  requestType: ID!
  startDate: Date!
  endDate: Date!
}

input TimeOffPolicyTypeInput {
  typeId: ID!
  yearStart: YearStart!
  onStartQuota: Float
  rollOverToNextYear: Boolean!
  maxCapacity: Float
  accrualsQuota: Float
  accuralsFrequency: AccrualsFrequency
}

enum DayOfWeek {
  Monday
  Tuesday
  Wednesday
  Thursday
  Friday
  Saturday
  Sunday
}

input TimeOffPolicyInput {
  title: String!
  isDefault: Boolean!
  workDays: [DayOfWeek!]!
  includedWeekendDays: [DayOfWeek!]!
  notifyAccruals: Boolean!
  typePolicies: [TimeOffPolicyTypeInput!]!
}

type TimeOffPolicy {
  id: ID!
  title: String!
  members: [Member!]!
  isDefault: Boolean!
  typePolicies: [TimeOffPolicyType!]!
  workDays: [DayOfWeek!]!
  includedWeekendDays: [DayOfWeek!]!
  notifyAccruals: Boolean!
}

type CalendarFeedFilters {
  managers: [String!]!
  departments: [String!]!
  teams: [String!]!
  countries: [String!]!
  types: [String!]!
}

type CalendarFeed {
  id: ID!
  title: String!
  memberId: String
  filters: CalendarFeedFilters!
}

enum AccrualsFrequency {
  Week
  Month
}

type TimeOffPolicyType {
  type: TimeOffRequestType!
  onStartQuota: Float!
  rollOverToNextYear: Boolean!
  yearStart: YearStart!
  accrualsQuota: Float!
  accuralsFrequency: AccrualsFrequency
  nextAccruals: Date
  maxCapacity: Float!
}

input CalendarFeedInput {
  title: String!
  memberId: ID
  managers: [String!]!
  departments: [String!]!
  teams: [String!]!
  countries: [String!]!
  types: [String!]!
}

input UpdateBalanceInput {
  memberId: ID!
  typeId: ID!
  balance: Float!
}

extend type Mutation {
  setTimeOffSettings(
    isEnable: Boolean
    channel: ChannelInput
    createDiscussionChannelWhenMultipleApprovers: Boolean
    changeSlackStatus: Boolean
  ): TimeOffs!
  openTimeOffsInApp: Void
  addTimeOffRequest(request: AddTimeOffInput!): Void
  updateTimeOffRequest(id: ID!, request: UpdateTimeOffInput!): Void
  deleteTimeOffRequest(id: ID!): Void
  approveTimeOffRequest(id: ID!): Void
  rejectTimeOffRequest(id: ID!, rejectReason: String): Void
  addTimeOffRequestType(type: TimeOffTypeInput!): TimeOffRequestType!
  updateTimeOffRequestType(
    id: ID!
    type: TimeOffTypeInput!
  ): TimeOffRequestType!
  deleteTimeOffRequestType(id: ID!): Void
  reorderTimeOffTypes(types: [ID!]!): Void
  updateMemberTimeOffPolicy(memberId: ID!, policyId: ID): Member!
  updateTimeOffBalance(balance: UpdateBalanceInput!): Member!
  deleteTimeOffPolicy(id: ID!): Void
  updateTimeOffPolicy(id: ID!, policy: TimeOffPolicyInput!): TimeOffPolicy!
  addTimeOffPolicy(policy: TimeOffPolicyInput!): TimeOffPolicy!
  addCalendarFeed(feed: CalendarFeedInput!): CalendarFeed!
  updateCalendarFeed(id: ID!, feed: CalendarFeedInput!): CalendarFeed!
  deleteCalendarFeed(id: ID!): Void
}
