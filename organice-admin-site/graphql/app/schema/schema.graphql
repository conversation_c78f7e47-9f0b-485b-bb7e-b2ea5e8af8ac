scalar Void
scalar DateTime
scalar Date

type Query {
  myWorkspace: Workspace!
  me: Me!
  policy: Policy!
  slackCustomFields(refreshFromSlack: Boolean!): SlackCustomFields!
  member(id: ID!): Member!
  members(isAdmin: <PERSON><PERSON><PERSON>, missingFields: [String!]): [Member!]!
  referralSettings: ReferralSettings!
  channels: [Channel!]!
  reports: Reports!
  slackIntegration: SlackIntegration!
  slackPermissionsIssues: SlackPermissionsIssues
  celebrationSettings: CelebrationSettings!
  predictedOrgTree: PredictedOrgChart!
  stats: WorkspaceStats!
  logs(memberId: ID!, from: Date!, to: Date!, types: [String!]): [Log!]!
}

union Log = TimeOffLog | BalanceLog | SlackStatusLog

interface LogItem {
  id: ID!
  createdAt: DateTime!
}

enum TimeOffLogType {
  CREATE
  UPDATE
  APPROVE
  REJECT
  DELETE
}

type TimeOffLogData {
  initiator: Member
  request: TimeOffRequest!
}

type TimeOffLog implements LogItem {
  id: ID!
  type: TimeOffLogType!
  createdAt: DateTime!
  data: TimeOffLogData!
}

enum BalanceLogType {
  ACCRUALS
  RESET
  MANUAL
  YEAR_START
  ROLL_OVER
  TEAM_JOIN
  REQUEST_UPDATE
  REQUEST_DELETE
}

type BalanceLogData {
  initiator: Member
  absolute: Float
  increase: Float
  type: TimeOffRequestType!
}

type BalanceLog implements LogItem {
  id: ID!
  type: BalanceLogType!
  createdAt: DateTime!
  data: BalanceLogData!
}

enum SlackStatusLogType {
  SET
  REVERT
}

type SlackStatusLogData {
  initiator: Member
  previousStatus: String
  newStatus: String!
}

type SlackStatusLog implements LogItem {
  id: ID!
  type: SlackStatusLogType!
  createdAt: DateTime!
  data: SlackStatusLogData!
}

type CelebrationSettings {
  id: String!
  birthdaysEnabled: Boolean!
  birthdaysGifsEnabled: Boolean!
  birthdaysChangeSlackStatus: Boolean!
  anniversaryEnabled: Boolean!
  anniversaryGifsEnabled: Boolean!
  anniversaryChangeSlackStatus: Boolean!
  channel: Channel
  timeOfPosting: String!
  timeZone: String!
  whenToPostIfHappensOnWeekend: String!
  birthdayTemplates: [Template!]!
  anniversaryTemplates: [Template!]!
}

type Template {
  id: String!
  text: String!
}

input ChannelInput {
  id: String!
  name: String
}

input CelebrationSettingsInput {
  birthdaysEnabled: Boolean
  birthdaysChangeSlackStatus: Boolean
  birthdaysGifsEnabled: Boolean
  anniversaryEnabled: Boolean
  anniversaryGifsEnabled: Boolean
  anniversaryChangeSlackStatus: Boolean
  channel: ChannelInput
  timeOfPosting: String
  timeZone: String
  whenToPostIfHappensOnWeekend: String
  birthdayTemplate: String
  anniversaryTemplate: String
}

type DigestMessage {
  ts: String
  channel: String
  positions: String
  updatedAt: Float
  createdAt: Float
}

type Reference {
  isEnable: Boolean
  channel: String
  message: DigestMessage
}

type Workspace {
  id: ID!
  name: String!
  url: String!
  iconUrl: String
  """
  returns all custom fields existing in a Slack workspace
  """
  customFields: [WorkspaceCustomField]
  """
  returns ordered fields policies for member.fields
  """
  memberFields: [MemberFieldPolicy!]
  editable: Boolean!
  clusterHorizontal: Boolean!
  dataField: MemberFieldPolicy!
  appId: String
  installedAt: DateTime
}

type WorkspaceStats {
  id: ID!
  # total number of users in Slack workspace
  totalMembers: Int!
  # total number of users visible on the chart
  totalMembersWithPosition: Int!
  # totalMembersWithPosition + all open
  totalPositions: Int!
  # if notificationsPolicy.enable is true shows number of users who haven't received 4 messages from the system
  totalPositionsInProgress: Int!
  # total number of users who completed all fields
  totalPositionsOnboarded: Int!
  # total number of members who provided their BD date
  totalNumberWithBirthday: Int!
  # total number of members who provided their Anniversary date
  totalNumberWithAnniversary: Int!
  # total number of teams
  totalNumberTeams: Int!
  # total number of departments
  totalNumberDepartments: Int!
  # total number of departments
  totalNumberCountries: Int!
}

type SlackCustomFields {
  slackCustomFields: [WorkspaceCustomField!]!
}

type WorkspaceCustomField {
  id: String!
  label: String!
  type: String!
  visible: Boolean!
  isEditableOnSlackOnly: Boolean!
  isSlackDefaultField: Boolean!
  hint: String
  variants: [String]
}

interface IMemberField {
  id: String!
  key: String!
}

type PhotoField implements IMemberField {
  id: String!
  key: String!
  url512: String
  url72: String
}

type TextField implements IMemberField {
  id: String!
  key: String!
  text: String
}

type LinkField implements IMemberField {
  id: String!
  key: String!
  link: String
}

type UserField implements IMemberField {
  id: String!
  key: String!
  member: Member
}

type DateField implements IMemberField {
  id: String!
  key: String!
  date: Date
}

type ManagerField implements IMemberField {
  id: String!
  key: String!
  position: Position
  withoutManagerManual: Boolean
}

type TeamsField implements IMemberField {
  id: String!
  key: String!
  teams: [Team!]!
}

type DepartmentField implements IMemberField {
  id: String!
  key: String!
  department: Department
}

type MemberTimeOffTypeStatus {
  memberId: ID!
  typeId: ID!
  balance: Float!
  nextResetAt: DateTime
}

type Member {
  id: ID!
  isAdmin: Boolean
  name: String
  photoUrl: String
  photo512Url: String
  photo72Url: String
  realName: String
  displayName: String
  title: String
  phone: String
  email: String
  notes: String
  nonFilledFields: [NonFilledField!]
  position: Position
  fields: [IMemberField!]!
  hideBirthday: Boolean
  timeOffs: [MemberTimeOffTypeStatus!]!
  timeOffTypePolicy: TimeOffPolicy
  workingDays(from: Date, to: Date): WorkingDaysResult
}

type NonFilledField {
  id: ID!
  label: String!
}

type Me {
  id: ID!
  intercomHash: String!
  isAdmin: Boolean
  isSlackWorkspaceAdmin: Boolean
  isSlackWorkspaceOwner: Boolean
  isSlackWorkspacePrimaryOwner: Boolean
  name: String
  country: String
  photoUrl: String
  photo512Url: String
  photo72Url: String
  realName: String
  title: String
  phone: String
  email: String
  notes: String
  positionId: String
  workspaceId: String
  botScopes: [String]
  shouldReceiveIntercomPricingMessage: Boolean
}

type Channel {
  id: String!
  name: String!
  membersCount: Int
}

type PositionReferrer {
  id: String
  name: String
}

type CandidateStatus {
  title: String!
  userId: String
}

type PositionCandidate {
  name: String
  email: String
  notes: String
  status: CandidateStatus
  referrer: PositionReferrer
}

type MemberEditable {
  value: Boolean!
  reason: String
}

type PredictedOrgChart {
  status: PredictedOrgChartStatus!
}

enum PredictedOrgChartStatus {
  NOT_AVAILABLE
  NOT_REQUESTED
  REQUESTED
  READY
}

type ReferralSettings {
  isEnable: Boolean!
  id: String!
  channel: Channel
  nextDigestAt: String
  message: DigestMessage
}

type Policy {
  id: ID!
  fields: [FieldPolicy!]!
  notifications: Notifications!
}

type FieldPolicy {
  id: ID!
  label: String!
  description: String
  type: String!
  required: Boolean!
  publiclyAvailable: Boolean!
  slackFieldId: String
  violationsCount: Int!
  canEdit: Boolean
  canToggleRequired: Boolean
  canChangeField: Boolean
}

type MemberFieldPolicy {
  id: ID!
  label: String!
  type: String!
  required: Boolean!
  isEditable: Boolean
  isPublic: Boolean
}

type FieldAlternative {
  id: ID!
  label: String!
  isDefault: Boolean!
}

enum DefaultField {
  MANAGER
  PHOTO_URL
  PHONE
  TITLE
  BIRTHDAY
  ANNIVERSARY
}

type Notifications {
  welcomeMessage: String!
  followUpMessage: String!
  enable: Boolean!
  schedule: String
}

input MemberProfileCustomField {
  message: String!
  id: String!
}

input MemberProfileInput {
  memberId: String!
  title: String
  realName: String
  photoUrl: String
  email: String
  notes: String
  hideBirthday: Boolean
  customField: MemberProfileCustomField
}

type Reports {
  enabledReports: Boolean
  id: String!
}

type SlackIntegration {
  enabledSlackIntegration: Boolean
  id: String!
}

enum TemplateTypes {
  birthday
  anniversary
}

type SlackPermissionsIssues {
  cannotUpdateRegularMembers: [Member!]!
  cannotUpdateAdmins: [Member!]!
  cannotUpdateOwners: [Member!]!
  cannotUpdatePrimaryOwner: Boolean!
  primaryOwner: Member!
  sentPrimaryOwnerApproveNotification: Boolean!
  installationLink: String!
}

type Mutation {
  editTemplate(id: String!, type: TemplateTypes!, text: String!): Template!
  toggleSlackIntegration(slackIntegration: Boolean!): SlackIntegration!
  removeTemplate(id: String!, type: TemplateTypes!): Template!
  setCelebrationSettings(
    settings: CelebrationSettingsInput!
  ): CelebrationSettings!
  toggleRequired(id: String!): FieldPolicy!
  addField(label: String!, type: String!, publiclyAvailable: Boolean!): Policy!
  editField(
    id: ID!
    label: String!
    type: String!
    publiclyAvailable: Boolean!
  ): Policy!
  deleteField(id: ID!): Policy!
  changeSlackField(id: ID!, slackFieldId: String): FieldPolicy!
  updateMemberProfile(profile: MemberProfileInput!): Member!
  makeNonAdmin(memberId: ID!): Member!
  makeAdmin(memberId: ID!): Member!
  changeReferrals(enabledReferrals: Boolean!): ReferralSettings!
  setChannelForReferrals(channelId: String!): ReferralSettings!
  changeNotificationsPolicy(
    welcomeMessage: String
    followUpMessage: String
    enable: Boolean
    schedule: String
  ): Policy!
  changeReports(enabledReports: Boolean): Reports!
  notifySlackAboutReferralPosition: Void
  askPrimaryOwnerToApprove: SlackPermissionsIssues
  requestOrgTreePrediction: PredictedOrgChart!
  collectEmail(fullName: String!, email: String!, companyName: String!): Void
}

type WorkingDaysResult {
  workedDays: Int!
  totalWorkingDays: Int!
}
