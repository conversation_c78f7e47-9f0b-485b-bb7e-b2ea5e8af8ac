type SurveyTemplate {
  id: ID!
  title: String!
  isDefault: Boolean!
  questions: [SurveyTemplateQuestion!]!
  createdBy: Member
  createdAt: Date
  updatedAt: Date
  usageCount: Int!
}

type Survey {
  id: ID!
  title: String!
  isAnonymous: Boolean!
  status: SurveyStatus!
  channelId: String
  channelName: String
  startMessagePermalink: String!
  startMessageTS: String!
  completionRate: Int!
  responders: [Responder!]!
  answers: [SurveyAnswer!]!
  questions: [SurveyQuestion!]!
  createdBy: Member
  createdAt: Date!
  updatedAt: Date!
  template: SurveyTemplate
  defaultTemplateId: String
  questionsNumber: Int!
  participantsCount: Int!
}

enum SurveyStatus {
  InProgress
  Closed
}

enum SurveyStatusFilter {
  All
  InProgress
  Closed
}

interface IScaleOption {
  id: ID!
  value: Int!
  label: String!
}

interface ITextOption {
  id: ID!
  value: String!
}

type ScaleOption implements IScaleOption {
  id: ID!
  value: Int!
  label: String!
}

type DetailedScaleOption implements IScaleOption {
  id: ID!
  value: Int!
  responsesNumber: Int!
  label: String!
}

type TextOption implements ITextOption {
  id: ID!
  value: String!
}

type DetailedTextOption implements ITextOption {
  id: ID!
  value: String!
  responsesNumber: Int!
}

type RawTextAnswer {
  text: String!
  responder: Responder
}

type AnswerStats {
  id: ID!
  questionTitle: String!
  totalResponsesNumber: Int!
  singleOptions: [DetailedTextOption!]
  multipleOptions: [DetailedTextOption!]
  scaleOptions: [DetailedScaleOption!]
  rawTextAnswers: [RawTextAnswer!]
}

type SurveyQuestion {
  id: ID!
  title: String!
  required: Boolean!
  type: SurveyQuestionType!
  singleOptions: [TextOption!]
  multipleOptions: [TextOption!]
  scaleOptions: [ScaleOption!]
}

type SurveyTemplateQuestion {
  id: ID!
  title: String!
  required: Boolean!
  type: SurveyQuestionType!
  singleOptions: [TextOption!]
  multipleOptions: [TextOption!]
  scaleOptions: [ScaleOption!]
}

enum SurveyQuestionType {
  Text
  Scale
  SingleOption
  MultipleOptions
}

type SurveyAnswer {
  id: ID!
  question: SurveyQuestion!
  responder: Responder
  rawTextValue: String
  scaleValue: ScaleOption
  singleValue: TextOption
  multipleValue: [TextOption!]
}

type Responder {
  id: ID!
  realName: String
  photo72Url: String
  title: String
}

input SurveyInput {
  surveyId: String
  responderId: String
  surveyStatusFilter: SurveyStatusFilter
}

input NewScaleOptionInput {
  value: Int!,
  label: String!
}

input NewQuestionInput {
  title: String!
  required: Boolean!
  type: SurveyQuestionType!
  scaleOptions: [NewScaleOptionInput!]
  singleOptions: [String!]
  multipleOptions: [String!]
}

input RunSurveyInput {
  surveyTitle: String!
  templateId: String
  channelId: String!
  isAnonymous: Boolean!
  questions: [NewQuestionInput!]!
}

input NewTemplateInput {
  title: String
  questions: [NewQuestionInput!]!
}

input TextOptionInput {
  id: ID
  value: String!
}

input ScaleOptionInput {
  id: ID
  value: Int!
  label: String!
}

input EditQuestionInput {
  id: ID
  title: String!
  required: Boolean!
  type: SurveyQuestionType!
  singleOptions: [TextOptionInput!]
  multipleOptions: [TextOptionInput!]
  scaleOptions: [ScaleOptionInput!]
}

input EditTemplateInput {
  id: ID!
  title: String!
  questions: [EditQuestionInput!]!
}

extend type Query {
  surveyTemplates: [SurveyTemplate!]!
  surveys(survey: SurveyInput): [Survey!]!
  answersStats(surveyId: String!): [AnswerStats!]!
}

extend type Mutation {
  createSurveyTemplate(template: NewTemplateInput!): SurveyTemplate!
  editSurveyTemplate(template: EditTemplateInput!): SurveyTemplate!
  copySurveyTemplate(templateId: String!): SurveyTemplate!
  deleteSurveyTemplate(templateId: String!): Void
  openSurveysInSlackApp: Void
  toggleSurveyStatus(surveyId: String!): Survey!
  runSurvey(survey: RunSurveyInput!): Survey!
  deleteSurvey(surveyId: String!): Void
}
