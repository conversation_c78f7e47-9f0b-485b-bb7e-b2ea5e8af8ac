type PositionReference {
  bonusDescription: String
  jobDescriptionLink: String
  isIncludeToReferrals: Boolean
  hiringManagerId: String
  candidates: [PositionCandidate]
}

type Position {
  id: ID!
  type: String!
  parentId: ID!
  title: String
  member: Member
  teams: [Team!]!
  department: Department
  subordinates: [Subordinate!]
  subordinatesNumber: Int!
  assignedPositionNumber: Int!
  allPositionNumber: Int!
  timestamp: Float!
  withoutManagerManual: Boolean!
  editable: Boolean!
  assignable: Boolean!
  reference: PositionReference
  managedDepartmentId: ID
}

type PositionsWithoutTeam {
  positions: [Position!]!
  allPositionNumber: Int!
  assignedPositionNumber: Int!
}

type NodeParentInfo {
  parentPosition: Position
  parentDepartment: Department
  departments: [Department!]
  members: [Member!]
  openPositions: [Position!]
}

type Department {
  id: ID!
  type: String!
  editable: Boolean!
  parentId: ID!
  managerId: ID
  title: String
  departmentColor: Color!
  subordinates: [Subordinate!]
  subordinatesNumber: Int!
  assignedPositionNumber: Int!
  allPositionNumber: Int!
  timestamp: Float!
}

union Subordinate = Department | Position

type RootNode {
  id: ID!
  title: String!
  logoUrl: String!
  editable: Boolean!
  type: String!
  subordinates: [Subordinate!]!
  subordinatesNumber: Int!
  assignedPositionNumber: Int!
  allPositionNumber: Int!
}

type Tree {
  rootNode: RootNode!
  editable: Boolean!
}

type DepartmentManagerInfo {
  members: [Member!]
  positions: [Position!]
}

union OrgTreeNode = Department | Position | RootNode

extend type Query {
  expandedNode(id: ID!): Subordinate!
  departments: [Department!]!
  department(id: ID!): Department!
  departmentManagerInfo(parentId: ID): DepartmentManagerInfo
  positionParentInfo(positionId: ID): NodeParentInfo!
  departmentParentInfo(departmentId: ID): NodeParentInfo!
  positions: [Position!]!
  positionsWithoutTeam: PositionsWithoutTeam!

  # NOTE: this is a cache only query, we define it here only to get
  # codegen to generate the types for it
  allKnownNodes: [OrgTreeNode!]!
  allNodes(nodesIds: [String!]!, withDepartments: Boolean): [OrgTreeNode!]!
  orgTree: Tree!
  openNodes: [OrgTreeNode!]!
}

input AddDepartmentInput {
  title: String!
  color: Color!
  parentId: ID
  manager: PositionOrMember
}

input MoveDepartmentInput {
  parentId: ID
  departmentId: ID!
  nextItemIndex: Int
}

input EditDepartmentInput {
  id: ID!
  title: String!
  color: Color!
  parentId: ID
  manager: PositionOrMember
}

input AddPositionInput {
  parentId: ID!
  title: String!
  memberId: String
}

input EditPositionInput {
  id: ID
  title: String
  memberId: String
  teamIds: [String!]
  isIncludeToReferrals: Boolean
  bonusDescription: String
  jobDescriptionLink: String
  hiringManagerId: String
  unassign: Boolean
}

input PositionOrMember {
  positionId: ID
  memberId: ID
}

type AddOrEditDepartmentResultAffected {
  nodes: [OrgTreeNode!]
}

type AddOrEditDepartmentResult {
  department: Department!
  affected: AddOrEditDepartmentResultAffected!
}

type AddPositionResultAffected {
  parentNode: OrgTreeNode!
}

type AddPositionResult {
  position: Position!
  affected: AddPositionResultAffected!
}

type MoveNodeAffected {
  nodes: [OrgTreeNode!]
  pathToRoot: [Subordinate!]!
}

input NodeOrMember {
  nodeId: ID
  memberId: ID
  withoutManagerManual: Boolean
}

input MovePositionInput {
  position: PositionOrMember!
  parent: NodeOrMember
  nextItemIndex: Int
}

type MovePosition {
  position: Position!
  affected: MoveNodeAffected!
}

type MoveDepartment {
  department: Department!
  affected: MoveNodeAffected!
}

type EditPositionAffected {
  prevPosition: Position
  teams: Teams
}

type EditPosition {
  position: Position!
  affected: EditPositionAffected!
}

input ChartLayoutInput {
  clusterHorizontal: Boolean!
  dataField: String
}

extend type Mutation {
  updateChartLayout(layout: ChartLayoutInput!): Workspace!
  removeTreeNode(id: ID!): OrgTreeNode!
  editPosition(position: EditPositionInput!): EditPosition!
  addPosition(position: AddPositionInput!): AddPositionResult!
  addDepartment(department: AddDepartmentInput!): AddOrEditDepartmentResult!
  editDepartment(department: EditDepartmentInput!): AddOrEditDepartmentResult!
  movePosition(data: MovePositionInput!): MovePosition!
  moveDepartment(department: MoveDepartmentInput!): MoveDepartment!
}
