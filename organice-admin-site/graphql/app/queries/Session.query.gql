# this query should return all Session and Usage information
# so the other parts of the system can consume it without making addiotnal
# queries

query Session {
  myWorkspace {
    id
    url
    name
    editable
    clusterHorizontal
    dataField {
      id
      label
      type
    }
    appId
    installedAt
  }
  onboarding {
    completed
    bookmarkedFeatures
  }
  me {
    id
    isAdmin
    photoUrl
    photo512Url
    photo72Url
    positionId
    intercomHash
    name
    realName
    title
    phone
    email
    country
    shouldReceiveIntercomPricingMessage
  }
  billing {
    ...SessionBilling
  }

  # semantically this is not a part of the session, but as we want to make
  # less server requests we put it here
  referralSettings {
    id
    isEnable
  }
  stats {
    id
    totalMembers
    totalPositionsInProgress
    totalPositionsOnboarded
    totalPositions
    totalMembersWithPosition
    totalNumberDepartments
    totalNumberTeams
    totalNumberCountries
  }
  celebrationSettings {
    id
    birthdaysEnabled
    anniversaryEnabled
  }
  timeOffs {
    id
    isEnable
  }
  links {
    id
    title
  }
}

fragment SessionBilling on Billing {
  id
  subscription {
    ... on TrialSubscription {
      ok
      daysLeft
    }
    ... on StripeSubscription {
      ok
      plan
    }
    ... on AppSumoSubscription {
      ok
      expiresSoon
      daysLeft
      endsAt
    }
  }
}
