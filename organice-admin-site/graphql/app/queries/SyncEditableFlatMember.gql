# NOTE: all pages/components that use these update mutations
# should use the same fragment to update the cache

fragment UpdatableMemberFields on Member {
  id
  position {
    id
  }
  fields {
    id
    key
    ... on LinkField {
      link
    }
    ... on UserField {
      member {
        id
        fields {
          id
          key
          ... on TextField {
            text
          }
          ... on PhotoField {
            url72
          }
        }
      }
    }
    ... on TextField {
      text
    }
    ... on DateField {
      date
    }
    ... on TeamsField {
      teams {
        ...TeamFields
      }
    }
    ... on DepartmentField {
      department {
        id
        title
        departmentColor
        managerId
      }
    }
    ... on ManagerField {
      position {
        id
        title
        member {
          id
          fields {
            id
            key
            ... on TextField {
              text
            }
            ... on PhotoField {
              url72
            }
          }
        }
      }
      withoutManagerManual
    }
  }
  hideBirthday
  nonFilledFields {
    id
    label
  }
}

# NOTE: use this fragment for all member selects components
fragment MemberShortInfoComponent on Member {
  id
  fields {
    id
    key
    ... on TextField {
      text
    }
    ... on PhotoField {
      url72
    }
    ... on DepartmentField {
      department {
        managerId
      }
    }
  }
}

mutation UpdateMemberPosition($position: EditPositionInput!) {
  editPosition(position: $position) {
    position {
      id
      # NOTE: we request teams so the cache gets updateed the components
      # that use postion,teams get updated
      teams {
        ...TeamFields
      }
      member {
        ...UpdatableMemberFields
      }
    }
    affected {
      teams {
        teams {
          ...TeamTreeData
        }
      }
    }
  }
}

mutation UpdateMemberProfile($profile: MemberProfileInput!) {
  updateMemberProfile(profile: $profile) {
    ...UpdatableMemberFields
  }
}
