query CalendarSidebar(
  $memberId: ID!
  $from: Date!
  $to: Date!
  $eventTypeIds: [ID!]
  $types: [String!]
) {
  timeOffTypes {
    id
    color
    label
    emoji
  }
  celebrationTypes {
    id
    color
    label
    emoji
  }
  holidayTypes {
    id
    color
    label
    emoji
  }
  memberEvents(
    id: $memberId
    from: $from
    to: $to
    eventTypeIds: $eventTypeIds
  ) {
    ... on TimeOffEvent {
      type {
        id
      }
      id
      endDate
      startDate
      status
      duration
      rejectReason
    }
    ... on CelebrationEvent {
      type {
        id
      }
      id
      endDate
      startDate
    }
    ... on HolidayEvent {
      type {
        id
      }
      id
      endDate
      startDate
      title
      isOfficial
      duration
    }
  }

  timeOffPolicies {
    id
    title
    isDefault
    workDays
    includedWeekendDays
    typePolicies {
      type {
        id
      }
      onStartQuota
      rollOverToNextYear
      yearStart
      accrualsQuota
      accuralsFrequency
      nextAccruals
      maxCapacity
    }
  }

  member(id: $memberId) {
    id
    realName
    displayName
    photo512Url
    title
    timeOffTypePolicy {
      id
      title
      workDays
      includedWeekendDays
      typePolicies {
        type {
          id
        }
        onStartQuota
        rollOverToNextYear
        yearStart
        accrualsQuota
        accuralsFrequency
        nextAccruals
        maxCapacity
      }
    }
    timeOffs {
      typeId
      balance
      nextResetAt
    }
  }

  logs(from: $from, to: $to, memberId: $memberId, types: $types) {
    ... on TimeOffLog {
      id
      timeOffLogType: type
      createdAt
      data {
        initiator {
          id
          realName
          displayName
        }
        request {
          id
          type {
            id
          }
          startDate
          endDate
        }
      }
    }
    ... on BalanceLog {
      id
      balanceLogType: type
      createdAt
      data {
        initiator {
          id
          realName
        }
        absolute
        increase
        type {
          id
        }
      }
    }
    ... on SlackStatusLog {
      id
      slackStatusLogType: type
      createdAt
      data {
        initiator {
          id
          realName
          displayName
        }
        previousStatus
        newStatus
      }
    }
  }
}
