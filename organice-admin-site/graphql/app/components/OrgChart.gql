fragment RootNodeFields on RootNode {
  id
  type
  rootTitle: title
  logoUrl
  editable
  rootSubordinates: subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
  subordinatesNumber
  assignedPositionNumber
  allPositionNumber
}

fragment PosFields on Position {
  id
  timestamp
  type
  parentId
  title
  reference {
    isIncludeToReferrals
    bonusDescription
    jobDescriptionLink
    hiringManagerId
  }
  managedDepartmentId
  department {
    id
    title
    departmentColor
  }
  teams {
    ...TeamFields
  }
  member {
    id
    nonFilledFields {
      id
      label
    }
    hideBirthday
    fields {
      id
      key
      ... on TextField {
        text
      }
      ... on PhotoField {
        url72
      }
      ... on LinkField {
        link
      }
      ... on UserField {
        member {
          id
          fields {
            id
            key
            ... on TextField {
              text
            }
            ... on PhotoField {
              url72
            }
          }
        }
      }
      ... on DateField {
        date
      }
    }
  }
  withoutManagerManual
  subordinatesNumber
  assignedPositionNumber
  allPositionNumber
  editable
  assignable
  subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
}

fragment DepFields on Department {
  id
  timestamp
  type
  editable
  parentId
  managerId
  title
  departmentColor
  assignedPositionNumber
  allPositionNumber
  subordinatesNumber
  subordinates {
    ... on Position {
      id
    }
    ... on Department {
      id
    }
  }
}

query AllNodes($nodesIds: [String!]!, $withDepartments: Boolean) {
  allNodes(nodesIds: $nodesIds, withDepartments: $withDepartments) {
    ... on Position {
      ...PosFields
    }
    ... on Department {
      ...DepFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
  policy {
    id
    fields {
      id
      label
      type
    }
  }
}

query AllKnownNodes {
  allKnownNodes @client {
    ... on Position {
      ...PosFields
    }
    ... on Department {
      ...DepFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

mutation RemoveTreeNode($id: ID!) {
  removeTreeNode(id: $id) {
    ... on Position {
      ...PosFields
    }
    ... on Department {
      ...DepFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

mutation AddDepartment($department: AddDepartmentInput!) {
  addDepartment(department: $department) {
    department {
      ...DepFields
    }
    affected {
      nodes {
        ... on Position {
          ...PosFields
        }
        ... on Department {
          ...DepFields
        }
        ... on RootNode {
          ...RootNodeFields
        }
      }
    }
  }
}

mutation AddPosition($position: AddPositionInput!) {
  addPosition(position: $position) {
    position {
      ...PosFields
    }
    affected {
      parentNode {
        ... on Position {
          ...PosFields
        }
        ... on Department {
          ...DepFields
        }
        ... on RootNode {
          ...RootNodeFields
        }
      }
    }
  }
}

mutation MoveDepartment($department: MoveDepartmentInput!) {
  moveDepartment(department: $department) {
    department {
      ...DepFields
    }
    affected {
      nodes {
        ... on Department {
          ...DepFields
        }
        ... on Position {
          ...PosFields
        }
        ... on RootNode {
          ...RootNodeFields
        }
      }
      pathToRoot {
        ... on Position {
          id
        }
        ... on Department {
          id
        }
      }
    }
  }
}

mutation MovePosition($data: MovePositionInput!) {
  movePosition(data: $data) {
    position {
      ...PosFields
      member {
        fields {
          ... on ManagerField {
            position {
              id
              title
              member {
                id
                fields {
                  id
                  key
                  ... on TextField {
                    text
                  }
                  ... on PhotoField {
                    url72
                  }
                }
              }
            }
            withoutManagerManual
          }
        }
      }
    }
    affected {
      nodes {
        ... on Department {
          ...DepFields
        }
        ... on Position {
          ...PosFields
        }
        ... on RootNode {
          ...RootNodeFields
        }
      }
      pathToRoot {
        ... on Position {
          id
        }
        ... on Department {
          id
        }
      }
    }
  }
}

query ExpandedNode($id: ID!) {
  expandedNode(id: $id) {
    ... on Position {
      ...PosFields
      subordinates {
        ... on Position {
          ...PosFields
        }
        ... on Department {
          ...DepFields
        }
      }
    }
    ... on Department {
      ...DepFields
      subordinates {
        ... on Position {
          ...PosFields
        }
        ... on Department {
          ...DepFields
        }
      }
    }
  }
}

mutation UpdateChartLayout($layout: ChartLayoutInput!) {
  updateChartLayout(layout: $layout) {
    id
    clusterHorizontal
    dataField {
      id
      label
      type
    }
  }
}
