import assert from "assert";
import crypto from "crypto";

import {
  Department as DomainDepartment,
  Position as DomainPosition,
  RootNode as DomainRootNode,
  Team as DomainTeam,
  ReadonlyDeep,
  Workspace as DomainWorkspace,
} from "@organice/core/domain";
import {
  getNode,
  createPosition as createPositionDomain,
  unlinkTeamManager,
  linkTeamManager,
} from "@organice/core/domain/org-chart";
import { difference, uniq } from "lodash";

import { getSessionWorkspace } from "../../../domain";
import {
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import { Color } from "../../server.generated";

import {
  Context,
  getTeams,
  formatPosition,
  formatRootNode,
  formatWorkspace,
  formatTeam,
} from "./_common";

export const Query = {
  teams(_parent, _args, { repository, session }) {
    return getTeams(repository, session);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async addTeam(_parent, { team: teamInput }, { repository, session }) {
    const affectedNodes: ReadonlyDeep<DomainRootNode | DomainPosition>[] = [];
    let workspace = await getSessionWorkspace(repository, session);

    const { teams } = workspace;

    const teamExists = teams.some(
      ({ label }) => label.toLowerCase() === teamInput.label.toLowerCase()
    );

    assert(!teamExists, "Team with current name already exists");
    let color = teamInput.color;

    if (!color) {
      const paletteColors = sortedColors;
      const alreadyUsedColors = uniq(workspace.teams.map((t) => t.color));
      const freeColors = difference(paletteColors, alreadyUsedColors);
      const newColor =
        freeColors.length > 0
          ? freeColors[0]
          : paletteColors[Math.floor(Math.random() * paletteColors.length)];

      assert(newColor, "Color is not found");
      color = newColor;
    }

    let newTeam: DomainTeam = {
      id: crypto.randomBytes(10).toString("hex"),
      label: teamInput.label,
      managerId: null,
      color,
    };

    workspace = {
      ...workspace,
      teams: [...workspace.teams, newTeam],
    };

    const teamWithUpdatedManager = editTeamManager(
      workspace,
      newTeam,
      teamInput.manager
        ? {
            memberId: teamInput.manager.memberId,
            positionId: teamInput.manager.positionId,
          }
        : null
    );

    workspace = teamWithUpdatedManager.workspace;
    newTeam = teamWithUpdatedManager.team;

    if (teamWithUpdatedManager.manager) {
      affectedNodes.push(teamWithUpdatedManager.manager);
    }

    if (teamWithUpdatedManager.affectedNodes.length) {
      affectedNodes.push(...teamWithUpdatedManager.affectedNodes);
    }

    await repository.setWorkspace(workspace);

    return {
      team: {
        ...newTeam,
        positions: teamWithUpdatedManager.manager
          ? [
              formatPosition(
                workspace,
                session,
                teamWithUpdatedManager.manager,
                true
              ),
            ]
          : [],
        allPositionNumber: 0,
        assignedPositionNumber: 0,
      },
      affected: {
        nodes: affectedNodes.map((n) => {
          if (n.type === "root") {
            return formatRootNode(n, workspace, session, true);
          }

          return formatPosition(workspace, session, n, true);
        }),
      },
    };
  },

  async removeTeam(_parent, { id: teamId }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const {
      teams,
      orgTree: { rootNode },
    } = workspace;

    const modifiedTeams = removeTeam(teams, teamId);
    const tree = removeTeamFromTree(rootNode, teamId);

    assert(teamId, "Team not found");

    workspace = {
      ...workspace,
      orgTree: {
        rootNode: tree,
      },
      teams: modifiedTeams,
    };

    await repository.setWorkspace(workspace);

    return formatWorkspace(workspace, session);
  },

  async editTeam(_parent, { team }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);
    const affectedNodes: ReadonlyDeep<DomainRootNode | DomainPosition>[] = [];

    const { teams } = workspace;
    const isExistingTeam = teams.some(
      ({ label, id }) => label === team.label && team.id !== id
    );

    assert(!isExistingTeam, "Team with current name already exists");

    const currentTeam = teams.find((t) => t.id === team.id);

    assert(currentTeam, "Expected team to exist");

    let updatedTeam: DomainTeam = {
      ...currentTeam,
      label: team.label,
      color: team.color,
    };

    workspace = {
      ...workspace,
      teams: workspace.teams.map((t) => {
        if (t.id === updatedTeam.id) {
          return updatedTeam;
        }

        return t;
      }),
    };

    const teamWithUpdatedManager = editTeamManager(
      workspace,
      updatedTeam,
      team.manager
        ? {
            memberId: team.manager.memberId,
            positionId: team.manager.positionId,
          }
        : null
    );

    workspace = teamWithUpdatedManager.workspace;
    updatedTeam = teamWithUpdatedManager.team;

    if (teamWithUpdatedManager.manager) {
      affectedNodes.push(teamWithUpdatedManager.manager);
    }

    if (teamWithUpdatedManager.affectedNodes.length) {
      affectedNodes.push(...teamWithUpdatedManager.affectedNodes);
    }

    await repository.setWorkspace(workspace);

    return {
      team: formatTeam(workspace, session, updatedTeam, true),
      affected: {
        nodes: affectedNodes.map((n) => {
          if (n.type === "root") {
            return formatRootNode(n, workspace, session, true);
          }

          return formatPosition(workspace, session, n, true);
        }),
      },
    };
  },
} satisfies MutationResolvers<Context>;

const sortedColors = [
  Color.Blue,
  Color.Violet,
  Color.Sky,
  Color.Cyan,
  Color.Teal,
  Color.Emerald,
  Color.Green,
  Color.Lime,
  Color.Yellow,
  Color.Amber,
  Color.Orange,
  Color.Red,
  Color.Rose,
  Color.Fuchsia,
];

function editTeamManager(
  workspace: ReadonlyDeep<DomainWorkspace>,
  team: ReadonlyDeep<DomainTeam>,
  manager: {
    memberId?: string | null;
    positionId?: string | null;
  } | null
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  team: ReadonlyDeep<DomainTeam>;
  manager: ReadonlyDeep<DomainPosition> | null;
  affectedNodes: ReadonlyDeep<DomainRootNode | DomainPosition>[];
} {
  if (!manager && !team.managerId) {
    return {
      workspace,
      team,
      manager: null,
      affectedNodes: [],
    };
  }

  let managerNode: ReadonlyDeep<DomainPosition> | null = null;
  const affectedNodes: ReadonlyDeep<DomainRootNode | DomainPosition>[] = [];

  if (manager?.positionId) {
    managerNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.id === manager.positionId
    );

    assert(managerNode, "Expected manager to exist");

    /**
     * NOTE: do not reset the same manager
     */
    if (manager.positionId === team.managerId) {
      return {
        workspace,
        team,
        manager: null,
        affectedNodes: [],
      };
    }
  } else if (manager?.memberId) {
    const createdPosition = createPositionDomain(workspace, manager.memberId);

    managerNode = createdPosition[1];
    workspace = createdPosition[0];

    affectedNodes.push(workspace.orgTree.rootNode);
  }

  const unlinkedTeam = unlinkTeamManager(workspace, team);

  workspace = unlinkedTeam.workspace;
  team = unlinkedTeam.team;

  if (unlinkedTeam.prevManager) {
    affectedNodes.push(unlinkedTeam.prevManager);
  }

  if (managerNode) {
    const linked = linkTeamManager(workspace, team, managerNode);

    workspace = linked.workspace;
    team = linked.team;
    managerNode = linked.manager;
  }

  return {
    workspace,
    team,
    manager: managerNode,
    affectedNodes,
  };
}

function removeTeam(
  teams: ReadonlyDeep<DomainTeam[]>,
  teamId: string
): ReadonlyDeep<DomainTeam[]> {
  return teams.filter(({ id }) => id !== teamId);
}

function removeTeamFromTree<
  T extends ReadonlyDeep<DomainDepartment | DomainPosition | DomainRootNode>
>(root: T, teamId: string): T {
  if (root.type === "position") {
    return {
      ...root,
      teamIds: root.teamIds.filter((id: string) => id !== teamId),
      subordinates: root.subordinates.map((s) => removeTeamFromTree(s, teamId)),
    };
  }

  if (root.type === "department") {
    return {
      ...root,
      subordinates: root.subordinates.map((s) => removeTeamFromTree(s, teamId)),
    };
  }

  return {
    ...root,
    subordinates: root.subordinates.map((s) => removeTeamFromTree(s, teamId)),
  };
}
