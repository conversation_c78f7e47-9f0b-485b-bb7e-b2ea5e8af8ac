/* eslint-disable max-classes-per-file */
import assert from "assert";
import crypto from "crypto";

import {
  Department as DomainDepartment,
  Position as DomainPosition,
  RootNode as DomainRootNode,
  Member as DomainMember,
  ReadonlyDeep,
  Workspace as DomainWorkspace,
  Logger,
  formatCustomFieldsForSlackUpdate,
  replaceMember,
  findTokenForSlackAdapterMemberUpdate,
} from "@organice/core/domain";
import {
  replaceNode,
  getNode,
  searchNodeWithPathOfNodes,
  changeNodeOrder,
  moveNode as moveNodeDomain,
  createPosition as createPositionDomain,
  markMemberAsNotHavingManager,
  resetMemberAsNotHavingManager,
  unlinkDepartmentManager,
  unlinkManagerPrevDepartment,
  moveNode,
  linkDeparmentManager,
  moveMemberToManager,
  moveMemberToDepartment,
  getParentDepartment,
  getParentPosition,
  getNodes,
  getAllDepartments,
  getPossibleParents,
} from "@organice/core/domain/org-chart";
import { includes, sortBy, uniqBy } from "lodash";

import { Session, getSessionWorkspace } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  type SubordinateResolvers,
  type OrgTreeNodeResolvers,
  type PositionOrRootNodeResolvers,
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import {
  MovePositionInput,
  RootNode,
  Position,
  Department,
  Color,
  PositionOrMember,
  NodeOrMember,
  Member,
  Tree,
} from "../../server.generated";

import {
  Context,
  formatWorkspace,
  formatMember,
  getMembers,
  canEditWorkspace,
  getTeams,
  formatPosition,
  formatDepartment,
  formatRootNode,
} from "./_common";

export const Subordinate: SubordinateResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === "position") {
      return "Position";
    }

    if (obj.type === "department") {
      return "Department";
    }

    return null;
  },
};

export const OrgTreeNode: OrgTreeNodeResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === "position") {
      return "Position";
    }

    if (obj.type === "department") {
      return "Department";
    }

    return "RootNode";
  },
};

export const PositionOrRootNode: PositionOrRootNodeResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === "position") {
      return "Position";
    }

    if (obj.type === "root") {
      return "RootNode";
    }

    return null;
  },
};

export const Query = {
  async expandedNode(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const expandedNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment | DomainPosition =>
        n.id === id && n.type !== "root"
    );

    assert(expandedNode, "Expanded node not found");

    return expandedNode.type === "position"
      ? formatPosition(workspace, session, expandedNode, true)
      : formatDepartment(workspace, session, expandedNode, true);
  },

  async departments(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const departments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment => n.type === "department"
    );

    const formattedDepartments = departments.map((dep) =>
      formatDepartment(workspace, session, dep, true)
    );

    return formattedDepartments;
  },

  async department(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const departments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment => n.type === "department"
    ).map((dep) => formatDepartment(workspace, session, dep, true));

    const department = departments.find((dep) => dep.id === id);

    assert(department, `Expected department with id ${id} to exist`);

    return department;
  },

  async departmentManagerInfo(_parent, { parentId }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    if (parentId) {
      const { path } = searchNodeWithPathOfNodes(
        workspace.orgTree.rootNode,
        parentId
      );
      const pathWithParent = [...(path?.map((n) => n.id) ?? []), parentId];

      const positions = getNodes(
        workspace.orgTree.rootNode,
        (n): n is DomainPosition => n.type === "position"
      );

      let members: ReadonlyDeep<Member>[] = [];
      const openPositions: ReadonlyDeep<Position>[] = [];

      positions.forEach((p) => {
        if (!includes(pathWithParent, p.id)) {
          if (p.memberId) {
            const member = workspace.members.find((m) => m.id === p.memberId);

            assert(member, "Expect member to exist");

            members.push({
              ...formatMember({
                workspace,
                session,
                member,
              }),
              position: formatPosition(workspace, session, p),
            });
          } else {
            openPositions.push(formatPosition(workspace, session, p));
          }
        }
      });

      workspace.members.forEach((member) => {
        if (!positions.some((p) => p.memberId === member.id)) {
          members.push({
            ...formatMember({
              workspace,
              session,
              member,
            }),
            position: null,
          });
        }
      });

      members = sortBy(members, (member) => member.realName?.toLowerCase());

      return {
        members,
        positions: openPositions,
      };
    }

    return {
      positions: getOpenPositions(workspace, session),
      members: await getMembers(repository, session),
    };
  },

  async positions(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const positions = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition => n.type === "position"
    );

    const formattedPositions = positions.map((pos) =>
      formatPosition(workspace, session, pos, true)
    );

    return formattedPositions;
  },

  async positionParentInfo(_parent, { positionId }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    if (!positionId) {
      const members = await getMembers(repository, session);
      const departments = getAllDepartments(workspace).map((d) =>
        formatDepartment(workspace, session, d)
      );
      const openPositions = getOpenPositions(workspace, session);

      return {
        parentPosition: null,
        parentDepartment: null,
        members,
        openPositions,
        departments,
      };
    }

    const node = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition | DomainDepartment => n.id === positionId
    );

    assert(node, "Position not found");

    const possibleParents = getPossibleParents(workspace, positionId);
    const parentDepartment = getParentDepartment(workspace, positionId);
    const parentPosition = getParentPosition(workspace, positionId);

    const members = [
      ...possibleParents.possibleMembers.map((m) =>
        formatMember({
          workspace,
          session,
          member: m,
        })
      ),
      ...possibleParents.possiblePositions.map((p) => {
        const member = workspace.members.find((m) => m.id === p.memberId);

        assert(member, "Expected member to exist");

        return {
          ...formatMember({
            workspace,
            session,
            member,
          }),
          position: formatPosition(workspace, session, p),
        };
      }),
    ];

    const openPositions = possibleParents.possibleOpenPositions.map((p) =>
      formatPosition(workspace, session, p)
    );
    const departments = possibleParents.possibleDepartments.map((d) =>
      formatDepartment(workspace, session, d)
    );

    return {
      parentPosition: parentPosition
        ? formatPosition(workspace, session, parentPosition)
        : null,
      parentDepartment: parentDepartment
        ? formatDepartment(workspace, session, parentDepartment)
        : null,
      members,
      openPositions,
      departments,
    };
  },

  /**
   * NOTE: we don't return members w/o positions here
   * as we allow setting only position as a department's parent
   */
  async departmentParentInfo(
    _parent,
    { departmentId },
    { repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    if (!departmentId) {
      const members = (await getMembers(repository, session)).filter(
        (m) => !!m.position
      );
      const departments = getAllDepartments(workspace).map((d) =>
        formatDepartment(workspace, session, d)
      );
      const openPositions = getOpenPositions(workspace, session);

      return {
        parentPosition: null,
        parentDepartment: null,
        members,
        openPositions,
        departments,
      };
    }
    const node = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition | DomainDepartment => n.id === departmentId
    );

    assert(node, "Department not found");

    const possibleParents = getPossibleParents(workspace, departmentId);
    const parentDepartment = getParentDepartment(workspace, departmentId);
    const parentPosition = getParentPosition(workspace, departmentId);

    const members = possibleParents.possiblePositions.map((p) => {
      const member = workspace.members.find((m) => m.id === p.memberId);

      assert(member, "Expected member to exist");

      return {
        ...formatMember({
          workspace,
          session,
          member,
        }),
        position: formatPosition(workspace, session, p),
      };
    });

    const openPositions = possibleParents.possibleOpenPositions.map((p) =>
      formatPosition(workspace, session, p)
    );
    const departments = possibleParents.possibleDepartments.map((d) =>
      formatDepartment(workspace, session, d)
    );

    return {
      parentPosition: parentPosition
        ? formatPosition(workspace, session, parentPosition)
        : null,
      parentDepartment: parentDepartment
        ? formatDepartment(workspace, session, parentDepartment)
        : null,
      members,
      openPositions,
      departments,
    };
  },

  async positionsWithoutTeam(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const positions = formatPositionsWithoutTeam(workspace, session);

    const j = positions.reduce((acc, position) => {
      if (position.member) {
        acc += 1;
      }

      return acc;
    }, 0);

    return {
      positions,
      allPositionNumber: positions.length,
      assignedPositionNumber: j,
    };
  },

  async allNodes(
    _parent,
    { nodesIds, withDepartments },
    { repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    let nodes = [
      ...workspace.orgTree.rootNode.subordinates,
      workspace.orgTree.rootNode,
    ];

    if (nodesIds.length) {
      const touchedNodes = getTouchedNodes(
        workspace.orgTree.rootNode,
        nodesIds,
        true
      );

      nodes = [...nodes, ...touchedNodes];
    }

    if (withDepartments) {
      const departments = getNodes(
        workspace.orgTree.rootNode,
        (n): n is DomainDepartment => n.type === "department"
      );

      const parents = departments.map((d) =>
        getNode(
          workspace.orgTree.rootNode,
          (n): n is DomainDepartment | DomainPosition | DomainRootNode =>
            n.id === d.parentId
        )
      );

      const parentsSubs = parents.flatMap((n) => n?.subordinates ?? []);

      const subs = departments.flatMap((sub) => sub.subordinates);

      nodes = [...nodes, ...parentsSubs, ...subs];
    }

    const uniqNodes = uniqBy(nodes, "id");

    return uniqNodes.map((node) =>
      node.type === "position"
        ? formatPosition(workspace, session, node, true)
        : node.type === "department"
        ? formatDepartment(workspace, session, node, true)
        : formatRootNode(workspace.orgTree.rootNode, workspace, session, true)
    );
  },

  async orgTree(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatOrgTree(workspace, session);
  },

  async openNodes(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const openPositionNodes = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition => n.type === "position" && n.memberId === null
    );

    const nodes = [
      ...openPositionNodes,
      ...openPositionNodes.map((n) => {
        return getNode(
          workspace.orgTree.rootNode,
          (node): node is DomainPosition | DomainDepartment | DomainRootNode =>
            node.id === n.parentId
        )!;
      }),
    ];

    const uniqNodes = uniqBy(nodes, "id");

    return uniqNodes.map((node) =>
      node.type === "position"
        ? formatPosition(workspace, session, node, true)
        : node.type === "department"
        ? formatDepartment(workspace, session, node, true)
        : formatRootNode(workspace.orgTree.rootNode, workspace, session, true)
    );
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async updateChartLayout(
    _parent,
    { layout },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      chartLayout: {
        clusterHorizontal: layout.clusterHorizontal,
        dataField: layout.dataField ?? "",
      },
    };

    workspace = await repository.setWorkspace(workspace);

    logger.info("Updated chart layout settings");

    return formatWorkspace(workspace, session);
  },

  async removeTreeNode(_parent, { id: nodeId }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    let rootNode = workspace.orgTree.rootNode;

    const node = getNode(
      rootNode,
      (n): n is DomainPosition | DomainDepartment =>
        n.id === nodeId && n.type !== "root"
    );

    assert(node, "Node node not found");

    const removedPositions = getNodes(
      node,
      (n): n is DomainPosition => n.type === "position"
    );

    let modifiedTeams = workspace.teams;

    for (const removedPosition of removedPositions) {
      if (removedPosition.memberId) {
        let member = workspace.members.find(
          (m) => m.id === removedPosition.memberId
        );

        assert(member, "Member not found");
        member = {
          ...member,
          botState: {
            ...member.botState,
            wasAskedAboutManager: false,
            wasAskedAboutDepartment: false,
          },
        };

        workspace = replaceMember(workspace, member);
      }

      for (const teamId of removedPosition.managerTeamIds) {
        modifiedTeams = modifiedTeams.map((team) =>
          team.id === teamId ? { ...team, managerId: null } : team
        );
      }

      rootNode = replaceNode(rootNode, {
        ...removedPosition,
        managerTeamIds: [],
        managedDepartmentId: null,
      });
    }

    workspace = {
      ...workspace,
      teams: modifiedTeams,
    };

    await repository.setWorkspace(workspace);

    let parentNode = getNode(
      rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === node.parentId
    );

    assert(parentNode, "Parent node not found");

    parentNode = {
      ...parentNode,
      subordinates: parentNode.subordinates.filter((sub) => sub.id !== nodeId),
    };

    rootNode = replaceNode(rootNode, parentNode);

    workspace = {
      ...workspace,
      orgTree: {
        rootNode,
      },
    };

    await repository.setWorkspace(workspace);
    const parent =
      parentNode.type === "root"
        ? formatRootNode(rootNode, workspace, session)
        : parentNode.type === "position"
        ? formatPosition(workspace, session, parentNode, true)
        : formatDepartment(workspace, session, parentNode, true);

    return parent;
  },

  async editPosition(
    _parent,
    { position: editPositionInput },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    let position = editPositionInput;
    let prevPosition: ReadonlyDeep<DomainPosition> | null = null;

    logger = logger.withContext({ workspace, position });

    assert(
      !(position.unassign && position.memberId),
      "Can't unassign and assign a member at the same time"
    );

    if (!position.id) {
      const newPosition: DomainPosition = {
        id: crypto.randomBytes(10).toString("hex"),
        timestamp: Date.now(),
        type: "position",
        title: position.title ?? "",
        memberId: position.memberId!,
        withoutManagerManual: false,
        parentId: workspace.orgTree.rootNode.id,
        teamIds: (position.teamIds ?? []) as string[],
        subordinates: [],
        reference: {
          isIncludeToReferrals: false,
          bonusDescription: "",
          jobDescriptionLink: "",
          candidates: [],
        },
        managedDepartmentId: null,
        managerTeamIds: [],
      };

      position = {
        id: newPosition.id,
        title: newPosition.title,
        memberId: newPosition.memberId!,
        teamIds: newPosition.teamIds,
        bonusDescription: newPosition.reference!.bonusDescription!,
        jobDescriptionLink: newPosition.reference!.jobDescriptionLink!,
        isIncludeToReferrals: newPosition.reference!.isIncludeToReferrals!,
        hiringManagerId: "",
      };

      const rootNode = {
        ...workspace.orgTree.rootNode,
        subordinates: [...workspace.orgTree.rootNode.subordinates, newPosition],
      };

      workspace = {
        ...workspace,
        orgTree: { rootNode },
      };
    }

    let rootNode = workspace.orgTree.rootNode;

    /**
     * NOTE: if a member had a previous position, we need to remove this
     * member from the previous position
     */
    if (position.memberId) {
      prevPosition = getNode(
        rootNode,
        (n): n is DomainPosition =>
          n.type === "position" && n.memberId === position.memberId
      );

      if (prevPosition && prevPosition.id !== position.id) {
        prevPosition = {
          ...prevPosition,
          memberId: null,
        };

        rootNode = replaceNode(rootNode, prevPosition);
      }
    }

    let positionNode = getNode(
      rootNode,
      (n): n is DomainPosition => n.type === "position" && n.id === position.id
    );

    assert(positionNode, "Position node not found");

    if (position.memberId) {
      assert(
        workspace.members.some((member) => member.id === position.memberId),
        `Member "${position.memberId}" does not exist`
      );
    }

    if (position.teamIds) {
      position.teamIds.forEach((teamId) => {
        assert(
          workspace.teams.some((team) => team.id === teamId),
          `Team "${teamId}" does not exist`
        );
      });
    }

    const newPositionTitle = position.title?.trim();

    const {
      bonusDescription = "",
      isIncludeToReferrals = false,
      jobDescriptionLink = "",
      candidates = [],
      hiringManagerId,
    } = positionNode.reference ?? {};

    positionNode = {
      ...positionNode,
      title: newPositionTitle ?? positionNode.title,
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      memberId: position.unassign
        ? null
        : position.memberId ?? positionNode.memberId,
      teamIds: position.teamIds ?? positionNode.teamIds,
      reference: {
        bonusDescription: position.bonusDescription ?? bonusDescription,
        isIncludeToReferrals:
          position.isIncludeToReferrals ?? isIncludeToReferrals,
        jobDescriptionLink: position.jobDescriptionLink ?? jobDescriptionLink,
        hiringManagerId:
          position.hiringManagerId === ""
            ? ""
            : position.hiringManagerId ?? hiringManagerId,
        candidates,
      },
    };

    rootNode = replaceNode(rootNode, positionNode);

    workspace = {
      ...workspace,
      orgTree: { rootNode },
    };

    if (positionNode.memberId) {
      const positionNodeMemberId = positionNode.memberId;
      const positionMember = workspace.members.find(
        (i) => i.id === positionNodeMemberId
      );

      if (workspace.slackIntegration && positionMember != null) {
        const userToken = findTokenForSlackAdapterMemberUpdate(
          workspace,
          positionMember
        );

        if (userToken) {
          assert(workspace.slackBotToken, "Expected Slack bot token to exist");

          const slackAdapter = getSlackAdapter(workspace.slackBotToken);

          void (async () => {
            try {
              await slackAdapter.updateMember(
                workspace.id,
                {
                  memberId: positionMember.id,
                  realName: positionMember.realName,
                  customFields: await formatCustomFieldsForSlackUpdate(
                    workspace,
                    positionMember,
                    slackAdapter
                  ),
                },
                workspace.policy,
                userToken
              );
            } catch (error) {
              logger.error(error);
            }
          })();
        }
      }
    }

    workspace = await repository.setWorkspace(workspace);

    return {
      position: formatPosition(workspace, session, positionNode, true),
      affected: {
        teams: await getTeams(repository, session),
        prevPosition: prevPosition
          ? formatPosition(workspace, session, prevPosition, true)
          : null,
      },
    };
  },

  async addPosition(_parent, { position }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const parentNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === position.parentId
    );

    assert(parentNode, "Parent node not found");

    const newPosition: ReadonlyDeep<DomainPosition> = {
      id: crypto.randomBytes(10).toString("hex"),
      timestamp: Date.now(),
      type: "position",
      title: position.title,
      memberId: position.memberId ?? null,
      withoutManagerManual: false,
      parentId: position.parentId,
      managedDepartmentId: null,
      managerTeamIds: [],
      teamIds: [],
      subordinates: [],
      reference: {
        isIncludeToReferrals: false,
        bonusDescription: "",
        jobDescriptionLink: "",
        candidates: [],
      },
    };

    const parentNodeManager = parentNode.subordinates.find(
      (subordinate) =>
        "managedDepartmentId" in subordinate &&
        subordinate.managedDepartmentId === parentNode.id
    );

    const subordinatesWithoutManager = parentNode.subordinates.filter(
      (subordinate) => subordinate !== parentNodeManager
    );

    const updatedParent = {
      ...parentNode,
      subordinates: [
        ...(parentNodeManager ? [parentNodeManager] : []),
        newPosition,
        ...subordinatesWithoutManager,
      ],
    };

    const rootNode = replaceNode(workspace.orgTree.rootNode, updatedParent);

    workspace = {
      ...workspace,
      orgTree: {
        rootNode,
      },
    };
    await repository.setWorkspace(workspace);

    const parent =
      updatedParent.type === "root"
        ? formatRootNode(rootNode, workspace, session)
        : updatedParent.type === "position"
        ? formatPosition(workspace, session, updatedParent, true)
        : formatDepartment(workspace, session, updatedParent, true);

    return {
      affected: {
        parentNode: parent,
      },
      position: formatPosition(workspace, session, newPosition),
    };
  },

  async addDepartment(_parent, { department }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const parentId = department.parentId ?? workspace.orgTree.rootNode.id;

    let rootNode = workspace.orgTree.rootNode;
    const parentNode = getNode(
      rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === parentId
    );

    const affectedNodes: ReadonlyDeep<
      DomainDepartment | DomainPosition | DomainRootNode
    >[] = [];

    assert(parentNode, "Parent node not found");

    let newDepartment: ReadonlyDeep<DomainDepartment> = {
      id: crypto.randomBytes(10).toString("hex"),
      timestamp: Date.now(),
      type: "department",
      title: department.title,
      parentId,
      managerId: null,
      color: department.color,
      subordinates: [],
    };

    const parentNodeManager = parentNode.subordinates.find(
      (subordinate) =>
        "managedDepartmentId" in subordinate &&
        subordinate.managedDepartmentId === parentNode.id
    );

    const subordinatesWithoutManager = parentNode.subordinates.filter(
      (subordinate) => subordinate !== parentNodeManager
    );

    const updatedParent = {
      ...parentNode,
      subordinates: [
        ...(parentNodeManager ? [parentNodeManager] : []),
        newDepartment,
        ...subordinatesWithoutManager,
      ],
    };

    rootNode = replaceNode(rootNode, updatedParent);

    workspace = {
      ...workspace,
      orgTree: {
        rootNode,
      },
    };

    const demartmentWithUpdatedManager = editDepartmentManager(
      workspace,
      newDepartment,
      department.manager
        ? {
            memberId: department.manager.memberId,
            positionId: department.manager.positionId,
          }
        : null
    );

    workspace = demartmentWithUpdatedManager.workspace;
    newDepartment = demartmentWithUpdatedManager.department;

    if (demartmentWithUpdatedManager.manager) {
      affectedNodes.push(demartmentWithUpdatedManager.manager);
    }

    if (demartmentWithUpdatedManager.affectedNodes.length) {
      affectedNodes.push(...demartmentWithUpdatedManager.affectedNodes);
    }

    await repository.setWorkspace(workspace);

    affectedNodes.unshift(updatedParent);

    return {
      department: formatDepartment(workspace, session, newDepartment, true),
      affected: {
        nodes: affectedNodes.map((n) =>
          formatNode(workspace, session, n, true)
        ),
      },
    };
  },

  async editDepartment(_parent, { department }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const affectedNodes: ReadonlyDeep<
      DomainDepartment | DomainPosition | DomainRootNode
    >[] = [];

    const rootNode = workspace.orgTree.rootNode;
    let departmentNode = getNode(
      rootNode,
      (n): n is DomainDepartment =>
        n.type === "department" && n.id === department.id
    );

    assert(departmentNode, "Department node not found");

    departmentNode = {
      ...departmentNode,
      title: department.title,
      color: (department.color as Color | undefined) ?? departmentNode.color,
    };

    workspace = {
      ...workspace,
      orgTree: {
        rootNode: replaceNode(rootNode, departmentNode),
      },
    };

    let prevParent = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === departmentNode?.parentId
    );
    let nextParent = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        department.parentId ? n.id === department.parentId : n.type === "root"
    );

    assert(prevParent, "Prev parent node not found");
    assert(nextParent, "New parent node not found");

    if (prevParent.id !== nextParent.id) {
      const result = moveNode(workspace, departmentNode, nextParent);

      workspace = result.workspace;
      departmentNode = result.node;
      nextParent = result.nextParent;
      prevParent = result.prevParent;

      affectedNodes.push(nextParent, prevParent);
    }

    const demartmentWithUpdatedManager = editDepartmentManager(
      workspace,
      departmentNode,
      department.manager
        ? {
            memberId: department.manager.memberId,
            positionId: department.manager.positionId,
          }
        : null
    );

    workspace = demartmentWithUpdatedManager.workspace;
    departmentNode = demartmentWithUpdatedManager.department;

    if (demartmentWithUpdatedManager.manager) {
      affectedNodes.push(demartmentWithUpdatedManager.manager);
    }

    if (demartmentWithUpdatedManager.affectedNodes.length) {
      affectedNodes.push(...demartmentWithUpdatedManager.affectedNodes);
    }

    await repository.setWorkspace(workspace);

    return {
      department: formatDepartment(workspace, session, departmentNode, true),
      affected: {
        nodes: affectedNodes.map((n) =>
          formatNode(workspace, session, n, true)
        ),
      },
    };
  },

  async movePosition(_parent, { data }, { logger, repository, session }) {
    let affectedNodes: ReadonlyDeep<
      DomainDepartment | DomainPosition | DomainRootNode
    >[] = [];

    let managedDepartment: ReadonlyDeep<DomainDepartment> | null = null;
    let workspace = await getSessionWorkspace(repository, session);

    const foundPosition = getOrCreatePosition(workspace, data.position);

    let position = foundPosition.position;

    workspace = foundPosition.workspace;

    if (position.managedDepartmentId) {
      managedDepartment = getNode(
        workspace.orgTree.rootNode,
        (n): n is DomainDepartment =>
          n.id === position.managedDepartmentId && n.type === "department"
      );

      assert(managedDepartment, "Expect department to exist");
    }

    const member = workspace.members.find((m) => m.id === position.memberId);

    let prevParent = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === position.parentId
    );

    assert(prevParent, "Prev parent node not found");

    let nextParent = prevParent;

    const moveResult = member
      ? movePositionWithMember(workspace, data, position, member, logger)
      : moveOpenPosition(workspace, data, position, logger);

    workspace = moveResult.workspace;
    position = moveResult.position;

    prevParent = moveResult.prevParent;
    nextParent = moveResult.nextParent;

    if (typeof data.nextItemIndex === "number") {
      const reorderResult = changeNodeOrder(
        workspace,
        position,
        data.nextItemIndex
      );

      workspace = reorderResult.workspace;
      position = reorderResult.node;
      nextParent = reorderResult.parent;
    }

    /**
     * NOTE: if the position we move was a department manager we have to
     * properly unlink them
     */
    if (managedDepartment) {
      const unlinkedDepartment = unlinkDepartmentManager(
        workspace,
        managedDepartment
      );

      workspace = unlinkedDepartment.workspace;
      managedDepartment = unlinkedDepartment.department;

      const unlinkedPrevDepartment = unlinkManagerPrevDepartment(
        workspace,
        position
      );

      workspace = unlinkedPrevDepartment.workspace;
      position = unlinkedPrevDepartment.manager;
    }

    workspace = await repository.setWorkspace(workspace);

    affectedNodes = registerAffectedNode(affectedNodes, prevParent);
    affectedNodes = registerAffectedNode(affectedNodes, nextParent);

    if (managedDepartment) {
      affectedNodes = registerAffectedNode(affectedNodes, managedDepartment);
    }

    const paths = getPrevAndCurrentPathes(
      workspace,
      session,
      position,
      prevParent
    );

    return {
      position: formatPosition(workspace, session, position, true),
      affected: {
        nodes: unifyFormattedNodes(
          affectedNodes.map((n) => formatNode(workspace, session, n, true)),
          paths.prevPathToRoot
        ),
        pathToRoot: paths.pathToRoot,
      },
    };
  },

  async moveDepartment(
    _parent,
    { department: args },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    let department = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment =>
        n.id === args.departmentId && n.type === "department"
    )!;
    let affectedNodes: ReadonlyDeep<
      DomainDepartment | DomainPosition | DomainRootNode
    >[] = [];

    assert(department, "Department not found");

    let prevParent = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        n.id === department.parentId
    );
    let nextParent = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
        args.parentId ? n.id === args.parentId : n.type === "root"
    );

    assert(prevParent, "Prev parent node not found");
    assert(nextParent, "New parent node not found");

    if (prevParent.id !== nextParent.id) {
      const result = moveNodeDomain(workspace, department, nextParent);

      workspace = result.workspace;
      department = result.node;
      nextParent = result.nextParent;
      prevParent = result.prevParent;

      logger = logger.withContext({
        position: { child: department.id, parent: nextParent.id },
      });
      logger.info("Moved node to new parent");
    } else {
      logger = logger.withContext({
        position: { child: department.id, parent: nextParent.id },
      });
      logger.info("Moved department inside same parent");
    }

    if (typeof args.nextItemIndex === "number") {
      const reorderResult = changeNodeOrder(
        workspace,
        department,
        args.nextItemIndex
      );

      workspace = reorderResult.workspace;
      department = reorderResult.node;
      nextParent = reorderResult.parent;
    }

    workspace = await repository.setWorkspace(workspace);

    affectedNodes = registerAffectedNode(affectedNodes, prevParent);
    affectedNodes = registerAffectedNode(affectedNodes, nextParent);

    const paths = getPrevAndCurrentPathes(
      workspace,
      session,
      department,
      prevParent
    );

    return {
      department: formatDepartment(workspace, session, department, true),
      affected: {
        nodes: unifyFormattedNodes(
          affectedNodes.map((n) => formatNode(workspace, session, n, true)),
          paths.prevPathToRoot
        ),
        pathToRoot: paths.pathToRoot,
      },
    };
  },
} satisfies MutationResolvers<Context>;

type DomainSubordinate = DomainDepartment | DomainPosition;
type FormattedSubordinateMap<T> = {
  DomainDepartment: Department;
  DomainPosition: Position;
}[T extends DomainDepartment ? "DomainDepartment" : "DomainPosition"];

function formatSubordinate(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  node: ReadonlyDeep<DomainSubordinate>,
  withSubordinates = false
): ReadonlyDeep<FormattedSubordinateMap<DomainSubordinate>> {
  if (node.type === "position") {
    return formatPosition(workspace, session, node, withSubordinates);
  }

  return formatDepartment(workspace, session, node, withSubordinates);
}

type DomainNode = DomainRootNode | DomainSubordinate;
type FormatNodeMap<T> = {
  DomainRootNode: RootNode;
  DomainDepartment: Department;
  DomainPosition: Position;
}[T extends DomainRootNode
  ? "DomainRootNode"
  : T extends DomainDepartment
  ? "DomainDepartment"
  : "DomainPosition"];

function formatNode(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  node: ReadonlyDeep<DomainNode>,
  withSubordinates = false
): ReadonlyDeep<FormatNodeMap<DomainNode>> {
  if (node.type === "root") {
    return formatRootNode(node, workspace, session, withSubordinates);
  }

  return formatSubordinate(workspace, session, node, withSubordinates);
}

function getPrevAndCurrentPathes(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  node: ReadonlyDeep<DomainSubordinate>,
  parentNode?: ReadonlyDeep<DomainNode>
): {
  pathToRoot: ReadonlyDeep<Position | Department>[];
  prevPathToRoot: ReadonlyDeep<Position | Department>[];
} {
  const rootNode = workspace.orgTree.rootNode;
  let prevPathToRoot: ReadonlyDeep<Position | Department>[] = [];

  if (parentNode && parentNode.type !== "root") {
    prevPathToRoot =
      searchNodeWithPathOfNodes(rootNode, parentNode.id).path?.map((n) =>
        formatSubordinate(workspace, session, n, true)
      ) ?? [];
  }

  return {
    pathToRoot:
      searchNodeWithPathOfNodes(rootNode, node.id).path?.map((n) =>
        formatSubordinate(workspace, session, n, true)
      ) ?? [],
    prevPathToRoot,
  };
}

/**
 * NOTE: make sure that we don't send the same node twice in different lists
 * to avoid troubles with Apollo cache
 */
function unifyFormattedNodes(
  affectedNodes: ReadonlyDeep<Department | Position | RootNode>[],
  prevPathToRoot: ReadonlyDeep<Position | Department>[]
): ReadonlyDeep<RootNode | Position | Department>[] {
  return [
    ...affectedNodes.filter(
      (node) => !prevPathToRoot.some((prevN) => prevN.id === node.id)
    ),
    ...prevPathToRoot,
  ];
}

function getOrCreateNode(
  workspace: ReadonlyDeep<DomainWorkspace>,
  node: NodeOrMember
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  node:
    | ReadonlyDeep<DomainRootNode>
    | ReadonlyDeep<DomainPosition>
    | ReadonlyDeep<DomainDepartment>;
} {
  assert(node.memberId ?? node.nodeId, "Expect memberId or nodeId");
  const { rootNode } = workspace.orgTree;

  if (node.nodeId === rootNode.id) {
    return {
      workspace,
      node: rootNode,
    };
  }

  const department = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition => n.type === "department" && n.id === node.nodeId
  );

  if (department) {
    return {
      workspace,
      node: department,
    };
  }

  const foundNode = getOrCreatePosition(workspace, {
    positionId: node.nodeId,
    memberId: node.memberId,
  });

  return {
    workspace: foundNode.workspace,
    node: foundNode.position,
  };
}

function getOrCreatePosition(
  workspace: ReadonlyDeep<DomainWorkspace>,
  node: PositionOrMember
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  position: ReadonlyDeep<DomainPosition>;
} {
  assert(node.memberId ?? node.positionId, "Expect memberId or positionId");

  let position = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition =>
      n.type === "position" &&
      (n.id === node.positionId || n.memberId === node.memberId)
  );

  if (!position && node.memberId) {
    [workspace, position] = createPositionDomain(workspace, node.memberId);
  }

  assert(position, "Position not found");

  return {
    workspace,
    position,
  };
}

function movePositionWithMember(
  workspace: ReadonlyDeep<DomainWorkspace>,
  data: MovePositionInput,
  position: ReadonlyDeep<DomainPosition>,
  member: ReadonlyDeep<DomainMember>,
  logger: Logger
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  prevParent: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>;
  nextParent: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>;
  position: ReadonlyDeep<DomainPosition>;
} {
  let moveResult;

  const prevParent = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
      n.id === position.parentId
  );

  assert(prevParent, "Prev parent node not found");

  if (!data.parent || !(data.parent.memberId || data.parent.nodeId)) {
    if (data.parent?.withoutManagerManual) {
      moveResult = markMemberAsNotHavingManager(workspace, member, position);
    } else {
      moveResult = resetMemberAsNotHavingManager(workspace, member, position);
    }
  } else if (changingPositionParent(workspace, data)) {
    /**
     * NOTE: do not move a position if it is already in the same parent
     */
    const foundParent = getOrCreateNode(workspace, data.parent);

    workspace = foundParent.workspace;

    if (foundParent.node.type === "department") {
      moveResult = moveMemberToDepartment(
        workspace,
        member,
        position,
        foundParent.node.id
      );
    } else if (foundParent.node.type === "position") {
      moveResult = moveMemberToManager(workspace, member, position, {
        positionId: foundParent.node.id,
      });
    } else {
      moveResult = moveNodeDomain(
        workspace,
        position,
        workspace.orgTree.rootNode
      );
    }
  }

  if (moveResult) {
    workspace = moveResult.workspace;

    if ("member" in moveResult) {
      member = moveResult.member;
    }

    if (workspace.slackIntegration) {
      const userToken = findTokenForSlackAdapterMemberUpdate(workspace, member);

      if (userToken) {
        assert(workspace.slackBotToken, "Expected Slack bot token to exist");

        const slackAdapter = getSlackAdapter(workspace.slackBotToken);

        void (async () => {
          try {
            await slackAdapter.updateMember(
              workspace.id,
              {
                memberId: member.id,
                customFields: await formatCustomFieldsForSlackUpdate(
                  workspace,
                  member,
                  slackAdapter
                ),
              },
              workspace.policy,
              userToken
            );
          } catch (error) {
            logger.error(error);
          }
        })();
      }
    }

    return {
      ...moveResult,
      position: moveResult.node,
    };
  }

  return {
    workspace,
    position,
    prevParent,
    nextParent: prevParent,
  };
}

function moveOpenPosition(
  workspace: ReadonlyDeep<DomainWorkspace>,
  data: MovePositionInput,
  position: ReadonlyDeep<DomainPosition>,
  logger: Logger
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  position: ReadonlyDeep<DomainPosition>;
  prevParent: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>;
  nextParent: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>;
} {
  let moveResult;

  const prevParent = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
      n.id === position.parentId
  );

  assert(prevParent, "Prev parent node not found");

  if (!data.parent || !(data.parent.memberId || data.parent.nodeId)) {
    position = {
      ...position,
      withoutManagerManual: !!data.parent?.withoutManagerManual,
    };
    workspace = {
      ...workspace,
      orgTree: {
        rootNode: replaceNode(workspace.orgTree.rootNode, position),
      },
    };
    moveResult = moveNodeDomain<
      DomainPosition,
      DomainRootNode | DomainPosition | DomainDepartment
    >(workspace, position, workspace.orgTree.rootNode);
  } else if (changingPositionParent(workspace, data)) {
    const foundParent = getOrCreateNode(workspace, data.parent);

    workspace = foundParent.workspace;
    const nextParent = foundParent.node;

    logger = logger.withContext({
      position: { child: position.id, parent: nextParent.id },
    });

    logger.info("Moved node to new parent");

    moveResult = moveNodeDomain<
      DomainPosition,
      DomainRootNode | DomainPosition | DomainDepartment
    >(workspace, position, nextParent);
  }

  if (moveResult) {
    return {
      ...moveResult,
      position: moveResult.node,
    };
  }

  return {
    workspace,
    position,
    prevParent,
    nextParent: prevParent,
  };
}

function changingPositionParent(
  workspace: ReadonlyDeep<DomainWorkspace>,
  data: MovePositionInput
): boolean {
  const foundPosition = getOrCreatePosition(workspace, data.position);

  const position = foundPosition.position;
  const { rootNode } = workspace.orgTree;

  const prevParent = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainRootNode | DomainPosition | DomainDepartment =>
      n.id === position.parentId
  );

  let nextParent = prevParent;

  if (!data.parent || data.parent.withoutManagerManual) {
    nextParent = rootNode;
  } else {
    const foundParent = getOrCreateNode(workspace, data.parent);

    nextParent = foundParent.node;
  }

  return prevParent?.id !== nextParent.id;
}

/**
 * NOTE: nodes might be affected multiple times during a calculation
 * this function makes sure that we store the latest version of an affected node
 */
function registerAffectedNode(
  nodes: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>[],
  node: (typeof nodes)[0]
): typeof nodes {
  const existingNodeIndex = nodes.findIndex((n) => n.id === node.id);

  if (existingNodeIndex !== -1) {
    const anodes = [
      ...nodes.slice(0, existingNodeIndex),
      node,
      ...nodes.slice(existingNodeIndex + 1),
    ];

    return anodes;
  }

  return [...nodes, node];
}

function editDepartmentManager(
  workspace: ReadonlyDeep<DomainWorkspace>,
  department: ReadonlyDeep<DomainDepartment>,
  manager: {
    memberId?: string | null;
    positionId?: string | null;
  } | null
): {
  workspace: ReadonlyDeep<DomainWorkspace>;
  department: ReadonlyDeep<DomainDepartment>;
  manager: ReadonlyDeep<DomainPosition> | null;
  affectedNodes: ReadonlyDeep<
    DomainRootNode | DomainPosition | DomainDepartment
  >[];
} {
  /**
   * NOTE: do not delete manager if it is already null
   */
  if (!manager && !department.managerId) {
    return {
      workspace,
      department,
      manager: null,
      affectedNodes: [],
    };
  }

  let affectedNodes: ReadonlyDeep<
    DomainDepartment | DomainPosition | DomainRootNode
  >[] = [];
  let managerNode: ReadonlyDeep<DomainPosition> | null = null;

  if (manager?.positionId) {
    managerNode = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.id === manager.positionId
    );

    assert(managerNode, "Expected manager to exist");

    /**
     * NOTE: do not reset the same manager
     */
    if (manager.positionId === department.managerId) {
      return {
        workspace,
        department,
        manager: null,
        affectedNodes: [],
      };
    }

    const unlinkedPrevDepartment = unlinkManagerPrevDepartment(
      workspace,
      managerNode
    );

    workspace = unlinkedPrevDepartment.workspace;
    managerNode = unlinkedPrevDepartment.manager;

    if (unlinkedPrevDepartment.prevDepartment) {
      affectedNodes = registerAffectedNode(
        affectedNodes,
        unlinkedPrevDepartment.prevDepartment
      );
    }
  } else if (manager?.memberId) {
    const createdPosition = createPositionDomain(workspace, manager.memberId);

    managerNode = createdPosition[1];
    workspace = createdPosition[0];

    affectedNodes = registerAffectedNode(
      affectedNodes,
      workspace.orgTree.rootNode
    );
  }

  const unlinkedDepartment = unlinkDepartmentManager(workspace, department);

  workspace = unlinkedDepartment.workspace;
  department = unlinkedDepartment.department;

  if (unlinkedDepartment.prevManager) {
    affectedNodes = registerAffectedNode(
      affectedNodes,
      unlinkedDepartment.prevManager
    );
  }

  if (managerNode) {
    if (managerNode.parentId !== department.id) {
      const movedNode = moveNodeDomain(workspace, managerNode, department);

      workspace = movedNode.workspace;
      managerNode = movedNode.node;
      department = movedNode.nextParent;
      affectedNodes = registerAffectedNode(affectedNodes, movedNode.prevParent);
    }

    const reorderResult = changeNodeOrder(workspace, managerNode, 0);

    workspace = reorderResult.workspace;
    department = reorderResult.parent as DomainDepartment;
    managerNode = reorderResult.node;

    const linked = linkDeparmentManager(workspace, department, managerNode);

    workspace = linked.workspace;
    department = linked.department;
    managerNode = linked.manager;
  }

  return {
    workspace,
    department,
    manager: managerNode,
    affectedNodes,
  };
}

function getOpenPositions(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): ReadonlyDeep<Position[]> {
  const allPositions = getNodes(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition => n.type === "position" && !n.memberId
  );

  return allPositions.map((p) => formatPosition(workspace, session, p));
}

function formatOrgTree(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): ReadonlyDeep<Tree> {
  return {
    rootNode: formatRootNode(workspace.orgTree.rootNode, workspace, session),
    editable: canEditWorkspace(workspace, session),
  };
}

function formatPositionsWithoutTeam(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): ReadonlyDeep<Position[]> {
  const {
    orgTree: { rootNode },
  } = workspace;
  const positions = getNodes(
    rootNode,
    (n): n is DomainPosition => n.type === "position" && !n.teamIds.length
  );
  const formattedPositions = positions.map((p) =>
    formatPosition(workspace, session, p, true)
  );
  const sortedPositions = sortBy(formattedPositions, (pos) =>
    pos.member?.realName?.toLowerCase()
  );

  return sortedPositions;
}

/**
 * NOTE: returns a node, its subordinates and its parent's subordinates
 */
const getFullBranch = (
  node: ReadonlyDeep<DomainPosition | DomainDepartment>,
  rootNode: ReadonlyDeep<DomainRootNode | DomainPosition | DomainDepartment>,
  result: ReadonlyDeep<DomainPosition | DomainDepartment>[] = []
): ReadonlyDeep<DomainPosition | DomainDepartment>[] => {
  const parent = getNode(
    rootNode,
    (n): n is DomainPosition | DomainDepartment =>
      n.type !== "root" && n.id === node.parentId
  );
  let currentResult = [...result];

  currentResult.push(node);
  currentResult = currentResult.concat(node.subordinates);

  if (parent) {
    currentResult.push(parent);
    currentResult = currentResult.concat(
      getFullBranch(parent, rootNode, currentResult)
    );
  }

  return currentResult;
};

function getTouchedNodes(
  rootNode: ReadonlyDeep<DomainRootNode>,
  nodesIds: ReadonlyDeep<string[]>,
  withInputNodes = false
): ReadonlyDeep<DomainPosition | DomainDepartment>[] {
  const nodes = nodesIds
    .map((id) =>
      getNode(
        rootNode,
        (n): n is DomainPosition | DomainDepartment =>
          n.type !== "root" && n.id === id
      )
    )
    .filter((x): x is DomainPosition | DomainDepartment => x !== null);

  const duplicatedNodes = nodes.flatMap((n) => getFullBranch(n, rootNode));

  let uniqueNodes = uniqBy(duplicatedNodes.concat(rootNode.subordinates), "id");

  if (withInputNodes) {
    return uniqueNodes;
  }
  uniqueNodes = uniqueNodes.filter(
    (n) => !nodes.some((inputNode) => inputNode.id === n.id)
  );

  return uniqueNodes;
}
