import {
  Department as DomainDepartment,
  Position as DomainPosition,
} from "@organice/core/domain";
import {
  getNode,
  getNodes,
  searchNodeWithPathOfNodes,
} from "@organice/core/domain/org-chart";

import { getSessionWorkspace } from "../../../domain";
import {
  type RecentMembersResolvers,
  type QueryResolvers,
  type Team,
  type Position,
  type Department,
  type RecentMembers as GraphQlRecentMembers,
} from "../../server.generated";

import {
  Context,
  formatPosition,
  formatDepartment,
  formatTeam,
  formatMember,
} from "./_common";

export const RecentMembers: RecentMembersResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if ("type" in obj) {
      if (obj.type === "position") {
        return "Position";
      }

      if (obj.type === "department") {
        return "Department";
      }
    }

    return "Team";
  },
};

export const Query = {
  async globalSearch(_parent, { label, recent }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const positions = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition => n.type === "position"
    );

    const departments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment => n.type === "department"
    );

    const formattedPositions = positions.map((position) =>
      formatPosition(workspace, session, position)
    );
    const formattedDepartments = departments.map((node) =>
      formatDepartment(workspace, session, node)
    );
    const formattedTeams = workspace.teams.map((team) =>
      formatTeam(workspace, session, team, true)
    );

    // get filtered results
    const filteredPositions = formattedPositions.filter(
      (node) =>
        searchRegExp(node.title ?? "", label) ||
        searchRegExp(node.member?.realName ?? "", label)
    );

    const filteredDepartments = formattedDepartments.filter((node) =>
      searchRegExp(node.title ?? "", label)
    );
    const filteredTeams = formattedTeams.filter((node) =>
      searchRegExp(node.label, label)
    );

    // get recent results

    const recentSearch = recent.reduce<GraphQlRecentMembers[]>(
      (acc, { id, type }) => {
        const positionMatch =
          type === "Position" &&
          formattedPositions.find((pos) => pos.id === id);

        if (positionMatch) {
          acc.push(positionMatch as Position);

          return acc;
        }
        const departmentMatch =
          type === "Department" &&
          formattedDepartments.find((pos) => pos.id === id);

        if (departmentMatch) {
          acc.push(departmentMatch as Department);

          return acc;
        }
        const teamMatch =
          type === "Team" && formattedTeams.find((pos) => pos.id === id);

        if (teamMatch) {
          acc.push(teamMatch as Team);

          return acc;
        }

        return acc;
      },
      []
    );

    return {
      positions: filteredPositions.slice(0, 10),
      departments: filteredDepartments.slice(0, 10),
      teams: filteredTeams.slice(0, 10),
      recentSearch: recentSearch.splice(0, 5),
    };
  },

  async searchPositionsAndDepartments(
    _parent,
    { nodeId, search },
    { repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    const nodeSubordinates =
      getNode(
        workspace.orgTree.rootNode,
        (n): n is DomainPosition | DomainDepartment => n.id === nodeId
      )?.subordinates.map((s) => s.id) ?? [];

    const parentIds =
      searchNodeWithPathOfNodes(workspace.orgTree.rootNode, nodeId).path?.map(
        (n) => n.id
      ) ?? [];

    const excludeIds = [...nodeSubordinates, ...parentIds];

    const allPositions = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        !excludeIds.includes(n.id) &&
        n.type === "position" &&
        n.memberId !== null
    ).map((pos) => formatPosition(workspace, session, pos, true));

    const allDepartments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment =>
        !excludeIds.includes(n.id) && n.type === "department"
    ).map((dep) => formatDepartment(workspace, session, dep, true));

    const allMembers = workspace.members
      .filter((m) => {
        const position = getNode(
          workspace.orgTree.rootNode,
          (n): n is DomainPosition =>
            n.type === "position" && n.memberId === m.id
        );

        return !position;
      })
      .map((m) => formatMember({ workspace, session, member: m }));

    const title = search.toLowerCase();

    const departments = allDepartments.filter((d) =>
      d.title?.toLowerCase().includes(title)
    );

    const positions = allPositions.filter(
      (p) =>
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        p.member?.realName?.toLowerCase().includes(title) ||
        p.title?.toLowerCase().includes(title)
    );

    const members = allMembers.filter((m) =>
      m.realName?.toLowerCase().includes(title)
    );

    return {
      positions,
      departments,
      members,
    };
  },
} satisfies QueryResolvers<Context>;

const searchRegExp = (str: string, value: string): boolean => {
  const escaped = value.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

  return !!(str || "").match(
    new RegExp(`^(?=.*${escaped})(?!.*Untitled|untitled)`, "igm")
  );
};
