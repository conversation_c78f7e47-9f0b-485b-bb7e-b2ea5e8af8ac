import { ReadonlyDeep } from "@organice/core/domain";
import { countries } from "@organice/core/domain/data-completion";

import { getSessionWorkspace } from "../../../domain";
import { type QueryResolvers, type Country } from "../../server.generated";

import { Context } from "./_common";

export const Query = {
  async countries(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return {
      countries: workspace.members
        .reduce((acc: ReadonlyDeep<Country>[], member) => {
          const { name, emoji } = countries.find(
            (country) => country.name === member.country
          ) ?? { name: "", emoji: "" };

          const countryLabel = [...(emoji && [emoji]), name].join(" ");
          const curCountry = acc.find(
            (country) => country.label === countryLabel
          );

          if (!curCountry) {
            return [
              ...acc,
              { label: countryLabel, members: [member] },
            ] as Country[];
          }

          return [
            ...acc.filter((country) => country.label !== curCountry.label),
            {
              ...curCountry,
              members: [...curCountry.members, member],
            } as Country,
          ];
        }, [])
        .filter((c: { label: string }) => c.label !== ""),
    };
  },
} satisfies QueryResolvers<Context>;
