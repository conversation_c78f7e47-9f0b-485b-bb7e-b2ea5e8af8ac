// eslint-disable-next-line max-classes-per-file
import assert from "assert";

import {
  ValidationError,
  <PERSON>only<PERSON>eep,
  replaceMember,
  Workspace as DomainWorkspace,
} from "@organice/core/domain";
import {
  getBillableMembersCount,
  switchToAppSumoSubscription,
} from "@organice/core/domain/billing";
import { differenceInCalendarDays } from "date-fns";

import { getSessionWorkspace } from "../../../domain";
import {
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import { Billing } from "../../server.generated";

import {
  Context,
  InvalidAppSumoCodeError,
  AppSumoCodeHasBeenRedeemedError,
} from "./_common";

export const Query = {
  async billing(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatBilling(workspace);
  },

  async proPlanPrices(_parent, _args, { repository, session, stripeAdapter }) {
    const workspace = await getSessionWorkspace(repository, session);
    const prices = await stripeAdapter.getPrices(
      getBillableMembersCount(workspace)
    );
    const monthlyPrice = prices.find(
      (price) => price.interval === "month" && !price.discounted
    );
    const yearlyPrice = prices.find(
      (price) => price.interval === "year" && !price.discounted
    );

    assert(monthlyPrice, "Expected monthly price to exist");
    assert(yearlyPrice, "Expected yearly price to exist");

    return {
      month: monthlyPrice.perUnit.formatted,
      year: yearlyPrice.perUnit.formatted,
    };
  },

  async stripeCheckoutUrl(
    _parent,
    args,
    { repository, session, stripeAdapter }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const me = workspace.members.find((m) => m.id === session.memberId);

    assert(me, "Me not found");

    const { interval, successUrl, cancelUrl } = args;

    assert(
      interval === "month" || interval === "year",
      "Expected interval to be either 'month' or 'year'"
    );

    const checkoutUrl = await stripeAdapter.generateCheckoutUrl({
      workspace: {
        id: workspace.id,
        name: workspace.name,
      },
      member: {
        id: me.id,
        email: me.email ?? "",
      },
      quantity: getBillableMembersCount(workspace),
      price:
        interval === "year"
          ? { interval: "year", discounted: false }
          : { interval: "month", discounted: false },
      successUrl,
      cancelUrl,
    });

    return checkoutUrl;
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async trackProPlanClick(_parent, _args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);
    let me = workspace.members.find((m) => m.id === session.memberId);

    assert(me, `Expected member with id "${session.memberId}" to exist`);

    const date = new Date();

    me = {
      ...me,
      firstClickedProPlanAt: me.firstClickedProPlanAt ?? date,
    };
    workspace = replaceMember(workspace, me);

    await repository.setWorkspace(workspace);
  },

  async redeemAppSumoCode(
    _parent,
    args,
    { appSumoGateway, logger, productOwnerNotifier, repository, session }
  ) {
    const now = new Date();
    let workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find((m) => m.id === session.memberId);

    assert(member, `Expected me to exist (id: "${session.memberId}`);

    logger = logger.withContext({ workspace, member });

    if (workspace.billing.subscription.type === "stripe") {
      throw new ValidationError(
        "We’re unable to update your subscription automatically. Please reach out to our team via the Intercom chat in the app for assistance."
      );
    }

    if (
      workspace.billing.subscription.type === "appsumo" &&
      workspace.billing.subscription.ok
    ) {
      throw new ValidationError(
        "You already have an active AppSumo subscription. Please reach out to our team via the Intercom chat in the app for assistance."
      );
    }

    try {
      await appSumoGateway.redeemCode(session.workspaceId, args.code);
    } catch (error) {
      if (error instanceof InvalidAppSumoCodeError) {
        throw new ValidationError(
          "Invalid code. Please reach out to our team via the Intercom chat in the app for assistance."
        );
      } else if (error instanceof AppSumoCodeHasBeenRedeemedError) {
        throw new ValidationError(
          "The code has been already activated. Please reach out to our team via the Intercom chat in the app for assistance."
        );
      } else {
        throw error;
      }
    }

    logger.info("AppSumo code has been redeemed", { code: args.code });

    workspace = switchToAppSumoSubscription(workspace, now, args.code);

    await repository.setWorkspace(workspace);

    await productOwnerNotifier
      .handleSwitchToAppSumoSubscription({
        workspace: {
          name: workspace.name,
        },
        billing: {
          code: args.code,
        },
      })
      .catch((error) => logger.error(error));

    return formatBilling(workspace);
  },
} satisfies MutationResolvers<Context>;

function formatBilling(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReadonlyDeep<Billing> {
  return {
    id: workspace.id,
    isStripeProPlanAvailable: workspace.members.length <= 500,
    subscription:
      workspace.billing.subscription.type === "stripe"
        ? {
            __typename: "StripeSubscription",
            ok: workspace.billing.subscription.ok,
            plan: workspace.billing.subscription.plan,
          }
        : workspace.billing.subscription.type === "appsumo"
        ? {
            __typename: "AppSumoSubscription",
            ok: workspace.billing.subscription.ok,
            expiresSoon: workspace.billing.subscription.ok
              ? Math.max(
                  0,
                  differenceInCalendarDays(
                    workspace.billing.subscription.endsAt as Date,
                    new Date()
                  )
                ) <= 10
              : false,
            daysLeft: workspace.billing.subscription.ok
              ? Math.max(
                  0,
                  differenceInCalendarDays(
                    workspace.billing.subscription.endsAt as Date,
                    new Date()
                  )
                )
              : null,
            endsAt: workspace.billing.subscription.endsAt.toISOString(),
          }
        : {
            __typename: "TrialSubscription",
            ok: workspace.billing.subscription.ok,
            daysLeft: workspace.billing.subscription.ok
              ? Math.max(
                  0,
                  differenceInCalendarDays(
                    workspace.billing.subscription.endsAt as Date,
                    new Date()
                  )
                )
              : null,
          },
  };
}
