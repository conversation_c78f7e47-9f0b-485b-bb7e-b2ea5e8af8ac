/* eslint-disable max-classes-per-file */

// eslint-disable-next-line max-classes-per-file
import assert from "assert";

import {
  Logger,
  ProductOwnerNotifier,
  StripeAdapter,
  WorkspaceRepository,
  Department as DomainDepartment,
  Position as DomainPosition,
  RootNode as DomainRootNode,
  Team as DomainTeam,
  Member as DomainMember,
  Workspace as DomainWorkspace,
  PresetPolicyId,
  ReadonlyDeep,
  TimeOffPolicy as DomainTimeOffPolicy,
  DayOfWeek as DomainDayOfWeek,
} from "@organice/core/domain";
import { ActivityLog } from "@organice/core/domain/activityLog";
import {
  collectMemberData,
  collectMemberPolicyViolations,
  countries,
  formatPolicyFields,
  getWorkspaceCustomPolicyFields,
} from "@organice/core/domain/data-completion";
import {
  HolidayDescriptionGenerator,
  HolidayGateway,
} from "@organice/core/domain/holidays";
import {
  getParentDepartment,
  getParentPosition,
  getNode,
  getNodes,
  getSubordinatesInfo,
} from "@organice/core/domain/org-chart";
import type { PrismaClient } from "@prisma/client";
import { sortBy } from "lodash";

import { Session, getSessionWorkspace } from "../../../domain";
import {
  Team,
  Teams,
  Member,
  Position,
  Department,
  RootNode,
  MemberFieldPolicy,
  FieldPolicy,
  Workspace,
  Policy,
  TimeOffPolicy,
  DayOfWeek,
} from "../../server.generated";

export interface Context {
  activityLog: ActivityLog;
  appSumoGateway: AppSumoGateway;
  holidayDescriptionGenerator: HolidayDescriptionGenerator;
  holidayGateway: HolidayGateway;
  logger: Logger;
  prismaClient: PrismaClient;
  productOwnerNotifier: ProductOwnerNotifier;
  repository: WorkspaceRepository;
  session: Session;
  stripeAdapter: StripeAdapter;
}

export interface AppSumoGateway {
  redeemCode(workspaceId: string, code: string): Promise<void>;
}

export class InvalidAppSumoCodeError extends Error {}

export class AppSumoCodeHasBeenRedeemedError extends Error {}

export class InvalidTokenError extends Error {}

interface TextField {
  id: string;
  key: string;
  text?: string;
}

interface LinkField {
  id: string;
  key: string;
  link?: string;
}

export interface UserField<TMember> {
  id: string;
  key: string;
  member?: ReadonlyDeep<TMember>;
}

interface SimplifiedUserField {
  id: string;
  key: string;
  memberId?: string;
}

interface DateField {
  id: string;
  key: string;
  date?: string;
}
interface PhotoField {
  id: string;
  key: string;
  url512?: string;
  url72?: string;
}
export interface TeamsField<TTeam> {
  id: string;
  key: string;
  teams: ReadonlyDeep<TTeam>[];
}
export interface DepartmentField<TDepartment> {
  id: string;
  key: string;
  department?: ReadonlyDeep<TDepartment>;
}
export interface ManagerField<TPosition> {
  id: string;
  key: string;
  position?: ReadonlyDeep<TPosition>;
  withoutManagerManual?: boolean;
}

interface SimplifiedManagerField {
  id: string;
  key: string;
  positionId?: string;
  withoutManagerManual?: boolean;
}

export type MemberField<TMember, TPosition, TDepartment, TTeam> =
  | TextField
  | DateField
  | PhotoField
  | TeamsField<TTeam>
  | DepartmentField<TDepartment>
  | ManagerField<TPosition>
  | LinkField
  | UserField<TMember>;

export async function getMembers(
  repository: WorkspaceRepository,
  session: Session,
  isAdmin?: boolean | null,
  missingFields?: string[] | null
): Promise<ReadonlyDeep<Member[]>> {
  const workspace = await getSessionWorkspace(repository, session);
  let members = workspace.members.map((member) => {
    const formatedMember = formatMember({ workspace, session, member });
    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.memberId === member.id
    );

    return {
      ...formatedMember,
      position: memberPosition
        ? formatPosition(workspace, session, memberPosition)
        : null,
    };
  });

  if (missingFields?.length) {
    members = members.filter((m) => {
      for (const fieldId of missingFields) {
        const field = m.fields.find((f) => f.key === fieldId);

        assert(field, `Expected field for ${fieldId} to exist`);

        if (
          ("text" in field && field.text) ||
          ("date" in field && field.date) ||
          ("teams" in field && (field.teams as Team[]).length) ||
          ("department" in field && field.department) ||
          ("position" in field && field.position)
        ) {
          return false;
        }
      }

      return true;
    });
  }

  if (typeof isAdmin === "boolean") {
    return members.filter((member) => member.isAdmin === isAdmin);
  }

  members = sortBy(members, (member) => member.realName?.toLowerCase());

  return members;
}

export async function getTeams(
  repository: WorkspaceRepository,
  session: Session
): Promise<ReadonlyDeep<Teams>> {
  const workspace = await getSessionWorkspace(repository, session);

  const teams = workspace.teams
    .map((team) => formatTeam(workspace, session, team, true))
    .sort(
      (a, b) =>
        Date.parse((b as unknown as { updateAt: string }).updateAt) -
        Date.parse((a as unknown as { updateAt: string }).updateAt)
    );

  return { teams, teamsQuantity: teams.length };
}

export function getCountriesWMembers(
  workspace: ReadonlyDeep<DomainWorkspace>
): {
  label: string;
  members: ReadonlyDeep<DomainMember>[];
}[] {
  return workspace.members.reduce(
    (
      countriesWMembers: {
        label: string;
        members: ReadonlyDeep<DomainMember>[];
      }[],
      member
    ) => {
      const country = countries.find((c) => c.name === member.country);

      if (!country) {
        return countriesWMembers;
      }

      const countryLabel = `${country.emoji} ${country.name}`;

      const countryWMember = countriesWMembers.find(
        (c) => c.label === countryLabel
      );

      if (!countryWMember) {
        return [
          ...countriesWMembers,
          { label: countryLabel, members: [member] },
        ];
      }

      return [
        ...countriesWMembers.filter((c) => c.label !== countryWMember.label),
        {
          ...countryWMember,
          members: [...countryWMember.members, member],
        },
      ];
    },
    []
  );
}

/**
 * The order of the fields is the following:
 * - All ids in the list
 * - All other custom fields
 */

const MEMBER_FIELDS_IDS = [
  "photo",
  "name",
  "jobTitle",
  "email",
  "slack",
  "organicePhone",
  "manager",
  "teams",
  "department",
  "country",
  "birthday",
  "anniversary",
];

export function collectMemberFields<TMember, TPosition, TDepartment, TTeam>(
  workspace: ReadonlyDeep<DomainWorkspace>,
  member: ReadonlyDeep<DomainMember>,
  formaters: {
    formatDepartment: (
      position: ReadonlyDeep<DomainDepartment>
    ) => ReadonlyDeep<TDepartment>;
    formatTeam: (team: ReadonlyDeep<DomainTeam>) => ReadonlyDeep<TTeam>;
  }
): /**
 * NOTE: collectMemberFields should not call formatPosition or
 * formatMember that might cause a recursion cycle
 */
(SimplifiedManagerField | SimplifiedUserField)[] {
  const memberData = collectMemberData(workspace, member);
  const memberPosition = getNode(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition =>
      n.type === "position" && n.memberId === member.id
  );
  const fields: MemberField<TMember, TPosition, TDepartment, TTeam>[] =
    MEMBER_FIELDS_IDS.map((fieldId) => {
      return {
        id: `${member.id}-${fieldId}`,
        key: fieldId,
        text: undefined,
      };
    });
  const photoField = fields.find(
    (field): field is PhotoField => field.key === "photo"
  )!;
  const nameField = fields.find(
    (field): field is TextField => field.key === "name"
  )!;
  const titleField = fields.find(
    (field): field is TextField => field.key === "jobTitle"
  )!;
  const emailField = fields.find(
    (field): field is LinkField => field.key === "email"
  )!;
  const slackField = fields.find(
    (field): field is LinkField => field.key === "slack"
  )!;
  const phoneField = fields.find(
    (field): field is TextField => field.key === "organicePhone"
  )!;
  const managerField = fields.find(
    (field): field is SimplifiedManagerField => field.key === "manager"
  )!;
  const teamsField = fields.find(
    (field): field is TeamsField<TTeam> => field.key === "teams"
  )!;
  const departmentField = fields.find(
    (field): field is DepartmentField<TDepartment> => field.key === "department"
  )!;
  const birthdayField = fields.find(
    (field): field is DateField => field.key === "birthday"
  )!;
  const anniversaryField = fields.find(
    (field): field is DateField => field.key === "anniversary"
  )!;
  const countryField = fields.find(
    (field): field is TextField => field.key === "country"
  )!;

  nameField.text = member.realName ?? "";
  emailField.link = member.email ?? "";
  slackField.link = `@${
    member.displayName && member.displayName !== ""
      ? member.displayName
      : member.realName ?? ""
  }`;
  phoneField.text = member.organicePhone ?? "";
  countryField.text = member.country ?? "";

  /**
   * NOTE: for some reason, there could be situations when
   * photoUrl has different image then photo512/72Url
   */
  photoField.url512 = member.photo512Url;
  photoField.url72 = member.photo72Url;

  teamsField.teams = [];
  managerField.positionId = undefined;
  managerField.withoutManagerManual = undefined;
  departmentField.department = undefined;
  titleField.text = "";

  if (memberPosition) {
    const managerPosition = getParentPosition(workspace, memberPosition.id);
    const department = getParentDepartment(workspace, memberPosition.id);

    titleField.text = memberPosition.title;

    managerField.withoutManagerManual = memberPosition.withoutManagerManual;

    if (managerPosition) {
      managerField.positionId = managerPosition.id;
    }

    teamsField.teams = workspace.teams
      .filter((team) => memberPosition.teamIds.includes(team.id))
      .map((team) => formaters.formatTeam(team));

    if (department) {
      departmentField.department = formaters.formatDepartment(department);
    }
  }

  birthdayField.date = memberData[PresetPolicyId.BIRTHDAY]!;
  anniversaryField.date = memberData[PresetPolicyId.ANNIVERSARY]!;

  const workspaceCustomPolicyFields = getWorkspaceCustomPolicyFields(workspace);

  return [
    ...fields,
    ...workspaceCustomPolicyFields.map((policy) => {
      const { type } = policy;

      if (type === "link") {
        const field: LinkField = {
          id: `${member.id}-${policy.id}`,
          key: policy.id,
          link: "",
        };
        const fieldValue = member.organiceCustomFileds[policy.id];

        if (fieldValue) {
          field.link = fieldValue;
        }

        return field;
      }

      if (type === "date") {
        const field: DateField = {
          id: `${member.id}-${policy.id}`,
          key: policy.id,
          date: "",
        };
        const fieldValue = member.organiceCustomFileds[policy.id];

        if (fieldValue) {
          field.date = fieldValue;
        }

        return field;
      }

      if (type === "user") {
        const field: SimplifiedUserField = {
          id: `${member.id}-${policy.id}`,
          key: policy.id,
          memberId: member.organiceCustomFileds[policy.id],
        };

        return field;
      }

      const field: TextField = {
        id: `${member.id}-${policy.id}`,
        key: policy.id,
        text: "",
      };
      const fieldValue = member.organiceCustomFileds[policy.id];

      if (fieldValue) {
        field.text = fieldValue;
      }

      return field;
    }),
  ];
}

export function getDataField(
  workspace: ReadonlyDeep<DomainWorkspace>,
  dataFieldId?: string
): ReadonlyDeep<MemberFieldPolicy> {
  const memberFields = collectMemberFieldPolicy(workspace);

  const dataField =
    memberFields.find((field) => field.id === dataFieldId) ??
    memberFields.find((field) => field.id === "country");

  assert(dataField, "Expected data field to exist");

  return dataField;
}

export function collectMemberFieldPolicy(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReadonlyDeep<MemberFieldPolicy[]> {
  const { fields } = formatPolicy(workspace);

  const predefinedFields: MemberFieldPolicy[] = [
    {
      id: "name",
      label: "Name",
      type: "text",
      required: true,
      isEditable: false,
      isPublic: true,
    },
    {
      id: "email",
      label: "Email",
      type: "link",
      required: true,
      isEditable: false,
      isPublic: true,
    },
    {
      id: "slack",
      label: "Slack handler",
      type: "link",
      required: true,
      isEditable: false,
      isPublic: true,
    },
    {
      id: "teams",
      label: "Teams",
      type: "teams",
      required: false,
      isEditable: true,
      isPublic: true,
    },
    {
      id: "department",
      label: "Department",
      type: "department",
      required: false,
      isEditable: true,
      isPublic: true,
    },
  ];

  function remapPolicyType(field: FieldPolicy): MemberFieldPolicy["type"] {
    const { id, type } = field;

    if (id === "manager") {
      return "manager";
    }

    if (id === "photo") {
      return "photo";
    }

    if (id === "department") {
      return "department";
    }

    if (id === "teams") {
      return "teams";
    }

    return type;
  }

  return [
    ...MEMBER_FIELDS_IDS.map((fieldId) => {
      const policyField = fields.find((field) => field.id === fieldId);

      if (policyField) {
        const { id, label, required, publiclyAvailable } = policyField;

        return {
          id,
          label,
          type: remapPolicyType(policyField),
          required,
          isEditable: id !== "photo",
          isPublic: publiclyAvailable,
        };
      }
      const predefinedField = predefinedFields.find(
        (field) => field.id === fieldId
      );

      assert(
        predefinedField,
        `if a field is not listed in policies it should be listed in predefined fields list, ${fieldId}`
      );

      return predefinedField;
    }),
    ...fields
      .filter((policyField) => !MEMBER_FIELDS_IDS.includes(policyField.id))
      .map((policyField) => {
        const { id, label, required, publiclyAvailable } = policyField;

        return {
          id,
          label,
          type: remapPolicyType(policyField),
          required,
          isEditable: true,
          isPublic: publiclyAvailable,
        };
      }),
  ];
}

export function formatWorkspace(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): ReadonlyDeep<Workspace> {
  assert(
    process.env.ORGANICE_SLACK_APP_ID,
    "Expected ORGANICE_SLACK_APP_ID env variable to be defined"
  );

  const memberFields = collectMemberFieldPolicy(workspace);
  const dataField = getDataField(workspace, workspace.chartLayout.dataField);

  return {
    id: workspace.id,
    name: workspace.name,
    iconUrl: workspace.iconUrl,
    url: workspace.url,
    customFields: workspace.customFields,
    editable: canEditWorkspace(workspace, session),
    memberFields,
    clusterHorizontal: workspace.chartLayout.clusterHorizontal,
    dataField,
    appId: process.env.ORGANICE_SLACK_APP_ID,
    installedAt: workspace.installedAt?.toDateString(),
  };
}

export function isSimplifiedUserField<TMember, TPosition, TDepartment, TTeam>(
  field: MemberField<TMember, TPosition, TDepartment, TTeam>
): field is SimplifiedUserField {
  return "memberId" in field;
}
export function isSimplifiedManagerField<
  TMember,
  TPosition,
  TDepartment,
  TTeam
>(
  field: MemberField<TMember, TPosition, TDepartment, TTeam>
): field is SimplifiedManagerField {
  return "positionId" in field;
}

/**
 * NOTE: this is the same method as in timeOffs.ts
 * but this one does not return policies members to avoid recursions
 * formatTimeOffPolicy <-> formatMember
 */
function formatTimeOffPolicy(
  workspace: ReadonlyDeep<DomainWorkspace>,
  policy: ReadonlyDeep<DomainTimeOffPolicy>
): TimeOffPolicy {
  return {
    id: policy.id,
    title: policy.title,
    isDefault: policy.isDefault,
    workDays: policy.workDays.map(
      (dayOfWeek) => domainDayOfWeekToGraphQlDayOfWeek[dayOfWeek]
    ),
    includedWeekendDays: policy.includedWeekendDays.map(
      (dayOfWeek) => domainDayOfWeekToGraphQlDayOfWeek[dayOfWeek]
    ),
    notifyAccruals: policy.notifyAccruals,
    members: [],
    typePolicies: policy.typePolicies.map((typePolicy) => ({
      ...typePolicy,
      type: workspace.timeOffs.types.find(
        (type) => type.id === typePolicy.typeId
      )!,
      nextAccruals: typePolicy.nextAccruals?.toDateString(),
      yearStart: typePolicy.yearStart,
    })),
  };
}

export function formatMember({
  workspace,
  session,
  member,
  permission = false,
  formatted = [],
}: {
  workspace: ReadonlyDeep<DomainWorkspace>;
  session: Session;
  member: ReadonlyDeep<DomainMember>;
  permission?: boolean;
  formatted?: ReadonlyDeep<Member>[];
}): ReadonlyDeep<Member> {
  let formattedMember: ReadonlyDeep<Member> | undefined = formatted.find(
    (m) => m.id === member.id
  );

  if (formattedMember) {
    return formattedMember;
  }

  const memberData = collectMemberData(workspace, member);
  const fields = collectMemberFields<Member, Position, Department, Team>(
    workspace,
    member,
    {
      formatDepartment: (department) =>
        formatDepartment(workspace, session, department),
      formatTeam: (team) => formatTeam(workspace, session, team, false),
    }
  );

  formattedMember = {
    ...member,
    fields,
    timeOffs: Object.entries(member.timeOffs).map(([typeId, t]) => ({
      memberId: member.id,
      typeId,
      balance: t?.balance ?? 0,
      nextResetAt: t?.nextResetAt?.toISOString(),
    })),
    title: memberData[PresetPolicyId.JOB_TITLE],
    nonFilledFields: permission
      ? collectMemberPolicyViolations(workspace, member).map((violation) => {
          return { id: violation.id, label: violation.label };
        })
      : null,
    timeOffTypePolicy: member.timeOffTypePolicyId
      ? formatTimeOffPolicy(
          workspace,
          workspace.timeOffs.policies.find(
            (p) => p.id === member.timeOffTypePolicyId
          )!
        )
      : undefined,
  };

  const updatedFields = fields.map((field) => {
    if (isSimplifiedUserField(field)) {
      if (field.memberId === member.id) {
        return {
          id: field.id,
          key: field.key,
          member: {
            ...formattedMember,
            fields: fields.filter((f) => {
              return (
                !isSimplifiedUserField(f) ||
                (isSimplifiedUserField(f) && f.memberId !== member.id)
              );
            }),
          },
        } as UserField<Member>;
      }

      if (field.memberId) {
        return {
          id: field.id,
          key: field.key,
          member: formatMember({
            workspace,
            session,
            member: workspace.members.find((m) => m.id === field.memberId)!,
            formatted: [...formatted, formattedMember],
          }),
        } as UserField<Member>;
      }

      return {
        id: field.id,
        key: field.key,
        member: undefined,
      } as UserField<Member>;
    }

    if (isSimplifiedManagerField(field)) {
      if (field.positionId) {
        const position = getNode(
          workspace.orgTree.rootNode,
          (n): n is DomainPosition =>
            n.type === "position" && n.id === field.positionId
        )!;

        return {
          id: field.id,
          key: field.key,
          position: formatPosition(workspace, session, position, false, {
            formatMember: (props) => {
              return formatMember({
                ...props,
                formatted: [...formatted, formattedMember],
              });
            },
            formatDepartment,
          }),
          withoutManagerManual: field.withoutManagerManual,
        } as ManagerField<Position>;
      }

      return {
        id: field.id,
        key: field.key,
        position: undefined,
        withoutManagerManual: field.withoutManagerManual,
      } as ManagerField<Position>;
    }

    return field;
  });

  return {
    ...formattedMember,
    fields: updatedFields,
  };
}

export function formatPolicy(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReadonlyDeep<Policy> {
  const policy = workspace.policy;
  const formattedPolicy = formatPolicyFields(workspace);

  const violationsIds: Record<string, string[] | undefined> = {};

  for (const member of workspace.members) {
    const violations = collectMemberPolicyViolations(workspace, member);

    for (const violation of violations) {
      if (violationsIds[violation.id]) {
        violationsIds[violation.id]?.push(member.id);
      } else {
        violationsIds[violation.id] = [member.id];
      }
    }
  }
  const fields = formattedPolicy.map((policyField): FieldPolicy => {
    if (policyField.id === PresetPolicyId.MANAGER) {
      return {
        ...policyField,
        description: `Having a manager helps to set up a hierarchy on the Org Chart.`,
        violationsCount: violationsIds[PresetPolicyId.MANAGER]?.length ?? 0,
        canEdit: false,
        canToggleRequired: false,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.JOB_TITLE) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.JOB_TITLE]?.length ?? 0,
        canEdit: false,
        canToggleRequired: false,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.PHOTO_URL) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.PHOTO_URL]?.length ?? 0,
        canEdit: false,
        canToggleRequired: true,
        canChangeField: false,
      };
    }

    if (policyField.id === PresetPolicyId.COUNTRY) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.COUNTRY]?.length ?? 0,
        canEdit: false,
        canToggleRequired: true,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.PHONE) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.PHONE]?.length ?? 0,
        canEdit: false,
        canToggleRequired: true,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.BIRTHDAY) {
      return {
        ...policyField,
        description: `The field is used to send automatic birthday congratulations. It can be configured in the settings in the B-days & Anniversaries section.`,
        violationsCount: violationsIds[PresetPolicyId.BIRTHDAY]?.length ?? 0,
        canEdit: false,
        canToggleRequired: true,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.ANNIVERSARY) {
      return {
        ...policyField,
        description: `The field is used to send automatic anniversary congratulations. It can be configured in the settings in the B-days & Anniversaries section.`,
        violationsCount: violationsIds[PresetPolicyId.ANNIVERSARY]?.length ?? 0,
        canEdit: false,
        canToggleRequired: true,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.DEPARTMENT) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.DEPARTMENT]?.length ?? 0,
        canEdit: false,
        canToggleRequired: false,
        canChangeField: true,
      };
    }

    if (policyField.id === PresetPolicyId.TEAMS) {
      return {
        ...policyField,
        description: undefined,
        violationsCount: violationsIds[PresetPolicyId.TEAMS]?.length ?? 0,
        canEdit: false,
        canToggleRequired: false,
        canChangeField: true,
      };
    }

    return {
      ...policyField,
      description: undefined,
      violationsCount: violationsIds[policyField.id]?.length ?? 0,
      canEdit: true,
      canToggleRequired: true,
      canChangeField: true,
    };
  });

  return {
    id: workspace.id,
    fields,
    notifications: {
      enable: policy.notifications.enable,
      welcomeMessage: policy.notifications.welcomeMessage,
      followUpMessage: policy.notifications.followUpMessage,
      schedule: policy.notifications.schedule,
    },
  };
}

export function formatRootNode(
  rootNode: ReadonlyDeep<DomainRootNode>,
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  withSubordinates = false
): ReadonlyDeep<RootNode> {
  const { assignedPositionNumber, allPositionNumber } = getSubordinatesInfo(
    rootNode.subordinates
  );

  const subordinates = rootNode.subordinates.map((subordinate) =>
    subordinate.type === "position"
      ? formatPosition(workspace, session, subordinate, withSubordinates)
      : formatDepartment(workspace, session, subordinate, withSubordinates)
  );

  return {
    id: rootNode.id,
    type: "root",
    title: workspace.name,
    logoUrl: workspace.iconUrl ?? "",
    editable: canEditWorkspace(workspace, session),
    subordinates,
    subordinatesNumber: rootNode.subordinates.length,
    assignedPositionNumber,
    allPositionNumber,
  };
}

export function formatDepartment(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  department: ReadonlyDeep<DomainDepartment>,
  includeSubordinates = false
): ReadonlyDeep<Department> {
  const { assignedPositionNumber, allPositionNumber } = getSubordinatesInfo(
    department.subordinates
  );
  const formattedSubordinates = includeSubordinates
    ? department.subordinates.map((subordinate) =>
        subordinate.type === "department"
          ? formatDepartment(workspace, session, subordinate)
          : formatPosition(workspace, session, subordinate)
      )
    : null;

  return {
    id: department.id,
    timestamp: department.timestamp,
    type: "department",
    parentId: department.parentId,
    managerId: department.managerId,
    title: department.title,
    departmentColor: department.color,
    subordinates: formattedSubordinates,
    subordinatesNumber: department.subordinates.length,
    assignedPositionNumber,
    allPositionNumber,
    editable: canEditWorkspace(workspace, session),
  };
}

export function formatPosition(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  position: ReadonlyDeep<DomainPosition>,
  includeSubordinates = false,
  formatters: {
    formatMember: typeof formatMember;
    formatDepartment: typeof formatDepartment;
  } = {
    formatMember,
    formatDepartment,
  }
): ReadonlyDeep<Position> {
  const currentMember = workspace.members.find(
    (member) => member.id === position.memberId
  );
  const department = getParentDepartment(workspace, position.id);
  const { assignedPositionNumber, allPositionNumber } = getSubordinatesInfo(
    position.subordinates
  );
  const formattedSubordinates = includeSubordinates
    ? position.subordinates.map((subordinate) =>
        subordinate.type === "department"
          ? formatters.formatDepartment(workspace, session, subordinate)
          : formatPosition(workspace, session, subordinate)
      )
    : null;

  const {
    bonusDescription = "",
    isIncludeToReferrals = false,
    jobDescriptionLink = "",
    candidates = [],
    hiringManagerId,
  } = position.reference ?? {};

  const permission = hasPermission(workspace, session, position);

  let formattedPosition: Position = {
    id: position.id,
    timestamp: position.timestamp,
    type: "position",
    parentId: position.parentId,
    member:
      currentMember &&
      formatters.formatMember({
        workspace,
        session,
        member: currentMember,
        permission,
      }),
    title: position.title,
    department: department
      ? formatters.formatDepartment(workspace, session, department)
      : undefined,
    subordinates: formattedSubordinates,
    subordinatesNumber: position.subordinates.length,
    assignedPositionNumber,
    allPositionNumber,
    teams: position.teamIds
      .map((teamId) => {
        const team = workspace.teams.find((t) => t.id === teamId);

        assert(
          team,
          `Team with id "${teamId}" does not exist at workspace "${workspace.id}"`
        );

        return team;
      })
      .map((team) => formatTeam(workspace, session, team, false)),
    withoutManagerManual: position.withoutManagerManual,
    editable: permission,
    assignable: canEditWorkspace(workspace, session),
    reference: {
      isIncludeToReferrals,
      bonusDescription,
      jobDescriptionLink,
      candidates,
      hiringManagerId,
    },
    managedDepartmentId: position.managedDepartmentId,
  };

  if (formattedPosition.member) {
    formattedPosition = {
      ...formattedPosition,
      member: {
        ...formattedPosition.member,
        position: formattedPosition,
      },
    };
  }

  return formattedPosition;
}

export function canEditWorkspace(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): boolean {
  const member = workspace.members.find((m) => m.id === session.memberId);

  assert(member, `Expected member with id "${session.memberId}" to exist`);

  return workspace.billing.subscription.ok && member.isAdmin;
}

export function hasPermission(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  position?: ReadonlyDeep<DomainPosition>,
  memberId?: string
): boolean {
  if (!workspace.billing.subscription.ok) {
    return false;
  }

  const me = workspace.members.find((m) => {
    return m.id === session.memberId;
  });

  assert(me, `Expected me to exist (id: "${session.memberId}`);
  const currentMember = position?.memberId ?? memberId;

  if (!currentMember) {
    return me.isAdmin;
  }

  const member = workspace.members.find((x) => x.id === currentMember);

  assert(member, `Expected member to exist (id: "${currentMember}")`);

  if (member.id !== me.id && !me.isAdmin) {
    return false;
  }

  return true;
}

export function formatTeam(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  team: DomainTeam,
  includePositions: boolean
): ReadonlyDeep<Team> {
  const positions = getNodes(
    workspace.orgTree.rootNode,
    (n): n is DomainPosition =>
      n.type === "position" && n.teamIds.includes(team.id)
  );
  let formattedPositions: ReadonlyDeep<Position>[] = [];

  if (includePositions) {
    formattedPositions = positions.map((p) =>
      formatPosition(workspace, session, p, true)
    );

    formattedPositions = sortBy(formattedPositions, (pos) =>
      pos.member?.realName?.toLowerCase()
    );
  }

  return {
    ...team,
    positions: formattedPositions,
    allPositionNumber: positions.length,
    assignedPositionNumber: positions.filter(({ memberId }) =>
      Boolean(memberId)
    ).length,
  };
}

export const domainDayOfWeekToGraphQlDayOfWeek: Record<
  DomainDayOfWeek,
  DayOfWeek
> = {
  [DomainDayOfWeek.MONDAY]: DayOfWeek.Monday,
  [DomainDayOfWeek.TUESDAY]: DayOfWeek.Tuesday,
  [DomainDayOfWeek.WEDNESDAY]: DayOfWeek.Wednesday,
  [DomainDayOfWeek.THURSDAY]: DayOfWeek.Thursday,
  [DomainDayOfWeek.FRIDAY]: DayOfWeek.Friday,
  [DomainDayOfWeek.SATURDAY]: DayOfWeek.Saturday,
  [DomainDayOfWeek.SUNDAY]: DayOfWeek.Sunday,
};

export const graphQlDayOfWeekToDomainDayOfWeek: Record<
  DayOfWeek,
  DomainDayOfWeek
> = {
  [DayOfWeek.Monday]: DomainDayOfWeek.MONDAY,
  [DayOfWeek.Tuesday]: DomainDayOfWeek.TUESDAY,
  [DayOfWeek.Wednesday]: DomainDayOfWeek.WEDNESDAY,
  [DayOfWeek.Thursday]: DomainDayOfWeek.THURSDAY,
  [DayOfWeek.Friday]: DomainDayOfWeek.FRIDAY,
  [DayOfWeek.Saturday]: DomainDayOfWeek.SATURDAY,
  [DayOfWeek.Sunday]: DomainDayOfWeek.SUNDAY,
};
