import assert from "assert";

import {
  <PERSON>only<PERSON>eep,
  Notification as DomainNotification,
  NotificationTemplate as DomainNotificationTemplate,
  Workspace as DomainWorkspace,
  NotificationBlockType as DomainNotificationBlockType,
  isTextBlock,
  isButtonBlock,
  isTimeOffsBlock,
  isCelebrationsBlock,
  isHolidaysBlock,
  ButtonBlockData,
  TimeOffsBlockData,
  CelebrationsBlockData,
  HolidaysBlockData,
  ButtonBlockType,
} from "@organice/core/domain";
import {
  toggleNotificationActivity as domainToggleNotificationActivity,
  deleteNotification as domainDeleteNotification,
  addNotification as domainAddNotification,
  updateNotification as domainUpdateNotification,
  NotificationInput as DomainNotificationInput,
  NotificationBlockInput as DomainNotificationBlockInput,
  formatNotificationBlockData,
  getNotificationRangeBasedOnFrequency,
  parseNotificationBlockInput,
} from "@organice/core/domain/notifications";

import { Session, getSessionWorkspace } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  type NotificationBlockResolvers,
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import {
  Notification,
  NotificationTemplate,
  NotificationInput,
  NotificationBlockInput,
  TimeOffRequestStatus,
  NotificationBlockType,
  NotificationBlock as GraphQlNotificationBlock,
} from "../../server.generated";

import { Context, formatMember } from "./_common";

export const NotificationBlock: NotificationBlockResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if (obj.type === NotificationBlockType.Button) {
      return "ButtonBlock";
    }

    if (obj.type === NotificationBlockType.TimeOffs) {
      return "TimeOffsBlock";
    }

    if (obj.type === NotificationBlockType.Celebrations) {
      return "CelebrationsBlock";
    }

    if (obj.type === NotificationBlockType.Holidays) {
      return "HolidaysBlock";
    }

    return "TextBlock";
  },
};

export const Query = {
  async notificationTemplates(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return workspace.notificationTemplates.map(formatNotificationTemplate);
  },

  async notifications(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return workspace.notifications
      .map((notification) =>
        formatNotification({
          workspace,
          session,
          notification,
        })
      )
      .sort((a, b) => {
        return a.isActive === b.isActive ? 0 : a.isActive ? -1 : 1;
      });
  },

  async notification(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const notification = workspace.notifications.find((n) => n.id === id);

    assert(notification, `Expect notification with id ${id} to exist`);

    return formatNotification({
      workspace,
      session,
      notification,
    });
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async deleteNotification(_parent, { id }, { logger, repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = domainDeleteNotification(workspace, logger, id);

    await repository.setWorkspace(workspace);
  },

  async postNotificationPreview(_parent, { data }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const sessionMember = workspace.members.find(
      (m) => m.id === session.memberId
    );

    assert(sessionMember, "Expect session member to exist");
    const now = new Date();

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);
    const range = getNotificationRangeBasedOnFrequency(data.frequency, now);
    const blocks = formatNotificationBlockData(
      workspace,
      data.blocks
        .map(formatNotificationBlockInput)
        .map(parseNotificationBlockInput),
      range
    );

    await slackAdapter.postNotification(sessionMember.id, blocks, "Preview", {
      workspace,
    });
  },

  async addNotification(_parent, { data }, { logger, repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const sessionMember = workspace.members.find(
      (m) => m.id === session.memberId
    );
    const now = new Date();

    assert(sessionMember, "Expect session member to exist");

    const addResult = domainAddNotification(
      workspace,
      logger,
      sessionMember,
      convertAPIInput(data),
      now
    );

    workspace = addResult[0];

    await repository.setWorkspace(workspace);

    const notification = addResult[1];

    return formatNotification({
      workspace,
      session,
      notification,
    });
  },

  async updateNotification(
    _parent,
    { id, data },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const notification = workspace.notifications.find((n) => n.id === id);
    const now = new Date();

    assert(notification, `Expect notification with id ${id} to exist`);

    const updateResult = domainUpdateNotification(
      workspace,
      logger,
      notification,
      convertAPIInput(data),
      now
    );

    workspace = updateResult[0];

    await repository.setWorkspace(workspace);

    const updatedNotification = updateResult[1];

    return formatNotification({
      workspace,
      session,
      notification: updatedNotification,
    });
  },

  async toggleNotificationActivity(
    _parent,
    { id },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    const notification = workspace.notifications.find((n) => n.id === id);

    assert(notification, `Expect notification with id ${id} to exist`);

    const toggleResult = domainToggleNotificationActivity(
      workspace,
      logger,
      notification
    );

    workspace = toggleResult[0];

    await repository.setWorkspace(workspace);

    const toggledNotification = toggleResult[1];

    return formatNotification({
      workspace,
      session,
      notification: toggledNotification,
    });
  },
} satisfies MutationResolvers<Context>;

function convertAPIInput(data: NotificationInput): DomainNotificationInput {
  return {
    title: data.title,
    blocks: data.blocks.map(formatNotificationBlockInput),
    settings: {
      frequency: data.settings.frequency,
      day: data.settings.day ?? undefined,
      time: data.settings.time,
      timezone: data.settings.timezone,
      channelId: data.settings.channelId,
    },
  };
}

function formatNotificationBlockInput(
  block: NotificationBlockInput
): DomainNotificationBlockInput {
  if ("text" in block && block.text) {
    return {
      type: DomainNotificationBlockType.text,
      data: {
        text: block.text.text,
      },
    };
  }

  if ("button" in block && block.button) {
    return {
      type: DomainNotificationBlockType.button,
      data: {
        type: block.button.buttonType as unknown as ButtonBlockType,
        title: block.button.title,
        url: block.button.url,
      } as ButtonBlockData,
    };
  }

  if ("timeOffs" in block && block.timeOffs) {
    return {
      type: DomainNotificationBlockType.timeOffs,
      data: {
        managers: block.timeOffs.managers,
        status: block.timeOffs.status,
        departments: block.timeOffs.departments,
        teams: block.timeOffs.teams,
        countries: block.timeOffs.countries,
        types: block.timeOffs.types,
      } as TimeOffsBlockData,
    };
  }

  if ("celebrations" in block && block.celebrations) {
    return {
      type: DomainNotificationBlockType.celebrations,
      data: {
        managers: block.celebrations.managers,
        departments: block.celebrations.departments,
        teams: block.celebrations.teams,
        countries: block.celebrations.countries,
        type: block.celebrations.type,
      } as CelebrationsBlockData,
    };
  }

  if ("holidays" in block && block.holidays) {
    return {
      type: DomainNotificationBlockType.holidays,
      data: {
        countries: block.holidays.countries,
        includeDescription: block.holidays.includeDescription,
        type: block.holidays.type,
      } as HolidaysBlockData,
    };
  }

  throw new Error(`Unexpected block type`);
}

function formatBlocks(
  blocks: ReadonlyDeep<DomainNotification["blocks"]>
): ReadonlyDeep<GraphQlNotificationBlock[]> {
  return blocks.map((block) => {
    if (isTextBlock(block)) {
      return {
        type: NotificationBlockType.Text,
        text: block.data.text,
      };
    }

    if (isButtonBlock(block)) {
      return {
        type: NotificationBlockType.Button,
        buttonType: block.data.type,
        title: block.data.title,
        url: block.data.url,
      };
    }

    if (isTimeOffsBlock(block)) {
      return {
        type: NotificationBlockType.TimeOffs,
        status: block.data.status as TimeOffRequestStatus,
        managers: block.data.managers,
        departments: block.data.departments,
        teams: block.data.teams,
        countries: block.data.countries,
        types: block.data.types,
      };
    }

    if (isCelebrationsBlock(block)) {
      return {
        type: NotificationBlockType.Celebrations,
        managers: block.data.managers,
        departments: block.data.departments,
        teams: block.data.teams,
        countries: block.data.countries,
        celebrationsType: block.data.type,
      };
    }

    if (isHolidaysBlock(block)) {
      return {
        type: NotificationBlockType.Holidays,
        countries: block.data.countries,
        includeDescription: block.data.includeDescription,
        holidaysType: block.data.type,
      };
    }

    throw new Error(`Unexpected block type ${block.type}`);
  });
}

function formatNotificationTemplate(
  template: ReadonlyDeep<DomainNotificationTemplate>
): ReadonlyDeep<NotificationTemplate> {
  return {
    id: template.id,
    title: template.title,
    description: template.description,
    blocks: formatBlocks(template.blocks),
    settings: {
      frequency: template.settings.frequency,
      day: template.settings.day,
      time: template.settings.time,
    },
  };
}

function formatNotification({
  workspace,
  session,
  notification,
}: {
  workspace: ReadonlyDeep<DomainWorkspace>;
  session: Session;
  notification: ReadonlyDeep<DomainNotification>;
}): ReadonlyDeep<Notification> {
  const channel = notification.settings.channelId
    ? workspace.channels.find(
        (c) => c.id === notification.settings.channelId
      ) ?? null
    : null;
  const creator = workspace.members.find(
    (m) => m.id === notification.createdById
  );

  assert(
    creator,
    `Expected member with id ${notification.createdById} to exist`
  );

  return {
    id: notification.id,
    title: notification.title,
    isActive: notification.isActive,
    createdBy: formatMember({
      workspace,
      session,
      member: creator,
    }),
    blocks: formatBlocks(notification.blocks),
    settings: {
      frequency: notification.settings.frequency,
      day: notification.settings.day,
      time: notification.settings.time,
      timezone: notification.settings.timezone,
      channel,
    },
  };
}
