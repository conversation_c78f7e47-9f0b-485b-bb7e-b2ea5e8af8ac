import assert from "assert";
import crypto from "crypto";

import {
  Announcement as AnnouncementDomain,
  ReadonlyDeep,
  Workspace,
} from "@organice/core/domain";

import { getSessionWorkspace, Session } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import type {
  Announcement,
  AnnouncementType,
  QueryResolvers,
  MutationResolvers,
} from "../../server.generated";

import { formatMember, Context } from "./_common";

export const Query = {
  async announcements(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatAnnouncements(workspace, session);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async announce(_parent, args, { logger, repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);
    const channel = workspace.channels.find((ch) => ch.id === args.channel);

    assert(channel, "Expect channel is exist");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    logger = logger.withContext({
      workspace,
      announcement: {
        type: args.type,
      },
    });

    const sentMessage = await slackAdapter.postMessage(
      channel.id,
      args.message
    );

    assert(sentMessage.ok && sentMessage.ts, "Expect sentMessage to work");

    const sentAt = new Date(Number(sentMessage.ts.split(".")[0]) * 1000);

    const newAnnouncement: AnnouncementDomain = {
      id: crypto.randomBytes(10).toString("hex"),
      message: args.message,
      type: args.type,
      channelId: channel.id,
      memberId: session.memberId,
      sentAt,
    };

    workspace = {
      ...workspace,
      announcements: [...workspace.announcements, newAnnouncement],
    };

    await repository.setWorkspace(workspace);
    logger.info("Announcement created");

    return formatAnnouncement(workspace, session, newAnnouncement);
  },
} satisfies MutationResolvers<Context>;

function formatAnnouncements(
  workspace: ReadonlyDeep<Workspace>,
  session: Session
): ReadonlyDeep<Announcement[]> {
  return workspace.announcements
    .filter(
      (value) =>
        value.type === "INTRODUCE_CELEBRATIONS" ||
        value.type === "INTRODUCE_HIRING" ||
        value.type === "INTRODUCE_KUDOS" ||
        value.type === "INTRODUCE_ORG_CHART" ||
        value.type === "INTRODUCE_TIME_OFFS"
    )
    .map((announcement) =>
      formatAnnouncement(workspace, session, announcement)
    );
}

function formatAnnouncement(
  workspace: ReadonlyDeep<Workspace>,
  session: Session,
  announcement: ReadonlyDeep<AnnouncementDomain>
): ReadonlyDeep<Announcement> {
  const { channels, members } = workspace;
  const member = announcement.memberId
    ? members.find((m) => m.id === announcement.memberId)
    : null;
  const channel = channels.find((c) => c.id === announcement.channelId);

  assert(channel, "Expect channel is exist");

  if (announcement.memberId) {
    assert(member, "Expect member is exist");
  }

  return {
    id: announcement.id,
    channel,
    message: announcement.message,
    sentAt: announcement.sentAt
      ? new Date(announcement.sentAt as Date).toISOString()
      : undefined,
    type: announcement.type as AnnouncementType,
    member: member
      ? formatMember({
          workspace,
          session,
          member,
        })
      : null,
  };
}
