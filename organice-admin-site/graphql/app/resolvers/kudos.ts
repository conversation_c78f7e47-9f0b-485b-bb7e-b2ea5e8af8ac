import assert from "assert";

import {
  ReadonlyDeep,
  Workspace,
  HomePages,
  addOrUpdateKudosValue,
  deleteKudosValue,
} from "@organice/core/domain";
import { getCurrentCycleStart } from "@organice/core/domain/kudos";

import { getSessionWorkspace } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  QueryResolvers,
  MutationResolvers,
  KudosDestinationFilter,
  KudosSettings,
} from "../../server.generated";

import { Context } from "./_common";

export const Query = {
  async kudosSettings(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatKudosSettings(workspace);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async openKudosInSlackApp(_parent, args, { logger, repository, session }) {
    const destination = args.destination;
    const workspace = await getSessionWorkspace(repository, session);

    // eslint-disable-next-line no-param-reassign
    logger = logger.withContext({ workspace });

    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    logger.info(`Member opened ${destination} kudos in slack app`);
    assert(member, `Expected member with id "${session.memberId}" to exist`);

    const kudosPage =
      destination === KudosDestinationFilter.Given
        ? "given_kudos"
        : "received_kudos";

    await slackAdapter.renderActiveTab(workspace, member, null, (pages) =>
      pages[HomePages.Kudos](kudosPage)
    );
  },

  async setKudosSettings(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const channel = workspace.channels.find((c) => c.id === args.channelId);

    workspace = {
      ...workspace,
      kudosSettings: {
        enable: args.enable,
        channel,
        kudosLimit: args.limit,
        resetFrequency: args.resetFrequency,
        resetDay: args.resetDay,
        resetTime: args.resetTime,
        resetTimezone: args.resetTimezone,
        values: workspace.kudosSettings.values,
      },
    };

    await repository.setWorkspace(workspace);

    return formatKudosSettings(workspace);
  },

  async addOrUpdateKudosValue(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = addOrUpdateKudosValue(workspace, {
      id: args.data.id ?? undefined,
      emoji: args.data.emoji,
      title: args.data.title,
      description: args.data.description ?? undefined,
    });

    await repository.setWorkspace(workspace);
  },

  async deleteKudosValue(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = deleteKudosValue(workspace, args);

    await repository.setWorkspace(workspace);
  },
} satisfies MutationResolvers<Context>;

function formatKudosSettings(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<KudosSettings> {
  return {
    id: workspace.id,
    channel: workspace.kudosSettings.channel,
    kudosLimit: workspace.kudosSettings.kudosLimit,
    enable: workspace.kudosSettings.enable,
    resetFrequency: workspace.kudosSettings.resetFrequency,
    resetDay: workspace.kudosSettings.resetDay,
    resetTime: workspace.kudosSettings.resetTime,
    resetTimezone: workspace.kudosSettings.resetTimezone,
    cycleStartDate: getCurrentCycleStart(
      workspace.kudosSettings,
      new Date()
    ).toISOString(),
    values: [...workspace.kudosSettings.values],
  };
}
