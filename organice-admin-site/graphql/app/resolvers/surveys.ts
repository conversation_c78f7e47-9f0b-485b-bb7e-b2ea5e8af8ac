import assert from "assert";
import crypto from "crypto";

import { faker } from "@faker-js/faker";
import {
  HomePages,
  Member as DomainMember,
  ReadonlyDeep,
  ScaleOption,
  Survey as DomainSurvey,
  SurveyAnswer as DomainSurveyAnswer,
  SurveyQuestion as DomainQuestion,
  SurveyTemplate as DomainSurveyTemplate,
  TextOption,
  Workspace as DomainWorkspace,
} from "@organice/core/domain";
import { getSurveyResponders, runSurvey } from "@organice/core/domain/surveys";
import { sortBy } from "lodash";

import { getSessionWorkspace, Session } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  AnswerStats,
  DetailedScaleOption,
  DetailedTextOption,
  type MutationResolvers,
  type QueryResolvers,
  RawTextAnswer,
  Responder,
  Survey,
  SurveyAnswer,
  SurveyQuestion,
  SurveyQuestionType,
  SurveyStatus,
  SurveyStatusFilter,
  SurveyTemplate,
} from "../../server.generated";

import { Context, formatMember } from "./_common";

export const Query = {
  async surveyTemplates(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const sortedTemplates = sortBy(workspace.surveyTemplates, (t) =>
      t.title.toLowerCase()
    );
    const formattedTemplates = sortedTemplates.map((t) =>
      formatSurveyTemplate(workspace, session, t)
    );

    return formattedTemplates;
  },

  async surveys(_parent, args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    let surveys = workspace.surveys;

    const surveyId = args.survey?.surveyId;
    const responderId = args.survey?.responderId;

    if (surveyId) {
      surveys = surveys.filter((s) => s.id === surveyId);
    }

    if (args.survey?.surveyStatusFilter) {
      surveys = surveys.filter((s) =>
        args.survey?.surveyStatusFilter === SurveyStatusFilter.InProgress
          ? s.status === SurveyStatus.InProgress
          : args.survey?.surveyStatusFilter === SurveyStatusFilter.Closed
          ? s.status === SurveyStatus.Closed
          : true
      );
    }
    const formattedSurveys = surveys.map((t) => {
      const survey = formatSurvey(workspace, session, t);

      return {
        ...survey,
        answers: responderId
          ? survey.answers.filter((a) => a.responder?.id === responderId)
          : survey.answers,
      };
    });

    if (responderId) {
      surveys = surveys.map((s) => ({ ...s, responders: s }));
    }

    const sortedSurveys = sortBy(formattedSurveys, (s) =>
      s.title.toLowerCase()
    );

    return sortedSurveys;
  },

  async answersStats(_parent, args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const survey = workspace.surveys.find((s) => s.id === args.surveyId);

    assert(survey, "Survey not found");

    const formattedSurvey = formatSurvey(workspace, session, survey);
    const answersStats: AnswerStats[] = survey.questions.map((question) => {
      const answers = formattedSurvey.answers.filter(
        (a) => a.question.id === question.id
      );
      let totalResponsesNumber = 0;
      let raw: RawTextAnswer[] | undefined;
      let scale: DetailedScaleOption[] | undefined;
      let single: DetailedTextOption[] | undefined;
      let multiple: DetailedTextOption[] | undefined;

      if (question.type === SurveyQuestionType.Text) {
        raw = answers
          .filter((a) => !!a.rawTextValue?.length)
          .map((a) => ({
            text: a.rawTextValue ?? "",
            responder: a.responder,
          }));
        totalResponsesNumber = raw.length;
      }

      if (question.type === SurveyQuestionType.Scale) {
        scale =
          question.scaleOptions?.map((option) => ({
            ...option,
            responsesNumber: answers.filter(
              (a) => a.scaleValue?.id === option.id
            ).length,
          })) ?? [];
        totalResponsesNumber = scale.reduce(
          (acc, curr) => acc + curr.responsesNumber,
          0
        );
      }

      if (question.type === SurveyQuestionType.SingleOption) {
        single =
          question.singleOptions?.map((option) => ({
            ...option,
            responsesNumber: answers.filter(
              (a) => a.singleValue?.id === option.id
            ).length,
          })) ?? [];
        totalResponsesNumber = single.reduce(
          (acc, curr) => acc + curr.responsesNumber,
          0
        );
      }

      if (question.type === SurveyQuestionType.MultipleOptions) {
        multiple =
          question.multipleOptions?.map((option) => ({
            ...option,
            responsesNumber: answers.filter((a) =>
              a.multipleValue?.some((v) => v.id === option.id)
            ).length,
          })) ?? [];
        totalResponsesNumber = multiple.reduce(
          (acc, curr) => acc + curr.responsesNumber,
          0
        );
      }

      const details: AnswerStats = {
        id: question.id,
        questionTitle: question.title,
        totalResponsesNumber,
        rawTextAnswers: raw,
        scaleOptions: scale,
        singleOptions: single,
        multipleOptions: multiple,
      };

      return details;
    });

    return answersStats;
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async createSurveyTemplate(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const newTemplate: DomainSurveyTemplate = {
      id: crypto.randomUUID(),
      title: args.template.title ?? "",
      isDefault: false,
      createdById: session.memberId,
      createdAt: new Date(),
      updatedAt: new Date(),
      questions: args.template.questions.map((q) => {
        return {
          ...q,
          id: crypto.randomUUID(),
          scaleOptions: q.scaleOptions?.map(({ value, label }) => ({
            id: crypto.randomUUID(),
            value,
            label,
          })),
          singleOptions: q.singleOptions?.map((value) => ({
            id: crypto.randomUUID(),
            value,
          })),
          multipleOptions: q.multipleOptions?.map((value) => ({
            id: crypto.randomUUID(),
            value,
          })),
        };
      }),
      usageCount: 0,
    };
    const updatedWorkspace = {
      ...workspace,
      surveyTemplates: [...workspace.surveyTemplates, newTemplate],
    };

    await repository.setWorkspace(updatedWorkspace);

    logger
      .withContext({ workspace, surveyTemplate: newTemplate })
      .info("Created new survey template");

    return formatSurveyTemplate(updatedWorkspace, session, newTemplate);
  },

  async editSurveyTemplate(_parent, args, { logger, repository, session }) {
    const { template } = args;
    const workspace = await getSessionWorkspace(repository, session);
    const currentTemplate = workspace.surveyTemplates.find(
      (t) => t.id === args.template.id
    );

    assert(currentTemplate, "Expected editable template to exist");
    const updatedTemplate: ReadonlyDeep<DomainSurveyTemplate> = {
      ...currentTemplate,
      title: template.title.trim() || "Untitled survey",
      questions: template.questions.map((question) => {
        const scale = question.scaleOptions?.map((option) => ({
          id: option.id ?? crypto.randomUUID(),
          value: option.value,
          label: option.label,
        }));

        const single = question.singleOptions?.map((option) => ({
          id: option.id ?? crypto.randomUUID(),
          value: option.value,
        }));

        const multiple = question.multipleOptions?.map((option) => ({
          id: option.id ?? crypto.randomUUID(),
          value: option.value,
        }));

        return {
          ...question,
          id: question.id ?? crypto.randomUUID(),
          title: question.title,
          scaleOptions: scale,
          singleOptions: single,
          multipleOptions: multiple,
        };
      }),
    };

    const updatedWorkspace = {
      ...workspace,
      surveyTemplates: workspace.surveyTemplates.map((t) => {
        return t.id === currentTemplate.id ? updatedTemplate : t;
      }),
    };

    await repository.setWorkspace(updatedWorkspace);

    logger
      .withContext({ workspace, surveyTemplate: updatedTemplate })
      .info("Edited survey template");

    return formatSurveyTemplate(updatedWorkspace, session, updatedTemplate);
  },

  async copySurveyTemplate(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const originalTemplate = workspace.surveyTemplates.find(
      (t) => t.id === args.templateId
    );

    assert(originalTemplate, "Expected original template to exist");

    const templateCopy: DomainSurveyTemplate = {
      ...originalTemplate,
      id: crypto.randomUUID(),
      isDefault: false,
      title: `(Copy) ${originalTemplate.title}`,
      questions: originalTemplate.questions.map((q) => ({
        ...q,
        id: crypto.randomUUID(),
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
      createdById: session.memberId,
    };
    const updatedTemplates = workspace.surveyTemplates.concat(templateCopy);
    const updatedWorkspace = {
      ...workspace,
      surveyTemplates: updatedTemplates,
    };

    await repository.setWorkspace(updatedWorkspace);

    logger
      .withContext({ workspace, surveyTemplate: templateCopy })
      .info("Copied survey template");

    return formatSurveyTemplate(updatedWorkspace, session, templateCopy);
  },

  async deleteSurveyTemplate(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const updatedWorkspace = {
      ...workspace,
      surveyTemplates: workspace.surveyTemplates.filter(
        (t) => t.id !== args.templateId
      ),
    };

    await repository.setWorkspace(updatedWorkspace);

    logger
      .withContext({ workspace, templateId: args.templateId })
      .info("Removed survey template");
  },

  async openSurveysInSlackApp(_parent, _args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    assert(member, `Expected member with id "${session.memberId}" to exist`);

    await slackAdapter.renderActiveTab(
      workspace,
      member,
      null,
      (pages) => pages[HomePages.Surveys]
    );
    // eslint-disable-next-line no-param-reassign
    logger = logger.withContext({ workspace, member });

    logger.info("Opened surveys in slack from web");
  },

  async toggleSurveyStatus(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const survey = workspace.surveys.find((s) => s.id === args.surveyId);

    assert(survey, "Survey not found");

    const updatedSurvey = {
      ...survey,
      status:
        survey.status === SurveyStatus.InProgress
          ? SurveyStatus.Closed
          : SurveyStatus.InProgress,
    };

    const updatedSurveys = workspace.surveys.map((s) =>
      s.id === updatedSurvey.id ? updatedSurvey : s
    );
    const updatedWorkspace = { ...workspace, surveys: updatedSurveys };

    await repository.setWorkspace(updatedWorkspace);
    // eslint-disable-next-line no-param-reassign
    logger = logger.withContext({ survey: updatedSurvey });

    logger.info(
      `Changed survey status from ${survey.status} to ${updatedSurvey.status}`
    );

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);
    const channel = workspace.channels.find(
      (c) => c.id === updatedSurvey.channelId
    );

    if (channel) {
      if (updatedSurvey.status === SurveyStatus.InProgress) {
        await slackAdapter.sendSurveyReminder(updatedSurvey);
        logger.info("Notified channel survey reopened");
      }
    }

    return formatSurvey(workspace, session, updatedSurvey);
  },

  async runSurvey(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const member = workspace.members.find((x) => x.id === session.memberId);

    assert(member, `Member not found`);

    const [updatedWorkspace, newSurvey] = await runSurvey(
      workspace,
      member,
      {
        origin: "admin-site",
        surveyTitle: args.survey.surveyTitle,
        templateId: args.survey.templateId ?? undefined,
        channelId: args.survey.channelId,
        isAnonymous: args.survey.isAnonymous,
        questions: args.survey.questions,
      },
      logger,
      getSlackAdapter(workspace.slackBotToken)
    );

    await repository.setWorkspace(updatedWorkspace);

    return formatSurvey(workspace, session, newSurvey);
  },

  async deleteSurvey(_parent, args, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const updatedWorkspace = {
      ...workspace,
      surveys: workspace.surveys.filter((s) => s.id !== args.surveyId),
    };

    await repository.setWorkspace(updatedWorkspace);
    const survey = workspace.surveys.find((s) => s.id === args.surveyId);

    assert(survey, "Survey not found");
    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(member, `Expected member with id "${session.memberId}" to exist`);

    logger.withContext({ survey, member }).info("Member deleted survey");
    const slackAdapter = getSlackAdapter(workspace.slackBotToken);
    const channel = workspace.channels.find((c) => c.id === survey.channelId);

    if (channel) {
      await slackAdapter.updateSurveyMessageOnDelete(survey);
    }
  },
} satisfies MutationResolvers<Context>;

function formatSurveyTemplate(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  template: ReadonlyDeep<DomainSurveyTemplate>
): ReadonlyDeep<SurveyTemplate> {
  const createdBy = workspace.members.find(
    (m) => m.id === template.createdById
  );
  const formattedTemplate = {
    ...template,
    createdAt: template.createdAt?.toISOString() ?? null,
    updatedAt: template.updatedAt?.toISOString() ?? null,
    createdBy: createdBy
      ? formatMember({ workspace, session, member: createdBy })
      : null,
  };

  return formattedTemplate;
}

function formatSurvey(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  survey: ReadonlyDeep<DomainSurvey>
): ReadonlyDeep<Survey> {
  const responders = getSurveyResponders(workspace, survey);
  const completionRate = Math.round(
    survey.participantsCount
      ? (responders.length / survey.participantsCount) * 100
      : 0
  );

  let template: ReadonlyDeep<DomainSurveyTemplate> | null = null;

  if (survey.template) {
    template = survey.template;
  } else if (survey.defaultTemplateId) {
    template =
      workspace.surveyTemplates.find(
        (t) => t.isDefault && t.id === survey.defaultTemplateId
      ) ?? null;
  }

  const channel = workspace.channels.find((c) => c.id === survey.channelId);
  const createdBy = workspace.members.find((m) => m.id === survey.createdById);

  const formattedSurvey: Survey = {
    ...survey,
    template: template
      ? formatSurveyTemplate(workspace, session, template)
      : null,
    createdAt: survey.createdAt.toISOString(),
    updatedAt: survey.updatedAt.toISOString(),
    createdBy: createdBy
      ? formatMember({ workspace, session, member: createdBy })
      : null,
    questions: survey.questions.map(formatSurveyQuestion),
    answers: survey.answers.map((a) =>
      formatSurveyAnswer(workspace, session, survey, a)
    ),
    responders: responders.map((p) =>
      formatResponder(workspace, session, survey, p)
    ),
    completionRate,
    questionsNumber: survey.questions.length,
    participantsCount: survey.participantsCount,
    channelName: channel?.name ?? null,
  };

  return formattedSurvey;
}

function formatSurveyAnswer(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  survey: ReadonlyDeep<DomainSurvey>,
  surveyAnswer: ReadonlyDeep<DomainSurveyAnswer>
): ReadonlyDeep<SurveyAnswer> {
  const { value } = surveyAnswer;
  let rawTextValue: string | undefined;
  let scaleValue: ScaleOption | undefined;
  let singleValue: TextOption | undefined;
  let multipleValue: TextOption[] | undefined;

  if (value) {
    if (typeof value === "string") {
      rawTextValue = value;
    } else if (Array.isArray(value)) {
      multipleValue = value;
    } else if (typeof (value as ScaleOption | TextOption).value === "string") {
      singleValue = value as TextOption;
    } else if (typeof (value as ScaleOption | TextOption).value === "number") {
      scaleValue = value as ScaleOption;
    }
  }

  const responder = surveyAnswer.responderId
    ? workspace.members.find((m) => m.id === surveyAnswer.responderId)
    : null;
  const question = survey.questions.find(
    (q) => q.id === surveyAnswer.questionId
  );

  assert(question, "Question not found");

  const formattedSurveyAnswer: ReadonlyDeep<SurveyAnswer> = {
    ...surveyAnswer,
    responder: responder
      ? formatResponder(workspace, session, survey, responder)
      : null,
    question,
    rawTextValue,
    scaleValue,
    singleValue,
    multipleValue,
  };

  return formattedSurveyAnswer;
}

function formatResponder(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session,
  survey: ReadonlyDeep<DomainSurvey>,
  member: ReadonlyDeep<DomainMember>
): ReadonlyDeep<Responder> {
  const formattedMember = formatMember({ workspace, session, member });

  return survey.isAnonymous
    ? {
        id: formattedMember.id,
        title: faker.person.jobTitle(),
        photo72Url: null,
        realName: faker.person.fullName(),
      }
    : {
        id: formattedMember.id,
        title: formattedMember.title,
        photo72Url: formattedMember.photo72Url,
        realName: formattedMember.realName,
      };
}

function formatSurveyQuestion(
  question: ReadonlyDeep<DomainQuestion>
): ReadonlyDeep<SurveyQuestion> {
  return {
    id: question.id,
    title: question.title,
    type: question.type,
    required: question.required,
  };
}
