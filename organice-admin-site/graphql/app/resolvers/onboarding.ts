import assert from "assert";
import crypto from "crypto";

import {
  Workspace as DomainWorkspace,
  Member as DomainMember,
  Position as DomainPosition,
  PresetPolicyId,
  ReadonlyDeep,
  SupportedFeature,
} from "@organice/core/domain";
import {
  getDefaultKudosHomePageState,
  getDefaultOrgChartHomePageState,
} from "@organice/slack-bot/slack/components/AppHome";

import { getSessionWorkspace } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  type QueryResolvers,
  type MutationResolvers,
  type Onboarding,
} from "../../server.generated";

import { Context } from "./_common";

export const Query = {
  async onboarding(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatOnboarding(workspace);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async updateBookmarkedFeatures(
    _parent,
    { features },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      onboarding: {
        ...workspace.onboarding,
        bookmarkedFeatures: features,
      },
    };

    await repository.setWorkspace(workspace);

    return formatOnboarding(workspace);
  },

  async confirmOnboarding(_parent, _args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(member, `Expected member with id "${session.memberId}" to exist`);

    workspace = {
      ...workspace,
      members: workspace.members.map((m) => ({
        ...m,
        isAdmin: m.id === member.id,
      })),
      onboarding: {
        ...workspace.onboarding,
        completed: true,
        finishedAt: new Date().getTime(),
        finishedBy: session.memberId,
      },
    };

    workspace = await addFirstUserToOrgTree(workspace, member);

    await repository.setWorkspace(workspace);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    if (
      workspace.onboarding.bookmarkedFeatures.includes(
        SupportedFeature.OrgChart
      )
    ) {
      await slackAdapter.renderOrgChartHomeTab(
        workspace,
        member,
        getDefaultOrgChartHomePageState()
      );
    } else if (
      workspace.onboarding.bookmarkedFeatures.includes(SupportedFeature.Kudos)
    ) {
      await slackAdapter.renderKudosHomeTab(
        workspace,
        member,
        getDefaultKudosHomePageState()
      );
    } else {
      await slackAdapter.renderOrgChartHomeTab(
        workspace,
        member,
        getDefaultOrgChartHomePageState()
      );
    }

    return formatOnboarding(workspace);
  },
} satisfies MutationResolvers<Context>;

function formatOnboarding(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReadonlyDeep<Onboarding> {
  return {
    completed: workspace.onboarding.completed,
    bookmarkedFeatures: workspace.onboarding.bookmarkedFeatures,
  };
}

async function addFirstUserToOrgTree(
  workspace: ReadonlyDeep<DomainWorkspace>,
  member: ReadonlyDeep<DomainMember>
): Promise<ReadonlyDeep<DomainWorkspace>> {
  const policy = workspace.policy;

  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  const slackAdapter = getSlackAdapter(workspace.slackBotToken);

  const userProfile = await slackAdapter.getMemberCustomFields(
    workspace.id,
    member.id,
    workspace.policy
  );

  const managerFieldSlackId = policy[PresetPolicyId.MANAGER].slackFieldId;
  const titleFieldSlackId = policy[PresetPolicyId.JOB_TITLE].slackFieldId;
  const managerId = managerFieldSlackId && userProfile[managerFieldSlackId];
  const userNode: DomainPosition = {
    id: crypto.randomBytes(10).toString("hex"),
    timestamp: Date.now(),
    type: "position",
    parentId: workspace.orgTree.rootNode.id,
    memberId: member.id,
    withoutManagerManual: false,
    title: (titleFieldSlackId && userProfile[titleFieldSlackId]) ?? "",
    subordinates: [],
    teamIds: [],
    reference: {
      isIncludeToReferrals: false,
      bonusDescription: "",
      jobDescriptionLink: "",
      candidates: [],
    },
    managedDepartmentId: null,
    managerTeamIds: [],
  };

  if (managerId) {
    const manager = workspace.members.find((m) => m.id === managerId);

    assert(manager, "Manager not found");

    const managerProfile = await slackAdapter.getMemberCustomFields(
      workspace.id,
      manager.id,
      workspace.policy
    );

    const managerNodeId = crypto.randomBytes(10).toString("hex");
    const newParentNode: DomainPosition = {
      id: managerNodeId,
      timestamp: Date.now(),
      type: "position",
      parentId: workspace.orgTree.rootNode.id,
      memberId: manager.id,
      withoutManagerManual: false,
      title: (titleFieldSlackId && managerProfile[titleFieldSlackId]) ?? "",
      subordinates: [],
      teamIds: [],
      reference: {
        isIncludeToReferrals: false,
        bonusDescription: "",
        jobDescriptionLink: "",
        candidates: [],
      },
      managedDepartmentId: null,
      managerTeamIds: [],
    };

    workspace = {
      ...workspace,
      orgTree: {
        rootNode: {
          ...workspace.orgTree.rootNode,
          subordinates: [
            ...workspace.orgTree.rootNode.subordinates,
            {
              ...newParentNode,
              subordinates: [{ ...userNode, parentId: managerNodeId }],
            },
          ],
        },
      },
    };
    const updatedMembers = workspace.members.map((currentMember) => {
      if (currentMember.id === member.id) {
        return {
          ...currentMember,
          botState: {
            ...currentMember.botState,
            wasAskedAboutManager: true,
          },
        };
      }

      return currentMember;
    });

    return {
      ...workspace,
      members: updatedMembers,
    };
  }

  workspace = {
    ...workspace,
    orgTree: {
      rootNode: {
        ...workspace.orgTree.rootNode,
        subordinates: [...workspace.orgTree.rootNode.subordinates, userNode],
      },
    },
  };

  return workspace;
}
