import assert from "assert";
import crypto from "crypto";

import {
  ReadonlyDeep,
  Workspace as DomainWorkspace,
  Link as DomainLink,
  Position,
  Department,
} from "@organice/core/domain";
import { getNodes, getNode } from "@organice/core/domain/org-chart";
import { uniq } from "lodash";

import { getSessionWorkspace } from "../../../domain";
import {
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import { Link } from "../../server.generated";

import { Context, getDataField } from "./_common";

export const Query = {
  async links(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return workspace.links.map((link) => formatLink(workspace, link));
  },

  async link(_parent, { id: linkId }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const link = workspace.links.find((l) => l.id === linkId);

    assert(link, "Sharing link not found");

    return formatLink(workspace, link);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async toggleNodeVisibility(
    _parent,
    { linkId, id: nodeId },
    { logger, repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const { links } = workspace;
    const link = links.find((l) => l.id === linkId);
    let visibleNodes;

    assert(link, "Sharing link not found");

    logger = logger.withContext({
      workspace,
    });

    const node = getNode(
      workspace.orgTree.rootNode,
      (n): n is Position | Department => n.id === nodeId
    );

    assert(node, "Node not found");

    const subNodes = getNodes(node, (_n): _n is Position | Department => true);
    const subNodesIds = subNodes.map((n) => n.id);

    if (link.visibleNodes.includes(nodeId)) {
      visibleNodes = link.visibleNodes.filter(
        (id) => id !== nodeId && !subNodesIds.includes(id)
      );
    } else {
      visibleNodes = [...link.visibleNodes, nodeId, ...subNodesIds];
    }

    const updatedLink = {
      ...link,
      visibleNodes: uniq(visibleNodes),
      updatedAt: new Date(),
    };

    await repository.setWorkspace({
      ...workspace,
      links: [...links.filter((l) => l.id !== linkId), updatedLink],
    });
    logger.info("Shared node visibility changed");

    return formatLink(workspace, updatedLink);
  },

  async share(_parent, { title }, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const { links } = workspace;

    logger = logger.withContext({
      workspace,
    });

    const link = {
      id: crypto.randomBytes(10).toString("hex"),
      title,
      expandedNodes: [],
      visibleNodes: [],
      visibleFields: DEFAULT_VISIBLE_FIELDS,
      visibleViews: DEFAULT_VISIBLE_VIEWS,
      chartLayout: { ...workspace.chartLayout },
    };

    await repository.setWorkspace({
      ...workspace,
      links: [...links, link],
    });
    logger.info("Sharing link created");

    return formatLink(workspace, link);
  },

  async updateLink(
    _parent,
    { id: linkId, link: linkData },
    { logger, repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const { links } = workspace;
    const link = links.find((l) => l.id === linkId);

    assert(link, "Sharing link not found");

    logger = logger.withContext({
      workspace,
    });

    const updatedLink = {
      ...link,
      title: linkData.title ?? link.title,
      visibleFields: linkData.visibleFields
        ? linkData.visibleFields
        : link.visibleFields,
      visibleViews: linkData.visibleViews
        ? linkData.visibleViews
        : link.visibleViews,
      updatedAt: new Date(),
    };

    await repository.setWorkspace({
      ...workspace,
      links: [...links.filter((l) => l.id !== linkId), updatedLink],
    });
    logger.info("Sharing link updated");

    return formatLink(workspace, updatedLink);
  },

  async syncExpandedNodes(_parent, { linkId, nodes }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const { links } = workspace;
    const link = links.find((l) => l.id === linkId);

    assert(link, "Sharing link not found");

    const updatedLink = {
      ...link,
      expandedNodes: uniq(nodes),
    };

    await repository.setWorkspace({
      ...workspace,
      links: [...links.filter((l) => l.id !== linkId), updatedLink],
    });

    return formatLink(workspace, updatedLink);
  },

  async updateLinkChartLayout(
    _parent,
    { linkId, layout },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    const link = workspace.links.find((l) => l.id === linkId);

    assert(link, `Link with id ${linkId} not found`);

    const updatedLink = {
      ...link,
      chartLayout: {
        ...link.chartLayout,
        clusterHorizontal: layout.clusterHorizontal,
        dataField: layout.dataField ?? "",
      },
    };

    workspace = {
      ...workspace,
      links: workspace.links.map((l) => (l.id === linkId ? updatedLink : l)),
    };

    workspace = await repository.setWorkspace(workspace);

    logger.info("Updated chart layout settings");

    return formatLink(workspace, updatedLink);
  },

  async unshare(_parent, { id: linkId }, { logger, repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    logger = logger.withContext({
      workspace,
    });

    const { links } = workspace;
    const link = links.find((l) => l.id === linkId);

    assert(link, "Sharing Sharing link not found");

    await repository.setWorkspace({
      ...workspace,
      links: links.filter((l) => l.id !== linkId),
    });
    logger.info("Sharing link removed");
  },
} satisfies MutationResolvers<Context>;

const DEFAULT_VISIBLE_FIELDS = [
  "photo",
  "name",
  "jobTitle",
  /**
   * NOTE: manager and department depetermines the position on the org chart
   * to not to show this information a user has to hide nodes and not fields
   */
  "manager",
  "department",
];
const DEFAULT_VISIBLE_VIEWS = [
  "teams",
  "departments",
  "hiring",
  "sidebar",
  "countries",
];

function formatLink(
  workspace: ReadonlyDeep<DomainWorkspace>,
  link: ReadonlyDeep<DomainLink>
): Link {
  return {
    ...link,
    createdAt: new Date(link.createdAt as Date).toDateString(),
    dataField: getDataField(workspace, link.chartLayout.dataField),
    clusterHorizontal: link.chartLayout.clusterHorizontal,
  };
}
