import assert from "assert";

import {
  Holiday as DomainHoliday,
  HolidaysSettings as CoreHolidaysSettings,
  ReadonlyDeep,
} from "@organice/core/domain";
import {
  newHoliday,
  deleteHoliday as domainDeleteHoliday,
  updateHoliday,
  fillHolidaysForCalendarYear,
} from "@organice/core/domain/holidays";
import { getYear } from "date-fns";
import { sortBy } from "lodash";

import { getSessionWorkspace } from "../../../domain";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import {
  type QueryResolvers,
  type MutationResolvers,
} from "../../server.generated";
import { Holiday, HolidaysSettings } from "../../server.generated";

import { Context } from "./_common";

export const Query = {
  async holidays(_parent, _args, { repository, session }) {
    const now = new Date();
    const workspace = await getSessionWorkspace(repository, session);
    let holidays = workspace.holidays.filter(
      (holiday) =>
        holiday.startDate.getFullYear() >= now.getFullYear() ||
        holiday.endDate.getFullYear() >= now.getFullYear()
    );

    holidays = sortBy(holidays, ["startDate"]);

    return formatHolidays(holidays);
  },

  async holidaysSettings(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatHolidaysSettings(workspace.holidaysSettings);
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async updateHolidaysSettings(
    _parent,
    { settings },
    { holidayDescriptionGenerator, holidayGateway, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const now = new Date();
    const removedCountriesIds = workspace.holidaysSettings.countries.filter(
      (c) => !settings.countries.includes(c)
    );
    const addedCountriesIds = settings.countries.filter(
      (c) => !workspace.holidaysSettings.countries.includes(c)
    );

    workspace = {
      ...workspace,
      holidaysSettings: {
        ...workspace.holidaysSettings,
        countries: settings.countries,
      },
    };

    if (addedCountriesIds.length) {
      const currentYear = getYear(new Date());

      workspace = await fillHolidaysForCalendarYear(
        holidayGateway,
        holidayDescriptionGenerator,
        workspace,
        addedCountriesIds,
        currentYear,
        now
      );
    }

    if (removedCountriesIds.length) {
      workspace = {
        ...workspace,
        holidays: workspace.holidays.filter(
          (holiday) => !removedCountriesIds.includes(holiday.country)
        ),
      };
    }

    await repository.setWorkspace(workspace);

    return formatHolidaysSettings(workspace.holidaysSettings);
  },

  async updateHolidays(
    _parent,
    { countries },
    { holidayDescriptionGenerator, holidayGateway, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const now = new Date();

    const currentYear = getYear(new Date());

    workspace = await fillHolidaysForCalendarYear(
      holidayGateway,
      holidayDescriptionGenerator,
      workspace,
      [...countries],
      currentYear,
      now
    );

    await repository.setWorkspace(workspace);

    let holidays = workspace.holidays.filter(
      (holiday) =>
        holiday.startDate.getFullYear() >= now.getFullYear() ||
        holiday.endDate.getFullYear() >= now.getFullYear()
    );

    holidays = sortBy(holidays, ["startDate"]);

    return formatHolidays(holidays);
  },

  async addOrUpdateHoliday(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);
    const { data, id } = args;

    let existingHoliday = id
      ? workspace.holidays.find((h) => h.id === id)
      : null;

    if (existingHoliday) {
      [workspace, existingHoliday] = updateHoliday(
        new Date(),
        workspace,
        existingHoliday,
        data
      );
    } else {
      [workspace] = newHoliday(new Date(), workspace, data);
    }

    await repository.setWorkspace(workspace);

    return formatHolidays(workspace.holidays.slice(-1))[0];
  },

  async deleteHoliday(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);
    const { id } = args;

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    workspace = await domainDeleteHoliday(slackAdapter, workspace, id);

    await repository.setWorkspace(workspace);
  },
} satisfies MutationResolvers<Context>;

function formatHolidays(holidays: ReadonlyDeep<DomainHoliday[]>): Holiday[] {
  return holidays.map((holiday) => ({
    ...holiday,
    startDate: holiday.startDate.toDateString(),
    endDate: holiday.endDate.toDateString(),
  }));
}

function formatHolidaysSettings(
  holidaysSettings: ReadonlyDeep<CoreHolidaysSettings>
): HolidaysSettings {
  return {
    countries: holidaysSettings.countries,
    channel: holidaysSettings.channel,
  };
}
