import assert from "assert";
import crypto from "crypto";

import {
  replace<PERSON><PERSON><PERSON>,
  Member as DomainMember,
  TimeOffType as DomainTimeOffType,
  TimeOffPolicy as DomainTimeOffPolicy,
  ReadonlyDeep,
  WorkspaceRepository,
  Workspace,
  TimeOffRequest as DomainTimeOffRequest,
  Holiday,
  CalendarFeed as DomainCalendarFeed,
  ValidationError,
} from "@organice/core/domain";
import { BalanceChangedType } from "@organice/core/domain/activityLog";
import {
  getAnniversaryDateThisYear,
  getBirthdayDateThisYear,
} from "@organice/core/domain/announcements";
import {
  approveTimeOffRequest,
  canDeleteTimeOffRequest,
  canEditTimeOffRequest,
  deleteTimeOffRequest as DomainDeleteTimeOffRequest,
  newTimeOff,
  TimeOffDuplicateError,
  editTimeOffRequest,
  updateMemberTimeOffBalance,
  updateMemberNextTimeOffResetDates,
  matchesFilter,
  calculateTimeOffDuration,
  rejectTimeOffRequest,
  clearMemberNextTimeOffResetDates,
} from "@organice/core/domain/time-offs";
import {
  eachDayOfInterval,
  eachWeekOfInterval,
  eachMonthOfInterval,
  startOfMonth,
  startOfWeek,
  endOfMonth,
  endOfWeek,
  isSameDay,
  isWithinInterval,
  areIntervalsOverlapping,
  format,
  setYear,
} from "date-fns";

import {
  getSessionWorkspace,
  Session,
  InvalidFeedError,
} from "../../../domain";
import { getCountryFromList } from "../../../helpers/flattenMember";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import { aggregate } from "../../../helpers/utils";
import {
  type CalendarEventResolvers,
  type QueryResolvers,
  type MutationResolvers,
  CalendarEvent as GraphQlCalendarEvent,
  TimeOffRequestStatus,
  Color,
  UpcomingGroupBy,
  TimeOffRequestType,
  TimeOffRequest,
  TimeOffPolicy,
  HolidayEvent,
  EventWithMember,
  CelebrationEventType,
  HolidayEventType,
  TimeOffs,
} from "../../server.generated";

import {
  Context,
  domainDayOfWeekToGraphQlDayOfWeek,
  formatMember,
  graphQlDayOfWeekToDomainDayOfWeek,
} from "./_common";

export const CalendarEvent: CalendarEventResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if ("status" in obj) {
      return "TimeOffEvent";
    }

    if ("title" in obj) {
      return "HolidayEvent";
    }

    return "CelebrationEvent";
  },
};

export const Query = {
  async timeOffRequest(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const request = workspace.timeOffs.requests.find((r) => r.id === id);

    if (!request) {
      return null;
    }

    const sessionMember = workspace.members.find(
      (m) => m.id === session.memberId
    );

    assert(sessionMember, "Member not found");

    const typeById = workspace.timeOffs.types.reduce<
      Record<string, DomainTimeOffType>
    >((acc, x) => {
      acc[x.id] = x;

      return acc;
    }, {});

    return formatRequest(workspace, session, sessionMember, request, typeById);
  },

  async timeOffs(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatTimeOffs(workspace);
  },

  async timeOffTypes(_parent, _args, { repository, session }) {
    return getCalendarEventTypes<TimeOffRequestType>(
      repository,
      session,
      "timeOff"
    );
  },

  async celebrationTypes(_parent, _args, { repository, session }) {
    return getCalendarEventTypes<CelebrationEventType>(
      repository,
      session,
      "celebration"
    );
  },

  async holidayTypes(_parent, _args, { repository, session }) {
    return getCalendarEventTypes<HolidayEventType>(
      repository,
      session,
      "holiday"
    );
  },

  async upcomingEvents(_parent, args, { repository, session }) {
    const to = new Date(args.to);
    const groupBy = args.groupBy;
    const eventTypeIds = args.eventTypeIds ?? undefined;
    const workspace = await getSessionWorkspace(repository, session);
    const today = new Date();
    const period = {
      start: today,
      end: to,
    };
    let buckets: Date[] = [];

    if (groupBy === UpcomingGroupBy.Week) {
      period.start = startOfWeek(today, { weekStartsOn: 1 });
      buckets = eachWeekOfInterval(period, { weekStartsOn: 1 });
    } else if (groupBy === UpcomingGroupBy.Month) {
      period.start = startOfMonth(today);
      buckets = eachMonthOfInterval(period);
    } else {
      period.start = today;
      buckets = eachDayOfInterval(period);
    }

    const members = workspace.members;
    const requests = workspace.timeOffs.requests.filter(
      (timeOff) => timeOff.status === "APPROVED"
    );
    const typeById = workspace.timeOffs.types.reduce<
      Record<string, DomainTimeOffType>
    >((acc, x) => {
      acc[x.id] = x;

      return acc;
    }, {});
    const sessionMember = workspace.members.find(
      (member) => member.id === session.memberId
    );

    assert(sessionMember, "Member not found");

    const holidays = workspace.holidays.filter((holiday) => {
      return isWithinInterval(holiday.startDate as Date, period);
    });

    return aggregate(requests, buckets, (timeOff, bucket) => {
      const startDate = new Date(timeOff.startDate as Date);
      const endDate = new Date(timeOff.endDate as Date);
      const prevBucket = buckets.indexOf(bucket)
        ? buckets[buckets.indexOf(bucket) - 1]
        : null;

      if (groupBy === UpcomingGroupBy.Week) {
        return (
          areIntervalsOverlapping(
            {
              start: bucket,
              end: endOfWeek(bucket, { weekStartsOn: 1 }),
            },
            { start: startDate, end: endDate }
          ) &&
          (!prevBucket ||
            !areIntervalsOverlapping(
              {
                start: prevBucket,
                end: endOfWeek(prevBucket, { weekStartsOn: 1 }),
              },
              { start: startDate, end: endDate }
            ))
        );
      }

      if (groupBy === UpcomingGroupBy.Month) {
        return (
          areIntervalsOverlapping(
            {
              start: bucket,
              end: endOfMonth(bucket),
            },
            { start: startDate, end: endDate }
          ) &&
          (!prevBucket ||
            !areIntervalsOverlapping(
              {
                start: prevBucket,
                end: endOfMonth(prevBucket),
              },
              { start: startDate, end: endDate }
            ))
        );
      }

      return (
        isWithinInterval(bucket, { start: startDate, end: endDate }) &&
        (!prevBucket ||
          !isWithinInterval(prevBucket, { start: startDate, end: endDate }))
      );
    })
      .map((aggregated) => {
        const { key: date, values } = aggregated;
        const events: ReadonlyDeep<EventWithMember>[] = values.map(
          (timeOff) => {
            const member = members.find(
              (_m) => _m.id === timeOff.memberId
            ) as DomainMember;

            return {
              member: formatMember({ workspace, session, member }),
              event: formatRequest(
                workspace,
                session,
                sessionMember,
                timeOff,
                typeById,
                {
                  start: period.start,
                  end: period.end,
                }
              ) as GraphQlCalendarEvent,
            };
          }
        );

        holidays.forEach((holiday) => {
          if (
            isSameDay(
              shiftToYear(holiday.startDate as Date, date.getFullYear()),
              date
            )
          ) {
            events.push({
              event: formatHolidayEvent(holiday),
            });
          }
        });

        members.forEach((member) => {
          const birthdayDate = getBirthdayDateThisYear(
            member,
            date.getFullYear(),
            sessionMember.isAdmin || sessionMember.id === member.id
          );
          const anniversaryDate = getAnniversaryDateThisYear(
            member,
            date.getFullYear()
          );

          if (birthdayDate && isSameDay(birthdayDate, date)) {
            events.push({
              member: formatMember({ workspace, session, member }),
              event: formatBirthdayEvent(member, birthdayDate),
            });
          }

          if (anniversaryDate && isSameDay(anniversaryDate, date)) {
            events.push({
              member: formatMember({ workspace, session, member }),
              event: formatAnniversaryEvent(member, anniversaryDate),
            });
          }
        });

        const filteredEvents = events.filter((event) =>
          eventTypeIds ? eventTypeIds.includes(event.event.type.id) : true
        );

        const sortCalendarEvents = (
          a: ReadonlyDeep<EventWithMember>,
          b: ReadonlyDeep<EventWithMember>
        ): number => {
          const getPriority = (
            event: ReadonlyDeep<EventWithMember>
          ): number => {
            if (event.event.type.id === "BIRTHDAY") return 1;

            if (event.event.type.id === "ANNIVERSARY") return 2;

            if ("isOfficial" in event.event) return 4;

            return 3;
          };

          return getPriority(a) - getPriority(b);
        };

        return {
          date: date.toDateString(),
          events: filteredEvents.sort(sortCalendarEvents),
        };
      })
      .filter((upcomingEvent) => upcomingEvent.events.length);
  },

  async calendarRowGroups(_parent, args, { repository, session }) {
    const from = new Date(args.from);
    const to = new Date(args.to);
    const eventTypeIds = args.eventTypeIds;
    const workspace = await getSessionWorkspace(repository, session);
    const sessionMember = workspace.members.find(
      (m) => m.id === session.memberId
    );

    assert(sessionMember, "Member not found");

    if (from > to) {
      throw new Error("Invalid date range");
    }

    const range = {
      start: from,
      end: to,
    };
    const typeById = workspace.timeOffs.types.reduce<
      Record<string, DomainTimeOffType>
    >((acc, x) => {
      if (matchesFilter(x.id, eventTypeIds ?? [])) {
        acc[x.id] = x;
      }

      return acc;
    }, {});

    const requests = workspace.timeOffs.requests.filter(
      (timeOff) =>
        (isWithinInterval(new Date(timeOff.startDate as Date), range) ||
          isWithinInterval(new Date(timeOff.endDate as Date), range) ||
          isWithinInterval(range.start, {
            start: timeOff.startDate as Date,
            end: timeOff.endDate as Date,
          })) &&
        (timeOff.status === "APPROVED" ||
          (timeOff.status === "PENDING" &&
            (sessionMember.isAdmin ||
              timeOff.approversIds.includes(sessionMember.id) ||
              timeOff.memberId === sessionMember.id))) &&
        timeOff.type in typeById
    );

    return aggregate(
      requests,
      workspace.members as ReadonlyDeep<DomainMember>[],
      (timeOff, member) => timeOff.memberId === member.id
    ).map((aggregated) => {
      const { key: member, values } = aggregated;

      const events: GraphQlCalendarEvent[] = values.map((timeOff) =>
        formatRequest(workspace, session, sessionMember, timeOff, typeById, {
          start: range.start,
          end: range.end,
        })
      );

      const birthdayDate = getBirthdayDateThisYear(
        member,
        range.start.getFullYear(),
        sessionMember.isAdmin || sessionMember.id === member.id
      );

      if (
        birthdayDate &&
        isWithinInterval(birthdayDate, range) &&
        matchesFilter("BIRTHDAY", eventTypeIds ?? [])
      ) {
        events.push(formatBirthdayEvent(member, birthdayDate));
      }
      const anniversaryDate = getAnniversaryDateThisYear(
        member,
        range.start.getFullYear()
      );

      if (
        anniversaryDate &&
        isWithinInterval(anniversaryDate, range) &&
        matchesFilter("ANNIVERSARY", eventTypeIds ?? [])
      ) {
        events.push(formatAnniversaryEvent(member, anniversaryDate));
      }

      const holidays = member.country
        ? workspace.holidays.filter((h) => {
            return member.country && h.country === member.country;
          })
        : [];

      holidays.forEach((holiday) => {
        if (isWithinInterval(holiday.startDate as Date, range)) {
          const holidayEvnet = formatHolidayEvent(holiday);

          events.push(holidayEvnet);
        }
      });

      return {
        member: formatMember({ workspace, session, member }),
        events,
      };
    });
  },

  async memberEvents(_parent, args, { repository, session }) {
    const from = new Date(args.from);
    const to = new Date(args.to);
    const memberId = args.id;
    const eventTypeIds = args.eventTypeIds ?? undefined;
    const workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find((m) => m.id === memberId);

    assert(member, "Member not found");

    if (from > to) {
      throw new Error("Invalid date range");
    }
    const range = {
      start: from,
      end: to,
    };

    const typeById = workspace.timeOffs.types.reduce<
      Record<string, DomainTimeOffType>
    >((acc, x) => {
      if (matchesFilter(x.id, eventTypeIds ?? [])) {
        acc[x.id] = x;
      }

      return acc;
    }, {});

    const timeOffs = workspace.timeOffs.requests.filter(
      (timeOff) =>
        (isWithinInterval(new Date(timeOff.startDate as Date), range) ||
          isWithinInterval(new Date(timeOff.endDate as Date), range) ||
          isWithinInterval(range.start, {
            start: timeOff.startDate as Date,
            end: timeOff.endDate as Date,
          })) &&
        timeOff.memberId === memberId &&
        timeOff.type in typeById
    );

    const events: GraphQlCalendarEvent[] = timeOffs.map((timeOff) =>
      formatRequest(workspace, session, member, timeOff, typeById, {
        start: range.start,
        end: range.end,
      })
    );
    /**
     * NOTE: show hidden birthdays as this query is available only for admins
     */
    const birthdayDate = getBirthdayDateThisYear(
      member,
      range.start.getFullYear(),
      true
    );

    if (birthdayDate && isWithinInterval(birthdayDate, range)) {
      events.push(formatBirthdayEvent(member, birthdayDate));
    }
    const anniversaryDate = getAnniversaryDateThisYear(
      member,
      range.start.getFullYear()
    );

    if (anniversaryDate && isWithinInterval(anniversaryDate, range)) {
      events.push(formatAnniversaryEvent(member, anniversaryDate));
    }

    const holidays = member.country
      ? workspace.holidays.filter((h) => {
          return member.country && h.country === member.country;
        })
      : [];

    holidays.forEach((holiday) => {
      if (isWithinInterval(holiday.startDate as Date, range)) {
        const holidayEvnet = formatHolidayEvent(holiday);

        events.push(holidayEvnet);
      }
    });

    return events;
  },

  async timeOffPolicies(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return workspace.timeOffs.policies.map((policy) =>
      formatTimeOffPolicy({
        policy,
        workspace,
        session,
      })
    );
  },

  async calendarFeed(_parent, { id: feedId }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const feed = workspace.calendarFeeds.find((f) => f.id === feedId);

    if (!feed || (feed.memberId && feed.memberId !== session.memberId)) {
      throw new InvalidFeedError();
    }

    return {
      id: feed.id,
      title: feed.title,
      filters: feed.filters,
    };
  },

  async calendarFeeds(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return workspace.calendarFeeds.filter(
      (feed) => !feed.memberId || feed.memberId === session.memberId
    );
  },
} satisfies QueryResolvers<Context>;

export const Mutation = {
  async setTimeOffSettings(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        channelId:
          args.channel === null
            ? null
            : args.channel?.id ?? workspace.timeOffs.channelId,
        isEnable: args.isEnable ?? workspace.timeOffs.isEnable,
        createDiscussionChannelWhenMultipleApprovers:
          args.createDiscussionChannelWhenMultipleApprovers ??
          workspace.timeOffs.createDiscussionChannelWhenMultipleApprovers,
        changeSlackStatus:
          args.changeSlackStatus ?? workspace.timeOffs.changeSlackStatus,
      },
    };

    await repository.setWorkspace(workspace);

    return formatTimeOffs(workspace);
  },

  async openTimeOffsInApp(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const member = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(member, `Expected member with id "${session.memberId}" to exist`);
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    await slackAdapter.renderCalendarHomeTab(workspace, member.id);
  },

  async addTimeOffRequest(
    _parent,
    { request: input },
    { activityLog, logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const creator = workspace.members.find((m) => m.id === session.memberId);

    assert(creator, "Member not found");
    assert(creator.isAdmin, "Only admins can create time offs from the web");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    try {
      let request: ReadonlyDeep<DomainTimeOffRequest>;

      [workspace, request] = await newTimeOff(
        activityLog,
        new Date(),
        workspace,
        input.requester,
        {
          approversIds: [creator.id],
          startDate: input.startDate,
          endDate: input.endDate,
          type: input.requestType,
        },
        { allowPastTimeOffs: true }
      );

      [workspace, request] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        new Date(),
        request,
        creator
      );

      await repository.setWorkspace(workspace);
    } catch (error) {
      if (error instanceof TimeOffDuplicateError) {
        throw new TimeOffDuplicateValidationError();
      }

      throw error;
    }
  },

  async updateTimeOffRequest(
    _parent,
    { id: requestId, request: input },
    { activityLog, logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const editor = workspace.members.find((m) => m.id === session.memberId);

    assert(editor, "Member not found");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    let request = workspace.timeOffs.requests.find((r) => r.id === requestId);

    assert(request, "Time off not found");

    [workspace, request] = await editTimeOffRequest(
      activityLog,
      slackAdapter,
      workspace,
      editor,
      request,
      {
        startDate: input.startDate,
        endDate: input.endDate,
        type: input.requestType,
      }
    );

    if (request.status === "PENDING") {
      [workspace, request] = await approveTimeOffRequest(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        new Date(),
        request,
        editor
      );
    }

    await repository.setWorkspace(workspace);
  },

  async deleteTimeOffRequest(
    _parent,
    { id },
    { activityLog, logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const timeOff = workspace.timeOffs.requests.find(
      (request) => request.id === id
    );

    assert(timeOff, "Time off not found");

    const member = workspace.members.find((m) => m.id === session.memberId);

    assert(member, "Member not found");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    workspace = await DomainDeleteTimeOffRequest(
      activityLog,
      slackAdapter,
      logger,
      new Date(),
      workspace,
      member,
      timeOff
    );

    await repository.setWorkspace(workspace);
  },

  async approveTimeOffRequest(
    _parent,
    { id: requestId },
    { activityLog, logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const approver = workspace.members.find((m) => m.id === session.memberId);

    assert(approver, "Member not found");
    assert(approver.isAdmin, "Only admins can approve time offs from the web");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const request = workspace.timeOffs.requests.find((r) => r.id === requestId);

    assert(request, "Time off not found");

    [workspace] = await approveTimeOffRequest(
      workspace,
      activityLog,
      slackAdapter,
      logger,
      new Date(),
      request,
      approver
    );

    await repository.setWorkspace(workspace);
  },

  async rejectTimeOffRequest(
    _parent,
    { id: requestId, rejectReason },
    { activityLog, logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const approver = workspace.members.find((m) => m.id === session.memberId);

    assert(approver, "Member not found");
    assert(approver.isAdmin, "Only admins can reject time offs from the web");
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const request = workspace.timeOffs.requests.find((r) => r.id === requestId);

    assert(request, "Time off not found");

    [workspace] = await rejectTimeOffRequest(
      workspace,
      activityLog,
      slackAdapter,
      logger,
      new Date(),
      request,
      rejectReason ?? null,
      approver
    );

    await repository.setWorkspace(workspace);
  },

  async addTimeOffRequestType(
    _parent,
    { type: eventType },
    { repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);

    const newTimeOffType: DomainTimeOffType = {
      id: crypto.randomBytes(10).toString("hex"),
      color: eventType.color,
      label: eventType.label,
      emoji: eventType.emojiUnicode,
      slackEmoji: eventType.emojiShortcode,
    };

    await repository.setWorkspace({
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        types: [...workspace.timeOffs.types, newTimeOffType],
      },
    });

    const { slackEmoji, ...calendarEventType } = newTimeOffType;

    return calendarEventType;
  },

  async updateTimeOffRequestType(
    _parent,
    { id, type: eventType },
    { repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const timeOffType = workspace.timeOffs.types.find((type) => type.id === id);

    assert(timeOffType, `Calendar event type with id ${id} not found`);

    const updatedCalendarEventType: DomainTimeOffType = {
      ...timeOffType,
      color: eventType.color,
      label: eventType.label,
      emoji: eventType.emojiUnicode,
      slackEmoji: eventType.emojiShortcode,
    };

    await repository.setWorkspace({
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        types: workspace.timeOffs.types.map((type) => {
          if (type.id === id) {
            return updatedCalendarEventType;
          }

          return type;
        }),
      },
    });

    const { slackEmoji, ...calendarEventType } = updatedCalendarEventType;

    return calendarEventType;
  },

  async deleteTimeOffRequestType(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    if (workspace.timeOffs.types.length === 1) {
      throw new Error("Can not remove the last Time off type");
    }

    await repository.setWorkspace({
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        types: workspace.timeOffs.types.filter((type) => type.id !== id),
      },
    });
  },

  async reorderTimeOffTypes(_parent, { types }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const typeMap = new Map(
      workspace.timeOffs.types.map((type) => [type.id, type])
    );

    const reorderedTypes = types.map((type) => {
      const existingType = typeMap.get(type);

      assert(existingType, `Type ${type} not found`);

      return existingType;
    });

    await repository.setWorkspace({
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        types: reorderedTypes,
      },
    });
  },

  async updateMemberTimeOffPolicy(
    _parent,
    { memberId, policyId },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find((m) => m.id === memberId);
    const policies = workspace.timeOffs.policies;
    const defaultPolicy = policies.find((p) => p.isDefault);
    let policy: ReadonlyDeep<DomainTimeOffPolicy> | undefined = defaultPolicy;

    if (policyId && defaultPolicy?.id !== policyId) {
      policy = policies.find((p) => p.id === policyId);
    }

    assert(member, `Member with id ${memberId} not found`);

    let updatedMember: ReadonlyDeep<DomainMember> = {
      ...member,
      timeOffTypePolicyId: policy?.id,
    };

    let updatedWorkspace: ReadonlyDeep<Workspace> = replaceMember(
      workspace,
      updatedMember
    );

    if (policy) {
      updatedWorkspace = updateMemberNextTimeOffResetDates(
        policy,
        [updatedMember],
        updatedWorkspace,
        new Date()
      );
    } else {
      updatedWorkspace = clearMemberNextTimeOffResetDates(
        updatedWorkspace,
        updatedMember
      );
    }
    updatedMember = updatedWorkspace.members.find(
      (m) => m.id === updatedMember.id
    )!;

    await repository.setWorkspace(updatedWorkspace);
    workspace = await getSessionWorkspace(repository, session);

    return formatMember({ workspace, session, member: updatedMember });
  },

  async updateTimeOffBalance(
    _parent,
    { balance: { memberId, typeId, balance } },
    { activityLog, repository, session }
  ) {
    const workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member with id ${memberId} not found`);

    const [updatedMember, updatedWorkspace] = await updateMemberTimeOffBalance(
      activityLog,
      member,
      typeId,
      {
        absolute: balance,
        initiatorId: session.memberId,
        type: BalanceChangedType.manual,
      },
      workspace
    );

    await repository.setWorkspace(updatedWorkspace);

    return formatMember({ workspace, session, member: updatedMember });
  },

  async deleteTimeOffPolicy(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const policyToDelete = workspace.timeOffs.policies.find((p) => p.id === id);
    let nextDefaultPolicy: ReadonlyDeep<DomainTimeOffPolicy> | undefined;

    assert(policyToDelete, `Time off policy with id ${id} not found`);

    if (policyToDelete.isDefault) {
      if (workspace.timeOffs.policies.length > 1) {
        nextDefaultPolicy = workspace.timeOffs.policies.find(
          (p) => p.id !== id
        );
      }
    }

    await repository.setWorkspace({
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        policies: workspace.timeOffs.policies
          .filter((policy) => policy.id !== id)
          .map((p) => {
            if (p.id === nextDefaultPolicy?.id) {
              return {
                ...nextDefaultPolicy,
                isDefault: true,
              };
            }

            return p;
          }),
      },
    });
  },

  async updateTimeOffPolicy(_parent, { id, policy }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const timeOffPolicy = workspace.timeOffs.policies.find((p) => p.id === id);

    assert(timeOffPolicy, `Time off policy with id ${id} not found`);
    const { typePolicies } = timeOffPolicy;

    const updatedTimeOffPolicy: ReadonlyDeep<DomainTimeOffPolicy> = {
      ...timeOffPolicy,
      title: policy.title,
      isDefault: policy.isDefault,
      workDays: policy.workDays.map(
        (dayOfWeek) => graphQlDayOfWeekToDomainDayOfWeek[dayOfWeek]
      ),
      includedWeekendDays: policy.includedWeekendDays.map(
        (dayOfWeek) => graphQlDayOfWeekToDomainDayOfWeek[dayOfWeek]
      ),
      notifyAccruals: policy.notifyAccruals,
      typePolicies: policy.typePolicies.map((updatedTypePolicy) => {
        const type = workspace.timeOffs.types.find(
          (t) => t.id === updatedTypePolicy.typeId
        );
        const typePolicy = typePolicies.find(
          (tp) => tp.typeId === updatedTypePolicy.typeId
        );

        assert(
          type,
          `Time off type with id ${updatedTypePolicy.typeId} not found`
        );

        return {
          typeId: type.id,
          yearStart: updatedTypePolicy.yearStart,
          onStartQuota: updatedTypePolicy.onStartQuota ?? 0,
          rollOverToNextYear: updatedTypePolicy.rollOverToNextYear,
          accrualsQuota: updatedTypePolicy.accrualsQuota ?? 0,
          accuralsFrequency: updatedTypePolicy.accuralsFrequency ?? null,
          nextAccruals: typePolicy?.nextAccruals ?? null,
          maxCapacity: updatedTypePolicy.maxCapacity ?? 0,
        };
      }),
    };

    let updatedWorkspace: ReadonlyDeep<Workspace> = {
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        policies: workspace.timeOffs.policies.map((p) => {
          if (p.id === id) {
            return updatedTimeOffPolicy;
          }

          if (policy.isDefault) {
            return {
              ...p,
              isDefault: false,
            };
          }

          return p;
        }),
      },
    };

    if (policy.isDefault) {
      updatedWorkspace = updateMemberNextTimeOffResetDates(
        updatedTimeOffPolicy,
        [...updatedWorkspace.members],
        updatedWorkspace,
        new Date()
      );
    } else {
      updatedWorkspace = updateMemberNextTimeOffResetDates(
        updatedTimeOffPolicy,
        [
          ...updatedWorkspace.members.filter(
            (member) => member.timeOffTypePolicyId === id
          ),
        ],
        updatedWorkspace,
        new Date()
      );
    }

    await repository.setWorkspace(updatedWorkspace);

    return formatTimeOffPolicy({
      policy: updatedTimeOffPolicy,
      workspace,
      session,
    });
  },

  async addTimeOffPolicy(_parent, { policy }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const newTimeOffPolicy: DomainTimeOffPolicy = {
      id: crypto.randomBytes(10).toString("hex"),
      title: policy.title,
      isDefault: policy.isDefault,
      workDays: policy.workDays.map(
        (dayOfWeek) => graphQlDayOfWeekToDomainDayOfWeek[dayOfWeek]
      ),
      includedWeekendDays: policy.includedWeekendDays.map(
        (dayOfWeek) => graphQlDayOfWeekToDomainDayOfWeek[dayOfWeek]
      ),
      notifyAccruals: policy.notifyAccruals,
      typePolicies: policy.typePolicies.map((typePolicy) => {
        const type = workspace.timeOffs.types.find(
          (t) => t.id === typePolicy.typeId
        );

        assert(type, `Time off type with id ${typePolicy.typeId} not found`);

        return {
          typeId: type.id,
          onStartQuota: typePolicy.onStartQuota ?? 0,
          rollOverToNextYear: typePolicy.rollOverToNextYear,
          yearStart: typePolicy.yearStart,
          accrualsQuota: typePolicy.accrualsQuota ?? 0,
          accuralsFrequency: typePolicy.accuralsFrequency ?? null,
          nextAccruals: null,
          maxCapacity: typePolicy.maxCapacity ?? 0,
        };
      }),
    };

    let updatedWorkspace: ReadonlyDeep<Workspace> = {
      ...workspace,
      timeOffs: {
        ...workspace.timeOffs,
        policies: [
          ...workspace.timeOffs.policies.map((p) => {
            if (policy.isDefault) {
              return {
                ...p,
                isDefault: false,
              };
            }

            return p;
          }),
          newTimeOffPolicy,
        ],
      },
    };

    if (newTimeOffPolicy.isDefault) {
      updatedWorkspace = updateMemberNextTimeOffResetDates(
        newTimeOffPolicy,
        [...updatedWorkspace.members],
        updatedWorkspace,
        new Date()
      );
    }

    await repository.setWorkspace(updatedWorkspace);

    return formatTimeOffPolicy({
      policy: newTimeOffPolicy,
      workspace,
      session,
    });
  },

  async addCalendarFeed(_parent, { feed }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const member: DomainMember | null = feed.memberId
      ? (workspace.members.find((m) => m.id === feed.memberId) as DomainMember)
      : null;

    assert(
      !(feed.memberId && !member),
      `Member with id ${feed.memberId!} not found`
    );

    const newFeed: ReadonlyDeep<DomainCalendarFeed> = {
      id: crypto.randomBytes(10).toString("hex"),
      title: feed.title,
      filters: {
        managers: feed.managers,
        departments: feed.departments,
        teams: feed.teams,
        countries: feed.countries,
        types: feed.types,
      },
      memberId: feed.memberId ?? null,
    };

    await repository.setWorkspace({
      ...workspace,
      calendarFeeds: [...workspace.calendarFeeds, newFeed],
    });

    return {
      id: newFeed.id,
      title: newFeed.title,
      filters: newFeed.filters,
    };
  },

  async updateCalendarFeed(_parent, { id, feed }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const feedToUpdate = workspace.calendarFeeds.find((f) => f.id === id);

    assert(feedToUpdate, `Calendar feed with id ${id} not found`);

    const member: DomainMember | null = feed.memberId
      ? (workspace.members.find((m) => m.id === feed.memberId) as DomainMember)
      : null;

    assert(
      !(feed.memberId && !member),
      `Member with id ${feed.memberId!} not found`
    );

    const updatedFeed: ReadonlyDeep<DomainCalendarFeed> = {
      ...feedToUpdate,
      title: feed.title,
      filters: {
        managers: feed.managers,
        departments: feed.departments,
        teams: feed.teams,
        countries: feed.countries,
        types: feed.types,
      },
      memberId: feed.memberId ?? null,
    };

    await repository.setWorkspace({
      ...workspace,
      calendarFeeds: workspace.calendarFeeds.map((f) => {
        if (f.id === id) {
          return updatedFeed;
        }

        return f;
      }),
    });

    return {
      id: updatedFeed.id,
      title: updatedFeed.title,
      filters: updatedFeed.filters,
    };
  },

  async deleteCalendarFeed(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    await repository.setWorkspace({
      ...workspace,
      calendarFeeds: workspace.calendarFeeds.filter((feed) => feed.id !== id),
    });
  },
} satisfies MutationResolvers<Context>;

const timeOffToCalendarStatus: Record<
  DomainTimeOffRequest["status"],
  TimeOffRequestStatus
> = {
  APPROVED: TimeOffRequestStatus.Approved,
  PENDING: TimeOffRequestStatus.Pending,
  REJECTED: TimeOffRequestStatus.Rejected,
};

function formatBirthdayEvent(
  member: ReadonlyDeep<DomainMember>,
  birthdayDate: Date
): GraphQlCalendarEvent {
  return {
    type: {
      id: "BIRTHDAY",
      label: "Birthday",
      color: Color.Pink,
      emoji: "🎂",
    },
    id: `${member.id}BIRTHDAY`,
    endDate: birthdayDate.toDateString(),
    startDate: birthdayDate.toDateString(),
    duration: 1,
  };
}

/**
 * Currently we store only type as a sting in DB, so we need to get all types and then add them by manually
 */
export function formatRequest(
  workspace: ReadonlyDeep<Workspace>,
  session: Session,
  sessionMember: ReadonlyDeep<DomainMember>,
  timeOff: ReadonlyDeep<DomainTimeOffRequest>,
  typeById: Record<string, DomainTimeOffType>,
  interval?: Interval
): TimeOffRequest & GraphQlCalendarEvent {
  const requester = workspace.members.find((m) => m.id === timeOff.memberId);

  assert(requester, "Requester not found");

  const approvers = workspace.members.filter((m) =>
    timeOff.approversIds.includes(m.id)
  );
  const handledBy = workspace.members.find((m) => m.id === timeOff.handledBy);

  assert(
    !interval ||
      isWithinInterval(timeOff.startDate as Date, interval) ||
      isWithinInterval(timeOff.endDate as Date, interval) ||
      isWithinInterval(interval.start, {
        start: timeOff.startDate as Date,
        end: timeOff.endDate as Date,
      }),
    "Start or end date should be within range"
  );

  const duration = calculateTimeOffDuration(
    workspace,
    requester,
    timeOff.startDate as Date,
    timeOff.endDate as Date,
    interval
  );

  return {
    type: {
      id: typeById[timeOff.type].id,
      label: typeById[timeOff.type].label,
      color: typeById[timeOff.type].color,
      emoji: typeById[timeOff.type].emoji,
    },
    requester: formatMember({ workspace, session, member: requester }),
    approvers: approvers.map((m) =>
      formatMember({ workspace, session, member: m })
    ),
    handledBy:
      handledBy && formatMember({ workspace, session, member: handledBy }),
    id: timeOff.id,
    status: timeOffToCalendarStatus[timeOff.status],
    rejectReason: timeOff.rejectReason,
    startDate: new Date(timeOff.startDate as Date).toDateString(),
    endDate: new Date(timeOff.endDate as Date).toDateString(),
    comment: timeOff.comment,
    editable: canEditTimeOffRequest(timeOff, sessionMember),
    deletable: canDeleteTimeOffRequest(timeOff, sessionMember),
    duration,
  };
}

function formatAnniversaryEvent(
  member: ReadonlyDeep<DomainMember>,
  anniversaryDate: Date
): GraphQlCalendarEvent {
  return {
    type: {
      id: "ANNIVERSARY",
      label: "Anniversary",
      color: Color.Sky,
      emoji: "🎉",
    },
    id: `${member.id}ANNIVERSARY`,
    endDate: anniversaryDate.toDateString(),
    startDate: anniversaryDate.toDateString(),
    duration: 1,
  };
}

function formatHolidayEvent(
  holiday: ReadonlyDeep<Holiday>
): GraphQlCalendarEvent {
  return {
    type: {
      id: holiday.country,
      label: holiday.country,
      color: Color.Lime,
      emoji: getCountryFromList(holiday.country).emoji,
    },
    title: holiday.name,
    description: holiday.description,
    id: holiday.id,
    endDate: format(holiday.endDate as Date, "yyyy-MM-dd"),
    startDate: format(holiday.startDate as Date, "yyyy-MM-dd"),
    duration: 1,
    isOfficial: holiday.isOfficial,
  } as HolidayEvent;
}

async function getCalendarEventTypes<
  T extends TimeOffRequestType | CelebrationEventType | HolidayEventType
>(
  repository: WorkspaceRepository,
  session: Session,
  type: "timeOff" | "celebration" | "holiday"
): Promise<ReadonlyDeep<T[]>> {
  const workspace = await getSessionWorkspace(repository, session);

  if (type === "timeOff") {
    return workspace.timeOffs.types.map((t) =>
      formatTimeOffType({
        type: t,
        workspace,
        session,
        includePolicies: true,
      })
    ) as unknown as ReadonlyDeep<T[]>;
  }

  if (type === "celebration") {
    return [
      {
        id: "BIRTHDAY",
        label: "Birthday",
        color: Color.Pink,
        emoji: "🎂",
      },
      {
        id: "ANNIVERSARY",
        label: "Anniversary",
        color: Color.Sky,
        emoji: "🎉",
      },
    ] as unknown as ReadonlyDeep<T[]>;
  }

  return workspace.holidaysSettings.countries.map((country) => ({
    id: country,
    label: country,
    color: Color.Lime,
    emoji: getCountryFromList(country).emoji,
  })) as unknown as ReadonlyDeep<T[]>;
}

function formatTimeOffs(workspace: ReadonlyDeep<Workspace>): TimeOffs {
  const channel = workspace.channels.find(
    (ch) => ch.id === workspace.timeOffs.channelId
  );

  return {
    id: workspace.id,
    channel,
    isEnable: workspace.timeOffs.isEnable,
    createDiscussionChannelWhenMultipleApprovers:
      workspace.timeOffs.createDiscussionChannelWhenMultipleApprovers,
    changeSlackStatus: workspace.timeOffs.changeSlackStatus,
  };
}

class TimeOffDuplicateValidationError extends ValidationError {
  constructor() {
    super("A time off for the specified date range already exists");
  }
}

function formatTimeOffPolicy({
  policy,
  workspace,
  session,
}: {
  policy: ReadonlyDeep<DomainTimeOffPolicy>;
  workspace: ReadonlyDeep<Workspace>;
  session: Session;
}): TimeOffPolicy {
  return {
    id: policy.id,
    title: policy.title,
    isDefault: policy.isDefault,
    workDays: policy.workDays.map(
      (dayOfWeek) => domainDayOfWeekToGraphQlDayOfWeek[dayOfWeek]
    ),
    includedWeekendDays: policy.includedWeekendDays.map(
      (dayOfWeek) => domainDayOfWeekToGraphQlDayOfWeek[dayOfWeek]
    ),
    notifyAccruals: policy.notifyAccruals,
    members: workspace.members
      .filter((member) => member.timeOffTypePolicyId === policy.id)
      .map((member) => formatMember({ workspace, session, member })),
    typePolicies: policy.typePolicies.map((typePolicy) => ({
      ...typePolicy,
      type: workspace.timeOffs.types.find(
        (type) => type.id === typePolicy.typeId
      )!,
      nextAccruals: typePolicy.nextAccruals?.toDateString(),
      yearStart: typePolicy.yearStart,
    })),
  };
}

function formatTimeOffType({
  type,
  workspace,
  session,
  includePolicies = false,
}: {
  type: ReadonlyDeep<DomainTimeOffType>;
  workspace: ReadonlyDeep<Workspace>;
  session: Session;
  includePolicies?: boolean;
}): TimeOffRequestType {
  if (includePolicies) {
    const policies = workspace.timeOffs.policies;

    return {
      id: type.id,
      label: type.label,
      color: type.color,
      emoji: type.emoji,
      policies: policies
        .filter((p) => p.typePolicies.some((tp) => tp.typeId === type.id))
        .map((policy) =>
          formatTimeOffPolicy({
            policy,
            workspace,
            session,
          })
        ),
    };
  }

  return {
    id: type.id,
    label: type.label,
    color: type.color,
    emoji: type.emoji,
  };
}

function shiftToYear(date: Date, year: number): Date {
  return setYear(new Date(date), year);
}
