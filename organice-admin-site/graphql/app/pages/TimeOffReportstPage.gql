query TimeOffReportstPage($from: Date!, $to: Date!, $eventTypeIds: [ID!]) {
  timeOffTypes {
    id
    label
    emoji
    policies {
      id
    }
  }
  timeOffPolicies {
    id
    isDefault
    workDays
    includedWeekendDays
  }
  calendarRowGroups(from: $from, to: $to, eventTypeIds: $eventTypeIds) {
    member {
      id
      fields {
        id
        key
        ... on PhotoField {
          url72
        }
        ... on TextField {
          text
        }
        ... on TeamsField {
          teams {
            ...TeamFields
          }
        }
        ... on DepartmentField {
          department {
            id
            title
            departmentColor
          }
        }
        ... on ManagerField {
          position {
            id
            title
            member {
              id
              fields {
                id
                key
                ... on TextField {
                  text
                }
                ... on PhotoField {
                  url72
                }
              }
            }
          }
          withoutManagerManual
        }
      }
      timeOffTypePolicy {
        id
        workDays
        includedWeekendDays
      }
      # TODO: Refactor - move WorkingDays as standalone type
      workingDays(from: $from, to: $to) {
        workedDays
        totalWorkingDays
      }
    }
    events {
      ... on TimeOffEvent {
        type {
          id
        }
        id
        endDate
        startDate
        status
        editable
        deletable
        duration
      }
    }
  }
}
