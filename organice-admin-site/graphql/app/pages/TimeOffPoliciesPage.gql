query TimeOffPoliciesPage {
  timeOffTypes {
    id
    label
    emoji
    policies {
      id
    }
  }
  myWorkspace {
    id
    memberFields {
      id
      label
    }
  }
  timeOffPolicies {
    id
    title
    isDefault
    workDays
    includedWeekendDays
    typePolicies {
      type {
        id
      }
      onStartQuota
      rollOverToNextYear
      yearStart
      accrualsQuota
      accuralsFrequency
      nextAccruals
      maxCapacity
    }
  }
  members {
    ...UpdatableMemberFields
    fields {
      id
      key
      ... on PhotoField {
        url72
      }
    }
    timeOffTypePolicy {
      id
      title
      isDefault
      workDays
      includedWeekendDays
      typePolicies {
        type {
          id
        }
        onStartQuota
        rollOverToNextYear
        yearStart
        accrualsQuota
        accuralsFrequency
        nextAccruals
        maxCapacity
      }
    }
    timeOffs {
      typeId
      balance
      nextResetAt
    }
  }
}

mutation UpdateTimeOffBalance($balance: UpdateBalanceInput!) {
  updateTimeOffBalance(balance: $balance) {
    id
    timeOffs {
      typeId
      balance
    }
  }
}

mutation UpdateMemberTimeOffPolicy($memberId: ID!, $policyId: ID) {
  updateMemberTimeOffPolicy(memberId: $memberId, policyId: $policyId) {
    id
    timeOffTypePolicy {
      id
      title
      isDefault
      workDays
      includedWeekendDays
      typePolicies {
        type {
          id
        }
        onStartQuota
        rollOverToNextYear
        yearStart
        accrualsQuota
        accuralsFrequency
        nextAccruals
        maxCapacity
      }
    }
  }
}
