query TimeOffsGeneralSettingsPage {
  timeOffs {
    id
    channel {
      id
      name
    }
    isEnable
    createDiscussionChannelWhenMultipleApprovers
    changeSlackStatus
  }
  channels {
    id
    name
  }
  slackPermissionsIssues {
    ...SlackPermissionsIssuesNotification
  }
}

query TimeOffsTypesPoliciesSettingsPage {
  timeOffTypes {
    id
    color
    label
    emoji
  }
  timeOffPolicies {
    id
    title
    isDefault
    workDays
    includedWeekendDays
    notifyAccruals
    typePolicies {
      type {
        id
      }
      yearStart
      onStartQuota
      rollOverToNextYear
      accrualsQuota
      accuralsFrequency
      nextAccruals
      maxCapacity
    }
  }
}

mutation SetTimeOffsGeneralSettings(
  $isEnable: Boolean
  $channel: ChannelInput
  $createDiscussionChannelWhenMultipleApprovers: Boolean
  $changeSlackStatus: Boolean
) {
  setTimeOffSettings(
    isEnable: $isEnable
    channel: $channel
    createDiscussionChannelWhenMultipleApprovers: $createDiscussionChannelWhenMultipleApprovers
    changeSlackStatus: $changeSlackStatus
  ) {
    id
    channel {
      id
      name
    }
    isEnable
    createDiscussionChannelWhenMultipleApprovers
    changeSlackStatus
  }
}

mutation AddTimeOffRequestType($eventType: TimeOffTypeInput!) {
  addTimeOffRequestType(type: $eventType) {
    id
    color
    label
    emoji
  }
}

mutation UpdateTimeOffRequestType($id: ID!, $eventType: TimeOffTypeInput!) {
  updateTimeOffRequestType(id: $id, type: $eventType) {
    id
    color
    label
    emoji
  }
}

mutation DeleteTimeOffRequestType($id: ID!) {
  deleteTimeOffRequestType(id: $id)
}

mutation ReorderTimeOffTypes($types: [ID!]!) {
  reorderTimeOffTypes(types: $types)
}

mutation UpdateTimeOffPolicy($id: ID!, $policy: TimeOffPolicyInput!) {
  updateTimeOffPolicy(id: $id, policy: $policy) {
    id
    title
    isDefault
    workDays
    includedWeekendDays
    notifyAccruals
    typePolicies {
      type {
        id
      }
      onStartQuota
      rollOverToNextYear
      yearStart
      accrualsQuota
      accuralsFrequency
      nextAccruals
      maxCapacity
    }
  }
}

mutation AddTimeOffPolicy($policy: TimeOffPolicyInput!) {
  addTimeOffPolicy(policy: $policy) {
    id
    title
    isDefault
    workDays
    includedWeekendDays
    notifyAccruals
    typePolicies {
      type {
        id
      }
      onStartQuota
      rollOverToNextYear
      yearStart
      accrualsQuota
      accuralsFrequency
      nextAccruals
      maxCapacity
    }
  }
}

mutation DeleteTimeOffPolicy($id: ID!) {
  deleteTimeOffPolicy(id: $id)
}
