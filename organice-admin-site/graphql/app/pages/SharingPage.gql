mutation UpdateLink($id: ID!, $link: UpdateLinkInput!) {
  updateLink(id: $id, link: $link) {
    id
    title
    visibleFields
    visibleViews
  }
}

mutation ToggleNodeVisibility($id: ID!, $linkId: ID!) {
  toggleNodeVisibility(id: $id, linkId: $linkId) {
    id
    visibleNodes
  }
}

query ReadOnlyPositionSidebar($positionId: ID!) {
  expandedNode(id: $positionId) {
    ... on Position {
      id
      type
      title
      teams {
        id
        label
        color
      }
      withoutManagerManual
      department {
        id
        title
        departmentColor
        managerId
      }
      reference {
        jobDescriptionLink
        hiringManagerId
      }
    }
  }
  positionParentInfo(positionId: $positionId) {
    parentPosition {
      id
      title
      member {
        ...MemberShortInfoComponent
      }
    }
  }
}
query ReadOnlyMemberSidebar($memberId: ID!) {
  member(id: $memberId) {
    ...UpdatableMemberFields
    fields {
      ... on PhotoField {
        url512
      }
    }
    notes
  }
  myWorkspace {
    id
    memberFields {
      id
      label
      type
      isEditable
    }
  }
}

query SharingPage(
  $linkId: ID!
  $nodesIds: [String!]!
  $withDepartments: Boolean
) {
  link(id: $linkId) {
    id
    title
    visibleNodes
    visibleFields
    visibleViews
    clusterHorizontal
    dataField {
      id
      label
      type
    }
  }
  myWorkspace {
    id
    memberFields {
      id
      label
      isPublic
    }
  }

  policy {
    id
    fields {
      id
      label
      type
    }
  }

  allNodes(nodesIds: $nodesIds, withDepartments: $withDepartments) {
    ... on Position {
      ...PosFields
    }
    ... on Department {
      ...DepFields
    }
    ... on RootNode {
      ...RootNodeFields
    }
  }
}

mutation SyncExpandedNodes($linkId: ID!, $nodes: [String!]!) {
  syncExpandedNodes(linkId: $linkId, nodes: $nodes) {
    id
  }
}

mutation Unshare($id: ID!) {
  unshare(id: $id)
}

mutation updateLinkChartLayout($linkId: ID!, $layout: ChartLayoutInput!) {
  updateLinkChartLayout(linkId: $linkId, layout: $layout) {
    id
    clusterHorizontal
    dataField {
      id
      label
      type
    }
  }
}
