Feature: Org chart
  Background:
    Given I have landed on org chart page

  Scenario: Adding a position to root
    When I add "Open position" "position" for "root"
    Then I should see "Open position" node under "root"

  Sc<PERSON><PERSON>: Adding a department to root
    When I add "Department" "department" for "root"

    Then I should see "Department" node under "root"

  <PERSON><PERSON><PERSON>: Move position to another position
    When I add "Open position" "position" for "root"
    And I assign "Zombie 3" to opened position
    And I click Move position on element
    Then I should see "Open position" node under "primary.owner"

  <PERSON>enario: Move department to another position
    When I add "Department" "department" for "root"
    And I have seen sidebar
    And I close sidebar
    And I click Move department on element
    Then I should see "Department" node under "primary.owner"

  Scenario: Adding a position
    When I add "Position" "position" for "primary.owner"

    Then I should see "Position" node under "primary.owner"

  Scenario: Adding a department
    When I add "Department" "department" for "primary.owner"

    Then I should see "Department" node under "primary.owner"

  Scenario: Assigning a member to a position
    When I add "First position" "position" for "root"
    And I assign "Zombie 3" to opened position
    Then I should see "Zombie 3" node under "root"

  <PERSON><PERSON><PERSON>: Assigning a position to a department
    When I add "Department" "department" for "primary.owner"
    And I have seen sidebar
    And I close sidebar
    And I add "Open position" "position" for "root"

    Then I should see "Open position" node under "root"
    And I should see "Department" node under "primary.owner"

    When I select department from sidebar
    Then I should see "Open position" node under "Department"

  Scenario: Assigning a manager to a position with member
    When I add "Open position" "position" for "root"
    And I should see "Open position" node under "root"
    And I assign "Zombie 3" to opened position
    # NOTE: when we assign a member Sidebar changes from Position to
    # Member, without manual reopening the next cypress instructuin might fail
    And I close sidebar
    And I have opened sidebar for "Zombie 3"

    And I select "primary.owner" as a manager for opened position
    Then I should see "Zombie 3" node under "primary.owner"

  Scenario: Assigning a manager to a position without member
    Given I add "First position" "position" for "root"
    # NOTE: make sure that after adding a second position
    # the sidebar will be opened for the second position
    And I close sidebar
    And I add "Second position" "position" for "root"

    And I assign "Zombie 3" to opened position
    And I close sidebar
    And I have opened sidebar for "Zombie 3"

    And I select "First position" as a manager for opened position
    Then I should see "Second position" node under "First position"

  Scenario: When unassigning a manager from the position it should go to the root
    Given I add "Open position" "position" for "root"
    And I should see "Open position" node under "root"
    When I assign "Zombie 3" to opened position
    # NOTE: when we assign a member Sidebar changes from Position to
    # Member, without manual reopening the next cypress instructuin might fail
    And I close sidebar
    And I have opened sidebar for "Zombie 3"

    And I select "primary.owner" as a manager for opened position
    Then I should see "Zombie 3" node under "primary.owner"

    And I select cancel manager from sidebar
    Then I should see regular user position as child for root

  Scenario: Changing card subtitle
    Given I have clicked settings button
    And I have clicked Add new field button
    And I have seen Add new field form
    And I create a new "public" custom field named "Custom User" with "user" type
    And I should see created custom field "Custom User"

    And I have clicked Add new field button
    And I have seen Add new field form
    And I create a new "public" custom field named "Custom Text" with "text" type
    And I should see created custom field "Custom Text"

    And I have clicked Add new field button
    And I have seen Add new field form
    And I create a new "public" custom field named "Custom Link" with "link" type
    And I should see created custom field "Custom Link"

    And I have clicked Add new field button
    And I have seen Add new field form
    And I create a new "public" custom field named "Custom Date" with "date" type
    And I should see created custom field "Custom Date"

    And I return back to OrgTree page
    And I have opened sidebar for "primary.owner"
    And I have seen sidebar

    And I select custom "link" "Custom Link" field and type "https://foo.bar.baz"
    And I select custom "text" "Custom Text" field and type "Some text"
    And I select custom "date" "Custom Date" field and type "2025-03-03"
    And  I select custom user "Custom User" field and select "Zombie 3"

    When I open org chart subtitle select
    And I select "Custom Link" as org chart subtitle

    Then I should see "https://foo.bar.baz" as org chart card subtitle

    When I open org chart subtitle select
    And I select "Custom Text" as org chart subtitle

    Then I should see "Some text" as org chart card subtitle

    When I open org chart subtitle select
    And I select "Custom Date" as org chart subtitle

    Then I should see "2025-03-03" as org chart card subtitle

    When I open org chart subtitle select
    And I select "Custom User" as org chart subtitle

    Then I should see "Zombie 3" as org chart card subtitle
