import {
  Given,
  When,
  Then,
  Before,
} from "@badeball/cypress-cucumber-preprocessor";
import { format } from "date-fns";

import { APIWorkspaceAdapter } from "../support/commands";

import "../support/common";

Before(() => {
  const workspaceName = `Workspace ${Math.round(
    Math.random() * Number.MAX_SAFE_INTEGER
  )}`;

  cy.then(() => workspaceName).as("workspaceName");

  cy.getWorkspaceUrl(workspaceName)
    .then((workspaceUrl) => new APIWorkspaceAdapter(workspaceUrl))
    .as("workspace");

  cy.get<APIWorkspaceAdapter>("@workspace").then((workspace) =>
    workspace.getMembers(10)
  );
});

Given("I have landed on org chart page", () => {
  cy.get<APIWorkspaceAdapter>("@workspace")
    .then((workspace) => {
      workspace.enableManager().then(() => workspace.switchToApiMode());
    })
    .then(() => cy.visit("/"))
    .then(() =>
      cy
        .get<string>("@workspaceName")
        .then((workspaceName) => cy.login({ workspaceName }))
    )
    .then(() =>
      cy
        .get<string>("@workspaceName")
        .then((workspaceName) =>
          cy.passOnboarding(workspaceName).then(() =>
            cy
              .addDepartment(workspaceName, "First department")
              .then((firstDepartment) => {
                return Promise.all([
                  cy.addDepartment(workspaceName, "Second department"),
                  cy.addPosition(
                    workspaceName,
                    "First position",
                    firstDepartment.id
                  ),
                ]);
              })
          )
        )
        .visit("/org-chart/teams")
    );
});

When("I select job title input and type", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--jobTitle"]').as("fieldGroup");
    cy.get('[data-test="sidebar-field--jobTitle"] input')
      .focus()
      .clear({ scrollBehavior: "center" })
      .type("owner-title", { scrollBehavior: "center" });

    // Simulate cy.blur() event
    cy.get("@fieldGroup").click(0, 0);
  });
});

When("I get success notification", () => {
  cy.contains("All changes were saved successfully");
});

Then("I should see selected node with typed title", () => {
  cy.get('[data-test^="node-position-title-"]').contains("owner-title");
});

Then("I should see typed value in job title field input", () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.get('[data-test="sidebar-field--jobTitle"] input').should(
      "have.value",
      "owner-title"
    );
  });
});

When("I select phone and type", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--organicePhone"]').as("fieldGroup");
    cy.get('[data-test="sidebar-field--organicePhone"] input')
      .focus()
      .type("+375291112233");

    // Simulate cy.blur() event
    cy.get("@fieldGroup").click(0, 0);
  });
});

Then("I should see typed value in phone field input", () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.get('[data-test="sidebar-field--organicePhone"] input').should(
      "have.value",
      "+375 29 111 22 33"
    );
  });
});

Then("I should see formatted typed value in phone field input", () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.get('[data-test="sidebar-field--organicePhone"] input').should(
      "have.value",
      "+375 29 111 22 33"
    );
  });
});

When("I select birthday date", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--birthday"]')
      .as("fieldGroup")
      .scrollIntoView();
    cy.get('[data-test="sidebar-field--birthday"] input[type="date"]')
      .focus()
      .type("1990-01-01");

    // Simulate cy.blur() event
    cy.get("@fieldGroup").click(0, 0);
  });
});

Then("I should see selected value in birthday date field", () => {
  cy.get('[data-test="sidebar-field--birthday"] input').should(
    "have.value",
    "1990-01-01"
  );
});

When("I select work anniversary date", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--anniversary"]')
      .as("fieldGroup")
      .scrollIntoView();
    cy.get('[data-test="sidebar-field--anniversary"] input')
      .focus()
      .type("1991-12-15");

    // Simulate cy.blur() event
    cy.get("@fieldGroup").click(0, 0);
  });
});

Then("I should see selected value in work anniversary date field", () => {
  cy.get('[data-test="sidebar-field--anniversary"] input').should(
    "have.value",
    "1991-12-15"
  );
});

Given("I have seen Profile field tab", () => {
  cy.url().should("include", "/settings/org-chart/profile-fields");
});

When("I sign-up as an regular user", () => {
  cy.visit("/org-chart/departments");

  cy.get<string>("@workspaceName").then((workspaceName) =>
    cy.login({ workspaceName, isRegular: true })
  );
});

Then("I cant find dropdown or unassign dropdown item", () => {
  cy.get('[data-test="sidebar-form"]').then(($div) => {
    if ($div.find('[aria-label="sidebar-dropdown-menu"]').length > 0) {
      cy.get('[aria-label="sidebar-dropdown-menu"]')
        .contains("Unassign")
        .should("not.exist");
    } else {
      cy.get('[aria-label="sidebar-dropdown-menu"]').should("not.exist");
    }
  });
});

Then("I cant find dropdown or delete dropdown item", () => {
  cy.get('[data-test="sidebar-form"]').then(($div) => {
    if ($div.find('[aria-label="sidebar-dropdown-menu"]').length > 0) {
      cy.get('[aria-label="sidebar-dropdown-menu"]')
        .contains("Delete")
        .should("not.exist");
    } else {
      cy.get('[aria-label="sidebar-dropdown-menu"]').should("not.exist");
    }
  });
});

Then("I should check all field couldn't be editable", () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.get("input").should("have.length", 0);
    cy.get("react-select").should("have.length", 0);
    cy.get("menu").should("have.length", 0);
    cy.get("select").should("have.length", 0);
    cy.get("textarea").should("have.length", 0);
  });
});

When(`I select "Doesn't have a manager" in "Manager" select`, () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.contains("Choose manager")
      .click({ force: true })
      .then(() => cy.get(`[data-test="without-manager"]`).click());
  });
});

Then(`I should see "Doesn't have a manager" value in sidebar`, () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--manager"]').scrollIntoView();
    cy.get('[data-test="sidebar-field--manager"]').should(
      "contain",
      "Doesn't have a manager"
    );
  });
});

Given("I have logout", () => {
  cy.visit("/sign-out");
});

Then("I have seen nodeId in the URL", () => {
  cy.url().should("include", "nodeId");
});

/**
 * NOTE: same as "I have opened sidebar for {name}"
 * but allows to open a sidebar while another sidebar is opened
 */
When("I select a node {name}", (name: string) => {
  cy.get('[data-test^="node-item-wrap-"]').contains(name).click({
    force: true,
  });
});

Then("I should see sidebar for {nodeName}", (nodeName: string) => {
  if (nodeName === "First department") {
    cy.get(
      '[data-test="sidebar-aside"] [data-test="department-title"] input'
    ).should("have.value", "First department");
  } else if (nodeName === "Second department") {
    // NOTE: for some reasons sometimes we have multiple sidebars
    cy.get('[data-test="department-title"] input').should(($elements) => {
      let anyMatch = false;

      $elements.each((_, element) => {
        if (
          "value" in element &&
          (element as HTMLInputElement).value.includes("Second department")
        ) {
          anyMatch = true;
        }
      });

      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      expect(anyMatch).to.be.true;
    });
  } else if (nodeName === "primary.owner") {
    cy.get('[data-test="sidebar-aside"]')
      .contains("primary.owner")
      .should("exist");
  } else if (nodeName === "First position") {
    cy.get(
      '[data-test="sidebar-aside"] [data-test="sidebar-field--jobTitle"] input'
    ).should("have.value", "First position");
  }
});

Then("I unassign position using sidebar actions", () => {
  cy.get('[data-test="sidebar-form"]').should("exist");
  cy.get('[data-test="sidebar-header-actions"]').click();
  cy.get("[data-floating-ui-portal]").within(() => {
    cy.contains("Unassign").click();
  });
});

Given("I navigated to users page", () => {
  cy.visit("/users");
});

Given("I navigated to reports page", () => {
  cy.visit("/calendar/reports");
});

Then("I open sidebar for {name} from the table view", (name: string) => {
  cy.get("tr")
    .contains("td", name)
    .parent()
    .within(() => {
      cy.get('[data-test="cell-open-sidebar-button"]').click();
    });
});

Then("I open sidebar for {name} from the grid view", (name: string) => {
  cy.contains(".dsg-row", name).within(() => {
    cy.get('[data-test="cell-open-sidebar-button"]').click();
  });
});

Then("I see a notification that a user is not on the org chart", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="notification"]').should(
      "contain.text",
      "is not displayed on the orgchart"
    );
  });
});

Then(
  "I should not see a notification that a user is not on the org chart",
  () => {
    cy.get('[data-test="sidebar-aside"]').within(() => {
      cy.get('[data-test="notification"]').should("not.exist");
    });
  }
);

When("I opened {name} sidebar tab", (name: string) => {
  cy.get(
    '[data-test="sidebar-aside"] [data-test="member-sidebar-tabs"]'
  ).within(() => {
    cy.contains(name).click();
  });
});

Then("I should see members time offs", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.contains("Time Offs").should("exist");
    cy.contains("Balance").should("exist");
    cy.contains("Activity").should("exist");
  });
});

Then("I add time off", () => {
  const now = new Date();

  cy.get<APIWorkspaceAdapter>("@workspace").then(() =>
    cy
      .get<string>("@workspaceName")
      .then((workspaceName) =>
        cy.addTimeoff(
          workspaceName,
          "VACATION",
          "U1",
          format(now, "yyyy-M-d"),
          format(now, "yyyy-M-d")
        )
      )
  );
});

Then("I open time off edit sidebar through member sidebar", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.contains("Vacation").click();
  });
});

Then("I should see time off edit sidebar", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.contains("Edit Time Off");
  });
});
