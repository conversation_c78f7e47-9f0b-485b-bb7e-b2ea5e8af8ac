import {
  Before,
  Given,
  Then,
  When,
} from "@badeball/cypress-cucumber-preprocessor";

import { APIWorkspaceAdapter } from "../support/commands";
import "../support/common";

Before(() => {
  const workspaceName = `Workspace ${Math.round(
    Math.random() * Number.MAX_SAFE_INTEGER
  )}`;

  cy.then(() => workspaceName).as("workspaceName");

  cy.getWorkspaceUrl(workspaceName)
    .then((workspaceUrl) => new APIWorkspaceAdapter(workspaceUrl))
    .as("workspace");

  cy.get<APIWorkspaceAdapter>("@workspace").then((workspace) =>
    workspace.getMembers(10)
  );
  cy.clock(new Date("2024-4-10").getTime(), ["Date"]);
});

Given("I have landed on reports page", () => {
  cy.get<APIWorkspaceAdapter>("@workspace")
    .then((workspace) => {
      workspace.enableManager().then(() => workspace.switchToApiMode());
    })
    .then(() => cy.visit("/"))
    .then(() =>
      cy
        .get<string>("@workspaceName")
        .then((workspaceName) => cy.login({ workspaceName }))
    )
    .then(() =>
      cy.get<string>("@workspaceName").then((workspaceName) =>
        cy
          .passOnboarding(workspaceName)
          .addTimeoff(workspaceName, "VACATION", "U1", "2024-4-5", "2024-4-8")
          .addTimeoff(workspaceName, "VACATION", "U1", "2024-4-30", "2024-5-09")
          .addTimeoff(workspaceName, "VACATION", "U1", "2024-3-25", "2024-4-03")
          .addTimeoff(workspaceName, "VACATION", "U1", "2024-3-11", "2024-3-22")
          .addTimeoff(workspaceName, "VACATION", "U1", "2024-2-5", "2024-2-9")

          .addTimeoff(
            workspaceName,
            "VACATION",
            "U1",
            "2023-12-25",
            "2024-01-01"
          )

          .visit("/calendar/reports")
      )
    );
});

Then("I see a total of {value} timeoffs", (value: string) => {
  cy.get("tr")
    .contains("td", "primary.owner")
    .parent()
    .within(() => {
      cy.get('[data-test="cell-event-VACATION"]').should("contain.text", value);
    });
});

Then("I see working days displayed for each employee", () => {
  cy.get('[data-test="reports-table"] tr').each(() => {
    cy.get('[data-test="cell-workingDays"]')
      .invoke("text")
      .then((text) => {
        const trimmed = text.trim();

        expect(trimmed).to.match(/[0-9]+\/[0-9]+/);
      });
  });
});

When("I clear filters", () => {
  cy.visit("/calendar/reports");
});

Given("I include weekends in time offs", () => {
  cy.visit("/settings/time-offs/types-policies");
  cy.get('[data-test="add-policy-button"]').click();
  cy.get('[data-test="select-included-weekend-days"]').click();
  cy.get('[data-test="day-select-Saturday"]').click();
  cy.get('[data-test="day-select-Sunday"]').click();
  cy.get('[data-test="save-policy-button"]').click();
  cy.visit("/calendar/reports");
});

When("I filter by date with {value}", (value: string) => {
  cy.get('[data-test="table-filters"]').within(() => {
    cy.contains("Filters").click();
  });
  cy.get('[data-test="multiple-filters--date"]').click();
  cy.get('[data-test="dropdown-menu"]').within(() => {
    cy.contains(value).click();
  });
});
