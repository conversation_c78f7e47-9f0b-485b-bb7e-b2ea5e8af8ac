Feature: Sidebar
  Background:
    Given I have landed on org chart page

  Scenario: Setting assigned position job title
    Given I have opened sidebar for "primary.owner"

    When I select job title input and type
    And I get success notification

    Then I should see selected node with typed title
    And I reload page

    Then I should see typed value in job title field input
    And I should see selected node with typed title

  Scenario: Settings assigned position phone
    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select phone and type
    And I get success notification

    Then I should see typed value in phone field input
    And I reload page

    When I have seen sidebar

    Then I should see formatted typed value in phone field input

  Scenario: Settings assigned position birthday
    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select birthday date
    And I get success notification

    Then I should see selected value in birthday date field
    And I reload page

    Then I should see selected value in birthday date field

  Scenario: Settings assigned position work anniversary
    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select work anniversary date
    And I get success notification

    Then I should see selected value in work anniversary date field
    And I reload page

    Then I should see selected value in work anniversary date field

  Scenario: Setting assigned position custom text field
    Given I have clicked settings button
    And I have seen Profile field tab
    And I have clicked Add new field button
    And I have seen Add new field form

    When I create a new "public" custom field named "Custom Text" with "text" type
    Then I should see created custom field "Custom Text"
    And I return back to OrgTree page

    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select custom "text" "Custom Text" field and type "Some text"
    And I get success notification

    Then I should see "Some text" as custom "text" "Custom Text" field value
    And I reload page

    Then I should see "Some text" as custom "text" "Custom Text" field value

  Scenario: Setting assigned position custom private text field
    Given I have clicked settings button
    And I have seen Profile field tab
    And I have clicked Add new field button
    And I have seen Add new field form

    When I create a new "private" custom field named "Custom Text" with "text" type
    Then I should see created custom field "Custom Text"

    And I return back to OrgTree page

    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select custom "text" "Custom Text" field and type "Some text"
    And I get success notification

    Then I should see "Some text" as custom "text" "Custom Text" field value

    And I reload page
    Then I should see "Some text" as custom "text" "Custom Text" field value
    And I have logout

    When I sign-up as an regular user

    Given  I have opened sidebar for "primary.owner"
    When I have seen sidebar
    Then I should not see custom field

  Scenario: Setting assigned position custom user field
    Given I have clicked settings button
    And I have seen Profile field tab
    And I have clicked Add new field button
    And I have seen Add new field form

    When I create a new "public" custom field named "Custom User" with "user" type

    Then I should see created custom field "Custom User"

    And I return back to OrgTree page

    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select custom user "Custom User" field and select "Zombie 3"
    And I get success notification

    Then I should see "Zombie 3" as custom "user" "Custom User" field value
    And I reload page

    Then I should see "Zombie 3" as custom "user" "Custom User" field value

  Scenario: Assigning a user to that user's custom user field
    Given I have clicked settings button
    And I have clicked Add new field button
    And I create a new "public" custom field named "Custom User" with "user" type
    And I return back to OrgTree page
    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select custom user "Custom User" field and select "primary.owner"
    And I get success notification

    Then I should see "primary.owner" as custom "user" "Custom User" field value
    And I reload page

    Then I should see "primary.owner" as custom "user" "Custom User" field value

  Scenario: Assigning a subordinate to a user's custom user field
    Given I have clicked settings button
    And I have clicked Add new field button
    And I create a new "public" custom field named "Custom User" with "user" type
    And I return back to OrgTree page

    And I add "First position" "position" for "primary.owner"
    And I assign "Zombie 3" to opened position

    And I add "Second position" "position" for "First position"
    And I assign "Zombie 10" to opened position

    # NOTE: when we assign a member Sidebar changes from Position to
    # Member, without manual reopening the next cypress instructuin might fail
    And I close sidebar
    And I have opened sidebar for "Zombie 10"

    When I select custom user "Custom User" field and select "Zombie 10"
    And I get success notification

    Then I should see "Zombie 10" as custom "user" "Custom User" field value

  Scenario: Setting assigned position custom link field
    Given I have clicked settings button
    And I have seen Profile field tab
    And I have clicked Add new field button
    And I have seen Add new field form

    When I create a new "public" custom field named "Custom Link" with "link" type

    Then I should see created custom field "Custom Link"

    And I return back to OrgTree page

    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar

    When I select custom "link" "Custom Link" field and type "https://foo.bar.baz"
    And I get success notification

    Then I should see "https://foo.bar.baz" as custom "link" "Custom Link" field value
    And I reload page

    Then I should see "https://foo.bar.baz" as custom "link" "Custom Link" field value

  Scenario: Regular users can't unassign other members from positions
    Given I add "Position" "position" for "primary.owner"

    Then I should see "Position" node under "primary.owner"

    Given I assign "Zombie 3" to opened position
    And I have logout

    When I sign-up as an regular user

    Given  I have opened sidebar for "primary.owner"

    When I have seen sidebar

    Then I cant find dropdown or unassign dropdown item

  Scenario: Regular users can't delete themselves position
    Given I add "Position" "position" for "primary.owner"

    Given I assign "Zombie 3" to opened position
    And I have logout

    When I sign-up as an regular user
    And I have opened sidebar for "Zombie 3"
    And I have seen sidebar

    Then I cant find dropdown or delete dropdown item

  Scenario: Regular users can't delete other positions
    Given I add "Position" "position" for "primary.owner"
    And I wait for a re-render for "500" ms

    Then I should see "Position" node under "primary.owner"

    Given I assign "Zombie 3" to opened position
    And I have logout

    When I sign-up as an regular user

    Given I have opened sidebar for "primary.owner"

    When I have seen sidebar

    Then I cant find dropdown or delete dropdown item

  Scenario: Regular user can't edit other positions fields
    Given I add "Position" "position" for "primary.owner"
    And I wait for a re-render for "500" ms

    Then I should see "Position" node under "primary.owner"

    Given I assign "Zombie 3" to opened position
    And I have logout

    When I sign-up as an regular user

    Given I have opened sidebar for "primary.owner"

    When I have seen sidebar

    Then I should check all field couldn't be editable

  Scenario: Assigning "Doesn't have a manager" to a position
    Given I add "Open position" "position" for "root"
    And I wait for a re-render for "500" ms

    When I should see "Open position" node under "root"

    And I select "Doesn't have a manager" in "Manager" select
    Then I should see "Doesn't have a manager" value in sidebar

  Scenario: Sidebar appears if we have nodeId in the URL params
    Given I have opened sidebar for "primary.owner"
    And I have seen sidebar
    And I reload page
    Then I have seen nodeId in the URL
    And I have seen sidebar

  Scenario: I can open different sidebars
    Given I have opened sidebar for "First position"
    Then I should see active "First position" node "on root chart"
    Then I should see sidebar for "First position"

    When I select a node "First department"
    Then I should see active "First department" node "on root chart"
    Then I should see sidebar for "First department"

    When I select a node "Second department"
    Then I should see active "Second department" node "on root chart"
    Then I should see sidebar for "Second department"

    When I select a node "First position"
    Then I should see active "First position" node "on root chart"
    Then I should see sidebar for "First position"

    When I select a node "primary.owner"
    Then I should see active "primary.owner" node "on root chart"
    Then I should see sidebar for "primary.owner"

    When I select a node "First position"
    Then I should see active "First position" node "on root chart"
    Then I should see sidebar for "First position"

    # NOTE: we have a little delay here so the latest opened node id
    # gets into the URL
    When I reload page
    Then I should see sidebar for "First position"

    When I select a node "First department"
    Then I should see active "First department" node "on root chart"
    Then I should see sidebar for "First department"

  Scenario: I can delete position
    Given I have opened sidebar for "First position"
    And I delete "First position" using sidebar actions
    Then I should not see sidebar

  Scenario: I can delete member's position
    Given I have opened sidebar for "primary.owner"
    And I delete "primary.owner" using sidebar actions
    Then I should not see sidebar

  Scenario: I can unassign members from position and assign them back on the org chart page
    Given I have opened sidebar for "primary.owner"
    And I unassign position using sidebar actions
    And I assign "primary.owner" to opened position
    Then I should see sidebar for "primary.owner"

  Scenario: I can unassign members from position and assign them back on the users page
    Given I navigated to users page
    And I open sidebar for "primary.owner" from the grid view
    And I unassign position using sidebar actions
    Then I see a notification that a user is not on the org chart
    And I select "Doesn't have a manager" in "Manager" select
    Then I should not see a notification that a user is not on the org chart

  Scenario: I can switch to member's time off data on the org chart page
    Given I have opened sidebar for "primary.owner"
    And I opened "Time Offs" sidebar tab
    Then I should see members time offs

  Scenario: I can see member's time off data on the reports page
    Given I navigated to reports page
    And I open sidebar for "primary.owner" from the table view
    Then I should see members time offs

  Scenario: I can open time off edit sidebar from member's time off data on the org chart page
    Given I navigated to reports page
    And I add time off
    And I open sidebar for "primary.owner" from the table view
    Then I should see members time offs
    And I open time off edit sidebar through member sidebar
    Then I should see time off edit sidebar
