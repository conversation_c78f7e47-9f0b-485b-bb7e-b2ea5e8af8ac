Feature: Users
  Background:
    Given I have landed on users page

  Scenario: Assign member to position by adding a title
    And I enter job title - "CEO" for "Zombie 10" without position
    Then I should see "Zombie 10" is assigned to position
    And I choose "Zombie 10" as a manager for "Zombie 9" without position
    Then I should see "Zombie 9" is assigned to position
    And I choose "Doesn't have a manager" as a manager for "Zombie 8" without position
    Then I should see "Zombie 8" is assigned to position
    And I choose first department for "Zombie 6" without position
    Then I should see "Zombie 6" is assigned to position
    And I choose first team for "Zombie 7" without position
    Then I should see "Zombie 7" is assigned to position
    And I filter by "fieldId" with "Team"
    Then I should see "Zombie 7" removed from the table
    And I filter by "fieldId" with "Department"
    Then I should see "Zombie 6" removed from the table

  Scenario: Search by multiple fields
    When I search for "zombie 12"
    And I should see "1" rows

    When I search for "Zombie 1"
    And I should see "3" rows

    When I search for ""
    And I enter job title - "CEO" for "Zombie 10" without position
    When I search for "ceo"
    And I should see "1" rows

  @skip
  Scenario: Sort by various fields
    When I search for "Zombie 1"
    And I should see "3" rows
    And I sort by "name"
    Then I should see employees sorted by name

    When I choose first department for "Zombie 11" without position
    And I should see "Zombie 11" is assigned to position
    And I sort by "department"
    Then I should see employees sorted by department

    When I choose first team for "Zombie 10" without position
    And I should see "Zombie 10" is assigned to position
    And I sort by "teams"
    Then I should see employees sorted by teams

    When I choose "Zombie 8" as a manager for "Zombie 12" without position
    And I choose "Zombie 9" as a manager for "Zombie 11" without position
    And I should see "Zombie 11" is assigned to position
    And I should see "Zombie 12" is assigned to position
    And I sort by "manager"
    Then I should see employees sorted by manager

  Scenario: Unassign position
  # TODO: implement

  @skip
  Scenario: Filter by various fields
    When I choose first department for "Zombie 6" without position
    And I should see "Zombie 6" is assigned to position
    And I filter by "d" with "First department"
    Then I should see "1" rows
    And I clear filters

    When I choose first team for "Zombie 7" without position
    And I should see "Zombie 7" is assigned to position
    And I filter by "t" with "First team"
    Then I should see "1" rows
    And I clear filters

    When I choose "Zombie 10" as a manager for "Zombie 9" without position
    And I should see "Zombie 9" is assigned to position
    And I filter by "m" with "Zombie 10"
    Then I should see "1" rows
