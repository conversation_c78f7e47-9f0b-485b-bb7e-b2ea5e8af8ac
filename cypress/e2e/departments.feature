Feature: Departments
  Background:
    Given I have landed on departments page

  Scenario: Adding a department to a department should show 2 departments
    When I add "First position" "position" for "primary.owner"
    And I have seen sidebar
    And I close sidebar
    And I add "First department" "department" for "First position"
    And I have seen sidebar
    And I close sidebar
    And I add "Second position" "position" for "First department"
    And I have seen sidebar
    And I close sidebar
    And I add "Second department" "department" for "Second position"
    And I have seen sidebar
    And I close sidebar

    Then I should see 2 departments

  Scenario: Highlighting node in all parent departments
    When I add "First department" "department" for "primary.owner"
    And I have seen sidebar
    And I close sidebar
    And I add "Second department" "department" for "First department"
    And I have seen sidebar
    And I close sidebar
    And I add "First position" "position" for "Second department"

    And I assign "Zombie 3" to opened position
    And I close sidebar
    And I expand group by panel
    And I click a user position on the departments chart

    Then I should see active "Zombie 3" node "everywhere"

  Scenario: Deleting a department
    Given I add "First department" "department" for "primary.owner"

    Then I delete "First department" using sidebar actions
    Then I should see 0 departments

  Scenario: Assigning a position as a manager of a department
    When I add "Open position" "position" for "root"
    And I add "Department" "department" for "root"
    And I have seen sidebar
    And I assign "Open position" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I should see "Open position" node under "Department"
    And I open "Department" Edit Department sidebar
    And I should see "Open position" as a manager of opened department

  Scenario: Assigning a member as a manager of a department
    When I add "Department" "department" for "root"
    And I have seen sidebar
    And I assign "Zombie" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I should see "Zombie" node under "Department"
    And I open "Department" Edit Department sidebar
    And I should see "Zombie" as a manager of opened department

  Scenario: Replace a manager position of a department with a non-manager position
    When I add "Open position 1" "position" for "root"
    And I add "Open position 2" "position" for "root"

    And I add "Department" "department" for "root"
    And I have seen sidebar
    And I assign "Open position 1" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar

    When I open "Department" Edit Department sidebar
    And I assign "Open position 2" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I should see "Open position 1" node under "Department"
    And I should see "Open position 2" node under "Department"
    And I open "Department" Edit Department sidebar
    And I should see "Open position 2" as a manager of opened department

  Scenario: Replace a manager position of a department with an another manager position
    When I add "Open position 1" "position" for "root"
    And I add "Open position 2" "position" for "root"

    And I add "Department 1" "department" for "root"
    And I have seen sidebar
    And I assign "Open position 1" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I add "Department 2" "department" for "root"
    And I have seen sidebar
    And I assign "Open position 2" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I open "Department 2" Edit Department sidebar

    When I assign "Open position 1" as a manager of opened department
    And I click the save button on the opened sidebar

    When Confrim Modal appears
    Then I agree Confrim Modal
    Then I should not see sidebar
    And I should see "Open position 1" node under "Department 2"
    And I should see "Open position 2" node under "Department 2"
    And I open "Department 2" Edit Department sidebar
    And I should see "Open position 1" as a manager of opened department

  Scenario: Remove a manager position of a department from the department sidebar
    When I add "Open position" "position" for "root"
    And I add "Department" "department" for "root"
    And I have seen sidebar
    And I assign "Open position" as a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I should see "Open position" node under "Department"
    And I open "Department" Edit Department sidebar
    And I should see "Open position" as a manager of opened department

    When I unassign a manager of opened department
    And I click the save button on the opened sidebar

    Then I should not see sidebar
    And I should see "Open position" node under "Department"
    And I open "Department" Edit Department sidebar
    And I should not see a manager of opened department


  Scenario: Create a new department with a manager
    Given I expand group by panel
    When I click Add department button
    And I change a department title to "My new department"
    And I assign "primary.owner" as a manager of opened department
    And I click the create button on the opened sidebar

    Then I should not see sidebar
    And I should see "primary.owner" node under "My new department"

  Scenario: Update a department manager to a member without a position
    Given I expand group by panel
    When I click Add department button
    And I change a department title to "Department with manager"
    And I assign "primary.owner" as a manager of opened department
    And I click the create button on the opened sidebar
    Then I should not see sidebar
    And I should see "primary.owner" node under "Department with manager"

    And I reload page
    When I open "Department with manager" Edit Department sidebar
    And I assign "Zombie" as a manager of opened department
    And I click the save button on the opened sidebar
    Then I should not see sidebar
    And I open "Department with manager" Edit Department sidebar
    And I should see "Zombie" as a manager of opened department
    And I should see "Zombie" before "primary.owner"

  Scenario: Changing a department manager should change positions order
    Given I add "First department" "department" for "root"
    And I close sidebar
    And I add "First position" "position" for "First department"

    When I have opened sidebar for "First department"
    And I assign "primary.owner" as a manager of opened department
    And I click the save button on the opened sidebar
    And I expand group by panel

    Then I should see "primary.owner" before "First position"

    When I expand org chart panel
    And I have opened sidebar for "First department"
    And I assign "First position" as a manager of opened department
    And I click the save button on the opened sidebar
    And I expand group by panel

    Then I should see "First position" before "primary.owner"

  Scenario: Changing a department manager to a manager of another deparment
    Given I add "First department" "department" for "root"
    And I close sidebar
    And I add "First position" "position" for "First department"
    And I close sidebar
    And I add "Second department" "department" for "root"
    And I close sidebar
    And I add "Second position" "position" for "Second department"
    And I close sidebar

    When I have opened sidebar for "First department"
    And I assign "First position" as a manager of opened department
    And I click the save button on the opened sidebar
    And I have opened sidebar for "Second department"
    And I assign "Second position" as a manager of opened department
    And I click the save button on the opened sidebar

    And I have opened sidebar for "First department"
    And I assign "Second position" as a manager of opened department
    And I click the save button on the opened sidebar
    And I approve manager change
    And I expand group by panel

    Then I should see "Second position" before "First position"
