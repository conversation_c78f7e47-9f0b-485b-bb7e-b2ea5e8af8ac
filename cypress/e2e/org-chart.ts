import {
  Given,
  When,
  Then,
  Before,
} from "@badeball/cypress-cucumber-preprocessor";

import { APIWorkspaceAdapter } from "../support/commands";
import "../support/common";

Before(() => {
  const workspaceName = `Workspace ${Math.round(
    Math.random() * Number.MAX_SAFE_INTEGER
  )}`;

  cy.then(() => workspaceName).as("workspaceName");

  cy.getWorkspaceUrl(workspaceName)
    .then((workspaceUrl) => new APIWorkspaceAdapter(workspaceUrl))
    .as("workspace");

  cy.get<APIWorkspaceAdapter>("@workspace").then((workspace) =>
    workspace.getMembers(10)
  );
});

Given("I have landed on org chart page", () => {
  cy.get<APIWorkspaceAdapter>("@workspace")
    .then((workspace) => {
      workspace.enableManager().then(() => workspace.switchToApiMode());
    })
    .then(() => cy.visit("/"))
    .then(() =>
      cy
        .get<string>("@workspaceName")
        .then((workspaceName) => cy.login({ workspaceName }))
    )
    .then(() =>
      cy.get<string>("@workspaceName").then((workspaceName) => {
        cy.passOnboarding(workspaceName).visit("/org-chart/departments");
      })
    );
});

When("I click Move position on element", () => {
  cy.contains("primary.owner")
    .closest(`[data-test^="node-item-wrap-"]`)
    .within(() => {
      cy.get(`[aria-label="Add position or department"]`).click({
        force: true,
      });
    });
  cy.get(`[data-test="new-node-select"] input`).type("Zombie 3");
  cy.get('[aria-label="move-position"]').click();
});

When("I click Move department on element", () => {
  cy.contains("primary.owner")
    .closest(`[data-test^="node-item-wrap-"]`)
    .within(() => {
      cy.get(`[aria-label="Add position or department"]`).click();
    });
  cy.get(`[data-test="new-node-select"] input`).type("Department");
  cy.get('[aria-label="move-department"]').click();
});

When("I select cancel manager from sidebar", () => {
  cy.get(
    '[data-test="sidebar-field--manager"] [data-test="select-clear"]'
  ).click({ multiple: true });
});

When("I select department from sidebar", () => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.contains("Choose department")
      .click({ force: true })
      .then(() => cy.get("[id*=-option-]").contains("Department").click());
  });
});

Then("I should see regular user position as child for root", () => {
  const rootNode = cy.get(`[data-testid^="rf__node-"]:has([data-root])`);
  const node = cy.get(`[data-testid^="rf__node-"]:contains("Zombie 3")`);

  rootNode.invoke("attr", "data-id").then((rootNodeId) => {
    node.invoke("attr", "data-id").then((nodeId) => {
      cy.get(`[data-id=${rootNodeId!}-${nodeId!}-source]`).should("exist");
    });
  });
});

When("I select {name} as a manager for opened position", (name: string) => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-field--manager"]').scrollIntoView();
    cy.get('[data-test="sidebar-field--manager"]').within(() => {
      cy.contains("Choose manager").click({ force: true });
      cy.get("input").type(name, { force: true });
      cy.get(`[data-test*="select-option-"]`).contains(name).click();
    });
  });
});

When("I open org chart subtitle select", () => {
  cy.get('[data-test="data-field-dropdown-trigger"]').click();
});

When("I select {name} as org chart subtitle", (name: string) => {
  cy.get('[data-test^="data-field-option-"]').contains(name).click();
});

Then("I should see {name} as org chart card subtitle", (name: string) => {
  cy.contains("primary.owner")
    .closest(`[data-test^="node-item-wrap-"]`)
    .within(() => {
      cy.contains(name).should("exist");
    });
});
