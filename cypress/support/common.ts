/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import {
  When,
  Then,
  Given,
  defineParameterType,
} from "@badeball/cypress-cucumber-preprocessor";

function disableMotion(win: Cypress.AUTWindow): void {
  const injectedStyleEl = win.document.getElementById("__cy_disable_motion__");

  if (injectedStyleEl) {
    return;
  }
  win.document.head.insertAdjacentHTML(
    "beforeend",
    `
    <style id="__cy_disable_motion__">
      /* Disable CSS transitions. */
      *, *::before, *::after { -webkit-transition: none !important; -moz-transition: none !important; -o-transition: none !important; -ms-transition: none !important; transition: none !important; }
      /* Disable CSS animations. */
      *, *::before, *::after { -webkit-animation: none !important; -moz-animation: none !important; -o-animation: none !important; -ms-animation: none !important; animation: none !important; }
    </style>
  `.trim()
  );
}

// to disable CSS transitions and animations:
Cypress.on("window:before:load", (cyWindow) => {
  disableMotion(cyWindow);
});

Cypress.on(
  "uncaught:exception",
  (err) => !err.message.includes("ResizeObserver loop")
);

defineParameterType({
  name: "node",
  regexp: /"(.*)"/,
  transformer: (value: string): "position" | "department" => {
    if (value === "position") {
      return "position" as const;
    }

    if (value === "department") {
      return "department" as const;
    }

    throw new Error(`Unknown node type: ${value}`);
  },
});

defineParameterType({
  name: "where",
  regexp: /"(.*)"/,
  transformer: (value: string): "on root chart" | "everywhere" => {
    if (value === "on root chart") {
      return "on root chart" as const;
    }

    if (value === "everywhere") {
      return "everywhere" as const;
    }

    throw new Error(`Unknown node type: ${value}`);
  },
});

defineParameterType({
  name: "targetNode",
  regexp: /"(.*)"/,
  transformer: (value: "root" | string) => value,
});

const STRING_PARAMS = [
  "nodeName",
  "name",
  "filter",
  "value",
  "customFieldPublicly",
  "customFieldTitle",
  "customFieldType",
];

STRING_PARAMS.forEach((param) => {
  defineParameterType({
    name: param,
    regexp: /"(.*)"/,
    transformer: (value: string) => value,
  });
});

const getMemberSidebarCustomField = (
  title = "custom-field"
): Cypress.Chainable => {
  return cy
    .get('[data-test^="sidebar-field--"]')
    .filter((_, el) => Cypress.$(el).text().includes(title));
};

When(
  "I add {nodeName} {node} for {targetNode}",
  (
    nodeName: string,
    node: "position" | "department",
    targetNode: "root" | string
  ) => {
    let $targetNode = null;

    if (targetNode === "root") {
      $targetNode = cy.get("[data-root]");
    } else {
      $targetNode = cy
        .contains(targetNode)
        .closest(`[data-test^="node-item-wrap-"]`);
    }

    const nodeSelector =
      node === "position"
        ? '[aria-label="Create position"]'
        : '[aria-label="Create department"]';

    $targetNode.within(() => {
      cy.get(`[aria-label="Add position or department"]`).click({
        force: true,
      });
    });
    cy.get(`[data-test="new-node-select"] input`).type(nodeName, {
      force: true,
    });
    cy.get(nodeSelector, { timeout: 10000 }).click({
      force: true,
    });
  }
);

When("I assign {name} to opened position", (name: string) => {
  cy.get('[data-test="sidebar-form"]').within(() => {
    cy.get('[data-test="assign-employee-select"]')
      .should("be.visible")
      .click()
      .get('[data-test*="select-option-"]')
      .contains(name)
      .click({ force: true });
  });
});

Given("I click the save button on the opened sidebar", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-save"]').should("not.be.disabled").click();
  });
});

Then("I click the create button on the opened sidebar", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    cy.get('[data-test="sidebar-create"]').should("be.enabled").click();
  });
});

Given("I have opened sidebar for {name}", (name: string) => {
  cy.get('[data-test="sidebar-form"]').should("not.exist");
  /**
   * NOTE: make sure that all nodes are visible on the org chart
   */
  cy.get('[data-testid="rf__controls"] [aria-label="fit view"]').click();
  cy.wait(200);
  cy.get('[data-test^="node-item-wrap-"]').contains(name).click({
    force: true,
  });
  cy.get('[data-test="sidebar-form"]').should("exist");
});

When("I have seen sidebar", () => {
  cy.get('[data-test="sidebar-form"]').should("exist");
});

Then("I should not see sidebar", () => {
  cy.get('[data-test="sidebar-aside"]').should("not.exist");
});

When("I close sidebar", () => {
  cy.get('[data-test="sidebar-form"]').should("exist");
  // NOTE: for some reasons sometimes we have multiple sidebars
  cy.get('[data-test="sidebar-aside"] [data-close]').click({ force: true });
  cy.get('[data-test="sidebar-form"]').should("not.exist");
});

Then(
  "I should see active {nodeName} node {where}",
  (nodeName: string, where: "on root chart" | "everywhere") => {
    const whereSelector = `[data-test^="node-item-wrap-"]:contains(${nodeName})`;

    cy.get(
      `${
        where === "on root chart" ? '[data-test="root-chart"] ' : ""
      }${whereSelector}`
    ).each((card) => {
      cy.wrap(card).invoke("attr", "data-active").should("eq", "true");
    });
  }
);

Then(
  "I should see {nodeName} node under {targetNode}",
  (nodeName: string, parentNodeName: "root" | string) => {
    let $parentNode = null;

    if (parentNodeName === "root") {
      $parentNode = cy.get("[data-root]").closest('[data-testid^="rf__node-"]');
    } else {
      $parentNode = cy.get(
        `[data-testid^="rf__node-"]:contains(${parentNodeName})`
      );
    }
    const $node = cy.get(`[data-testid^="rf__node-"]:contains(${nodeName})`);

    $parentNode.invoke("attr", "data-id").then((parentNodeId) => {
      $node.invoke("attr", "data-id").then((nodeId) => {
        cy.get(`[data-id=${parentNodeId!}-${nodeId!}-source]`).should("exist");
      });
    });
  }
);

Then("I reload page", () => {
  cy.wait(500);
  cy.reload();
});

Then("I delete {nodeName} using sidebar actions", (nodeName: string) => {
  cy.contains(`${nodeName}`).closest(`[data-test^="node-item-wrap-"]`);
  cy.get('[data-test="sidebar-form"]').should("exist");
  cy.get('[data-test="sidebar-header-actions"]').click();
  cy.get("[data-floating-ui-portal]").within(() => {
    cy.contains(/Delete|Close position/).click();
  });

  cy.get('[data-test="remove-modal"]').within(() => {
    cy.contains("Delete").click();
  });
});

When("I filter by {filter} with {value}", (filter: string, value: string) => {
  cy.get('[data-test="table-filters"]').within(() => {
    cy.contains("Filters").click();
  });
  cy.get('[data-test="multiple-filters"]').should("exist");
  cy.get(`[data-test="multiple-filters--${filter}"] input`).clear({
    scrollBehavior: "center",
  });

  cy.get(`[data-test="multiple-filters--${filter}"]`).within(() => {
    cy.contains(value).click();
  });
});

Then("I wait for a re-render for {value} ms", (value: string) => {
  cy.wait(parseInt(value, 10));
});

When("Confrim Modal appears", () => {
  cy.get(`[data-text="confirm-modal"]`).should("exist");
});

Then("I agree Confrim Modal", () => {
  cy.get(`[data-text="confirm-modal"]`)
    .get(`[data-test="confirm-yes"]`)
    .click();
});

Then("I disagree Confrim Modal", () => {
  cy.get(`[data-text="confirm-modal"]`)
    .get(`[data-test="confirm-cancel"]`)
    .click();
});

When("I expand org chart panel", () => {
  cy.get(`[data-test="expand-org-chart-panel"]`).click();
});

When("I expand group by panel", () => {
  cy.get(`[data-test="expand-group-by-panel"]`).click();
});

Given("I have clicked settings button", () => {
  cy.get('[data-test="settings-btn"]').click();
});

Given("I have clicked Add new field button", () => {
  cy.get('[data-test="setting-layout"]')
    .scrollTo("bottom", { ensureScrollable: false })
    .get('[data-test="add-new-field-btn"]')
    .click();
});

Given("I have seen Add new field form", () => {
  cy.get('[data-test="add-new-field-form"]').within(() => {
    cy.get('[data-test="add-new-field-form-title"]');
    cy.get('[data-test="add-new-field-form-type"]');
  });
});

When(
  "I create a new {customFieldPublicly} custom field named {customFieldTitle} with {customFieldType} type",
  (
    customFieldPublicly: string,
    customFieldTitle: string,
    customFieldType: string
  ) => {
    cy.get('[data-test="add-new-field-form-title"]').type(customFieldTitle);
    cy.get('[data-test="add-new-field-form-type"]').click();

    cy.get('[data-test="select-options"]').within(() => {
      cy.get("li").contains(customFieldType).click();
    });

    if (customFieldPublicly === "private") {
      cy.get('[data-test="add-new-field-form-publicly"]').click();
    }
    cy.get('[data-test="sidebar-create"]')
      .contains("Create")
      .should("be.visible")
      .should("be.enabled")
      .click();
  }
);

Then(
  "I should see created custom field {customFieldTitle}",
  (customFieldTitle: string) => {
    cy.get('[data-test="setting-layout"]').scrollTo("bottom", {
      ensureScrollable: false,
    });
    cy.get("li").contains(customFieldTitle);
  }
);

Then("I return back to OrgTree page", () => {
  cy.visit("/org-chart/departments");
  cy.location("pathname", { timeout: 6000 }).should(
    "eq",
    "/org-chart/departments"
  );
});

When(
  "I select custom user {customFieldTitle} field and select {name}",
  (customFieldTitle: string, name: string) => {
    cy.get('[data-test="sidebar-aside"]').within(() => {
      getMemberSidebarCustomField(customFieldTitle)
        .as("fieldGroup")
        .scrollIntoView();
      getMemberSidebarCustomField(customFieldTitle).within(() => {
        cy.contains("Choose person")
          .click({ force: true })
          .get('[data-test*="select-option-"]')
          .contains(name)
          .click();
      });
    });
  }
);

When(
  "I select custom {customFieldType} {customFieldTitle} field and type {name}",
  (customFieldType: string, customFieldTitle: string, name: string) => {
    cy.get('[data-test="sidebar-aside"]').within(() => {
      getMemberSidebarCustomField(customFieldTitle)
        .as("fieldGroup")
        .scrollIntoView();
      getMemberSidebarCustomField(customFieldTitle).within(() => {
        cy.get("input").focus().type(name);
        // Simulate cy.blur() event
        cy.get("@fieldGroup").click(0, 0);
      });
    });
  }
);

Then(
  "I should see {value} as custom {customFieldType} {customFieldTitle} field value",
  (value: string, customFieldType: string, customFieldTitle: string) => {
    cy.get('[data-test="sidebar-aside"]').within(() => {
      getMemberSidebarCustomField(customFieldTitle).scrollIntoView();
      getMemberSidebarCustomField(customFieldTitle).within(() => {
        if (customFieldType === "user") {
          cy.contains(value).should("exist");
        } else {
          cy.get("input").should("have.value", value);
        }
      });
    });
  }
);

Then("I should not see custom field", () => {
  cy.get('[data-test="sidebar-aside"]').within(() => {
    getMemberSidebarCustomField().should("not.exist");
  });
});
