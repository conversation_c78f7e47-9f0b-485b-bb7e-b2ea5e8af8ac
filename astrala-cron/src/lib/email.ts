export type EmailData = {
  from: string;
  to: string;
  subject: string;
  htmlBody: string;
  textBody: string;
  messageStream?: string;
  tag?: string;
};

export async function sendEmail(emailData: EmailData) {
  try {
    const postmarkServerToken = process.env.POSTMARK_SERVER_API;

    if (!postmarkServerToken) {
      throw new Error('Postmark server token is not configured');
    }

    const postmarkData = {
      From: emailData.from,
      To: emailData.to,
      Subject: emailData.subject,
      HtmlBody: emailData.htmlBody,
      TextBody: emailData.textBody,
      MessageStream: emailData.messageStream || 'outbound',
      Tag: emailData.tag,
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': postmarkServerToken,
      },
      body: JSON.stringify(postmarkData),
    });

    if (!response.ok) {
      const errorData = (await response.json()) as { Message?: string };
      throw new Error(
        `Postmark API error: ${errorData.Message || 'Unknown error'}`,
      );
    }

    const result = (await response.json()) as { MessageID: string };
    return { success: true, messageId: result.MessageID };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
