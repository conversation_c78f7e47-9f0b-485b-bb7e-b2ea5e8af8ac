import { supabase } from '../lib/supabase';
import { sendEmail } from '../lib/email';
import { assert } from 'console';

type VacancyInfo = {
  id: number;
  title: string;
  created_at: string;
  city: string;
  country: string;
};

type CompanyWithVacancies = {
  id: number;
  name: string;
  open_vacancy_nudge_sent_at: string | null;
  vacancies: VacancyInfo[];
  employers: Array<{
    email: string;
    full_name: string;
  }>;
};

export async function sendOpenVacancyNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;
  
  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString();
  
  // First, get companies with open vacancies older than 60 days
  const { data: companiesWithOldVacancies, error: vacanciesError } = await supabase
    .from('vacancies')
    .select(`
      id,
      title,
      created_at,
      city,
      country,
      company_id,
      companies!inner(
        id,
        name,
        open_vacancy_nudge_sent_at
      )
    `)
    .eq('status', 'OPENED')
    .lt('created_at', sixtyDaysAgo);

  if (vacanciesError) {
    throw new Error(`Failed to fetch companies with old vacancies: ${vacanciesError.message}`);
  }

  if (!companiesWithOldVacancies || companiesWithOldVacancies.length === 0) {
    console.log('📊 No companies found with vacancies open for 60+ days');
    return;
  }

  // Group vacancies by company
  const companiesMap = new Map<number, CompanyWithVacancies>();
  
  for (const vacancy of companiesWithOldVacancies) {
    const company = vacancy.companies as unknown as typeof companiesWithOldVacancies[number]['companies'][number];
    const companyId = company.id;
    
    if (!companiesMap.has(companyId)) {
      companiesMap.set(companyId, {
        id: companyId,
        name: company.name,
        open_vacancy_nudge_sent_at: company.open_vacancy_nudge_sent_at,
        vacancies: [],
        employers: []
      });
    }
    
    companiesMap.get(companyId)!.vacancies.push({
      id: vacancy.id,
      title: vacancy.title,
      created_at: vacancy.created_at,
      city: vacancy.city,
      country: vacancy.country,
    });
  }

  // Get employers for each company
  for (const [companyId, companyData] of companiesMap) {
    const { data: employers, error: employersError } = await supabase
      .from('employers')
      .select('email, full_name')
      .eq('company_id', companyId)
      .not('email', 'is', null);

    if (employersError) {
      console.error(`❌ Failed to fetch employers for company ${companyId}: ${employersError.message}`);
      continue;
    }

    if (employers && employers.length > 0) {
      companyData.employers = employers;
    }
  }

  // Filter out companies without employers
  const companiesWithEmployers = Array.from(companiesMap.values()).filter(
    company => company.employers.length > 0
  );

  console.log(
    `📊 Found ${companiesWithEmployers.length} companies with old vacancies and active employers`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const company of companiesWithEmployers) {
    try {
      // Send emails to all employers of this company
      for (const employer of company.employers) {
        await sendOpenVacancyNudgeEmail(
          employer.email,
          employer.full_name || 'there',
          company.name,
          company.vacancies,
          siteUrl,
        );
      }

      // Update the database to mark that we sent the nudge email to this company
      const { error: updateError } = await supabase
        .from('companies')
        .update({ open_vacancy_nudge_sent_at: new Date().toISOString() })
        .eq('id', company.id);

      if (updateError) {
        console.error(
          `❌ Failed to update open_vacancy_nudge_sent_at for company ${company.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
        console.log(`✅ Sent open vacancy nudge emails to ${company.employers.length} employers at ${company.name}`);
      }
    } catch (error) {
      console.error(
        `❌ Failed to send open vacancy nudge emails to company ${company.name}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Open vacancy nudge emails completed: ${successCount} companies notified successfully, ${errorCount} failed`,
  );
}

async function sendOpenVacancyNudgeEmail(
  email: string,
  name: string,
  companyName: string,
  vacancies: VacancyInfo[],
  siteUrl: string,
) {
  return;

  const subject = `${companyName}: Update your long-running job postings on Astrala Nexus`;

  const formatDaysOpen = (createdAt: string) => {
    const daysOpen = Math.floor((Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24));
    return `${daysOpen} days`;
  };

  const vacancyListHtml = vacancies.map(vacancy => `
    <li style="margin-bottom: 15px; padding: 10px; border-left: 3px solid #007bff; background-color: #f8f9fa;">
      <strong>${vacancy.title}</strong><br>
      <span style="color: #666;">📍 ${vacancy.city}, ${vacancy.country}</span><br>
      <span style="color: #e74c3c;">⏰ Open for ${formatDaysOpen(vacancy.created_at)}</span>
    </li>
  `).join('');

  const vacancyListText = vacancies.map(vacancy => 
    `• ${vacancy.title} (${vacancy.city}, ${vacancy.country}) - Open for ${formatDaysOpen(vacancy.created_at)}`
  ).join('\n');

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #333;">Hi ${name}!</h1>
      <p style="font-size: 16px; line-height: 1.6;">
        We noticed that ${companyName} has ${vacancies.length} job posting${vacancies.length > 1 ? 's' : ''} that have been open for more than 60 days.
      </p>
      <p style="font-size: 16px; line-height: 1.6;">
        Long-running job postings might indicate that:
      </p>
      <ul style="font-size: 16px; line-height: 1.6; padding-left: 20px;">
        <li>The job requirements might be too specific</li>
        <li>The salary range might not be competitive</li>
        <li>The job description needs updating</li>
        <li>The position has been filled but not closed</li>
      </ul>
      
      <h2 style="color: #333; margin-top: 30px;">Your Open Positions:</h2>
      <ul style="list-style: none; padding: 0;">
        ${vacancyListHtml}
      </ul>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Consider reviewing and updating these postings to attract more qualified candidates.
      </p>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${siteUrl}/dashboard/employer/vacancies" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Manage Your Job Postings
        </a>
      </div>
      
      <p style="font-size: 14px; color: #666;">
        Fresh and updated job postings get better visibility and attract more qualified candidates.
      </p>
      <p style="font-size: 14px; color: #666;">
        Best regards,<br>
        The Astrala Nexus Team
      </p>
    </div>
  `;

  const textBody = `
    Hi ${name}!

    We noticed that ${companyName} has ${vacancies.length} job posting${vacancies.length > 1 ? 's' : ''} that have been open for more than 60 days.

    Long-running job postings might indicate that:
    - The job requirements might be too specific
    - The salary range might not be competitive
    - The job description needs updating
    - The position has been filled but not closed

    Your Open Positions:
    ${vacancyListText}

    Consider reviewing and updating these postings to attract more qualified candidates.

    Manage your job postings here: ${siteUrl}/dashboard/employer/vacancies

    Fresh and updated job postings get better visibility and attract more qualified candidates.

    Best regards,
    The Astrala Nexus Team
  `;

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'OpenVacancyNudge',
  });

  if (result.success) {
    console.log(`✅ Sent open vacancy nudge email to ${email} at ${companyName}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
