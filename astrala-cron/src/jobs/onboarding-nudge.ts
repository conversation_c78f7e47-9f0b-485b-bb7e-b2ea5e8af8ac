import { supabase } from '../lib/supabase';
import { sendEmail } from '../lib/email';
import { assert } from 'console';

export async function sendOnboardingNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;

  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const { data: inactiveJobSeekers, error } = await supabase
    .from('job_seekers')
    .select('id, email, full_name, created_at')
    .eq('is_active', false) // TODO: use correct column here
    .is('onboarding_nudge_sent_at', null)
    .not('email', 'is', null)
    .lt('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

  if (error) {
    throw new Error(`Failed to fetch inactive job seekers: ${error.message}`);
  }

  if (!inactiveJobSeekers || inactiveJobSeekers.length === 0) {
    console.log('📊 No inactive job seekers found who need onboarding nudge emails');
    return;
  }

  console.log(
    `📊 Found ${inactiveJobSeekers.length} inactive job seekers who need onboarding nudge emails`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const jobSeeker of inactiveJobSeekers) {
    try {
      // Send the onboarding nudge email
      await sendOnboardingNudgeEmail(
        jobSeeker.email,
        jobSeeker.full_name || 'there',
        siteUrl,
      );

      // Update the database to mark that we sent the nudge email
      const { error: updateError } = await supabase
        .from('job_seekers')
        .update({ onboarding_nudge_sent_at: new Date().toISOString() })
        .eq('id', jobSeeker.id);

      if (updateError) {
        console.error(
          `❌ Failed to update onboarding_nudge_sent_at for job seeker ${jobSeeker.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
      }
    } catch (error) {
      console.error(
        `❌ Failed to send onboarding nudge email to ${jobSeeker.email}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Onboarding nudge emails completed: ${successCount} sent successfully, ${errorCount} failed`,
  );
}

async function sendOnboardingNudgeEmail(
  email: string,
  name: string,
  siteUrl: string,
) {
  const subject = 'Complete your Astrala Nexus profile to unlock opportunities';

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #333;">Hi ${name}!</h1>
      <p style="font-size: 16px; line-height: 1.6;">
        We noticed you started creating your profile on Astrala Nexus but haven't finished setting it up yet.
      </p>
      <p style="font-size: 16px; line-height: 1.6;">
        Don't miss out on amazing job opportunities! Complete your profile to:
      </p>
      <ul style="font-size: 16px; line-height: 1.6; padding-left: 20px;">
        <li>Get matched with relevant job openings</li>
        <li>Be discovered by top employers</li>
        <li>Receive personalized job recommendations</li>
        <li>Access exclusive career opportunities</li>
      </ul>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${siteUrl}" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Complete Your Profile
        </a>
      </div>
      <p style="font-size: 14px; color: #666;">
        It only takes a few minutes to finish your profile and start connecting with employers.
      </p>
      <p style="font-size: 14px; color: #666;">
        Best regards,<br>
        The Astrala Nexus Team
      </p>
    </div>
  `;

  const textBody = `
    Hi ${name}!

    We noticed you started creating your profile on Astrala Nexus but haven't finished setting it up yet.

    Don't miss out on amazing job opportunities! Complete your profile to:
    - Get matched with relevant job openings
    - Be discovered by top employers
    - Receive personalized job recommendations
    - Access exclusive career opportunities

    Complete your profile here: ${siteUrl}

    It only takes a few minutes to finish your profile and start connecting with employers.

    Best regards,
    The Astrala Nexus Team
  `;

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'OnboardingNudge',
  });

  if (result.success) {
    console.log(`✅ Sent onboarding nudge email to ${email}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
