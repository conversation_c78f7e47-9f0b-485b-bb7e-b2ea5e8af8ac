import { supabase } from '../lib/supabase';
import { sendEmail } from '../lib/email';
import { assert } from 'console';

export async function sendProfileUpdateNudgeEmails() {
  const siteUrl = process.env.SITE_URL!;
  
  assert(siteUrl, 'SITE_URL env variable should be defined');
  
  const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString();
  
  const { data: staleProfileJobSeekers, error } = await supabase
    .from('job_seekers')
    .select('id, email, full_name, information_updated_at, information_update_nudge_sent_at')
    .eq('is_active', true)
    .not('email', 'is', null)
    .lt('information_updated_at', sixtyDaysAgo)
    // .or('information_update_nudge_sent_at.is.null,information_update_nudge_sent_at.lt.information_updated_at');

  if (error) {
    throw new Error(`Failed to fetch job seekers with stale profiles: ${error.message}`);
  }

  if (!staleProfileJobSeekers || staleProfileJobSeekers.length === 0) {
    console.log('📊 No active job seekers found who need profile update nudge emails');
    return;
  }

  console.log(
    `📊 Found ${staleProfileJobSeekers.length} active job seekers who need profile update nudge emails`,
  );

  let successCount = 0;
  let errorCount = 0;

  for (const jobSeeker of staleProfileJobSeekers) {
    try {
      // Send the profile update nudge email
      await sendProfileUpdateNudgeEmail(
        jobSeeker.email,
        jobSeeker.full_name || 'there',
        siteUrl,
      );

      // Update the database to mark that we sent the nudge email
      const { error: updateError } = await supabase
        .from('job_seekers')
        .update({ information_update_nudge_sent_at: new Date().toISOString() })
        .eq('id', jobSeeker.id);

      if (updateError) {
        console.error(
          `❌ Failed to update information_update_nudge_sent_at for job seeker ${jobSeeker.id}: ${updateError.message}`,
        );
        errorCount++;
      } else {
        successCount++;
      }
    } catch (error) {
      console.error(
        `❌ Failed to send profile update nudge email to ${jobSeeker.email}: ${error}`,
      );
      errorCount++;
    }
  }

  console.log(
    `📧 Profile update nudge emails completed: ${successCount} sent successfully, ${errorCount} failed`,
  );
}

async function sendProfileUpdateNudgeEmail(
  email: string,
  name: string,
  siteUrl: string,
) {
  return;

  const subject = 'Keep your Astrala Nexus profile fresh and competitive';

  const htmlBody = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #333;">Hi ${name}!</h1>
      <p style="font-size: 16px; line-height: 1.6;">
        We noticed it's been a while since you last updated your profile on Astrala Nexus.
      </p>
      <p style="font-size: 16px; line-height: 1.6;">
        Keeping your profile current is crucial for:
      </p>
      <ul style="font-size: 16px; line-height: 1.6; padding-left: 20px;">
        <li>Staying visible to top employers</li>
        <li>Getting matched with the latest job opportunities</li>
        <li>Showcasing your most recent skills and experience</li>
        <li>Improving your ranking in search results</li>
      </ul>
      <p style="font-size: 16px; line-height: 1.6;">
        Consider updating:
      </p>
      <ul style="font-size: 16px; line-height: 1.6; padding-left: 20px;">
        <li>Your work experience and recent achievements</li>
        <li>New skills you've acquired</li>
        <li>Current salary expectations</li>
        <li>Availability and location preferences</li>
      </ul>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${siteUrl}/dashboard/job-seeker/settings" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Update Your Profile
        </a>
      </div>
      <p style="font-size: 14px; color: #666;">
        A fresh profile means better opportunities and higher visibility to employers.
      </p>
      <p style="font-size: 14px; color: #666;">
        Best regards,<br>
        The Astrala Nexus Team
      </p>
    </div>
  `;

  const textBody = `
    Hi ${name}!

    We noticed it's been a while since you last updated your profile on Astrala Nexus.

    Keeping your profile current is crucial for:
    - Staying visible to top employers
    - Getting matched with the latest job opportunities
    - Showcasing your most recent skills and experience
    - Improving your ranking in search results

    Consider updating:
    - Your work experience and recent achievements
    - New skills you've acquired
    - Updated resume or portfolio
    - Current salary expectations
    - Availability and location preferences

    Update your profile here: ${siteUrl}/dashboard/job-seeker/settings

    A fresh profile means better opportunities and higher visibility to employers.

    Best regards,
    The Astrala Nexus Team
  `;

  const result = await sendEmail({
    from: '<EMAIL>',
    to: email,
    subject,
    htmlBody,
    textBody,
    tag: 'ProfileUpdateNudge',
  });

  if (result.success) {
    console.log(`✅ Sent profile update nudge email to ${email}`);
  } else {
    throw new Error(`Failed to send email: ${result.error}`);
  }
}
