import * as cron from 'node-cron';
import { sendBiweeklyNotifications } from './jobs/biweekly-notifications';

console.log('Astrala Cron Service start');

cron.schedule(
  '0 9 */5 * *',
  async () => {
    console.log('📧 Starting biweekly notifications job...');

    try {
      await sendBiweeklyNotifications();
      console.log('Notifications completed successfully');
    } catch (error) {
      console.error('Notifications failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

console.log(
  'Cron jobs scheduled: biweekly notifications every 5 days at 9:00 AM UTC',
);

process.on('SIGINT', () => {
  process.exit(0);
});
