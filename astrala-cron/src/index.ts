import * as cron from 'node-cron';
import { sendBiweeklyNotifications } from './jobs/biweekly-notifications';
import { sendOnboardingNudgeEmails } from './jobs/onboarding-nudge';

console.log('Astrala Cron Service start');

cron.schedule(
  '0 9 */5 * *',
  async () => {
    console.log('📧 Starting biweekly notifications job...');

    try {
      await sendBiweeklyNotifications();
      console.log('Notifications completed successfully');
    } catch (error) {
      console.error('Notifications failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

// Schedule onboarding nudge emails to run daily at 10:00 AM UTC
cron.schedule(
  '0 10 * * *',
  async () => {
    console.log('📧 Starting onboarding nudge emails job...');

    try {
      await sendOnboardingNudgeEmails();
      console.log('Onboarding nudge emails completed successfully');
    } catch (error) {
      console.error('Onboarding nudge emails failed:', error);
    }
  },
  {
    timezone: 'UTC',
  },
);

console.log(
  'Cron jobs scheduled:',
  '\n- Biweekly notifications every 5 days at 9:00 AM UTC',
  '\n- Onboarding nudge emails daily at 10:00 AM UTC',
);

process.on('SIGINT', () => {
  process.exit(0);
});
