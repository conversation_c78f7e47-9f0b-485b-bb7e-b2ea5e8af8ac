import React, { useCallback, useEffect, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { BasicJobInfoForm } from '~/app/onboarding/employer/(components)/(forms)/BasicJobInfoForm';
import { DrivingLicenseForm } from '~/app/onboarding/employer/(components)/(forms)/DrivingLicenseForm';
import { EducationForm } from '~/app/onboarding/employer/(components)/(forms)/EducationForm';
import { VacancyFormSchema } from '~/app/onboarding/employer/(schemas)/vacancy.schema';
import { useEmployerOnboardingStore } from '~/app/onboarding/employer/(store)/useEmployerOnboardingStore';
import { FormSectionTabs } from '~/app/onboarding/job-seeker/(components)/FormSectionTabs';
import { SectionContainer } from '~/app/onboarding/job-seeker/SectionContainer';

import { LanguageForm } from '~/shared/components/forms/LanguageForm';
import { SalaryForm } from '~/shared/components/forms/SalaryForm';
import { SkillsForm } from '~/shared/components/forms/SkillsForm';
import SubmitOverlay from '~/shared/components/SubmitOverlay.tsx';

import { CertificationsForm } from '../(forms)/CertificationForm';

import { Button } from '~/components/ui/button';
import { Form } from '~/components/ui/form';

type VacancyInfoStepProps = {
  form: UseFormReturn<VacancyFormSchema>;
  onSubmit: (data: VacancyFormSchema) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  submitLabel?: string;
};

export const VacancyInfoStep: React.FC<VacancyInfoStepProps> = ({
  form,
  onSubmit,
  onCancel,
  isSubmitting: externalIsSubmitting,
  submitLabel = 'Next',
}) => {
  const [activeSection, setActiveSection] = useState('basicInfo');
  const [completedSections, setCompletedSections] = useState<string[]>([]);
  const [localIsSubmitting, setLocalIsSubmitting] = useState(false);
  const formInitialized = useRef(false);
  const lastUpdateTime = useRef(0);

  const isOnboardingFlowRef = useRef(
    typeof window !== 'undefined' &&
      window.location.pathname.includes('/onboarding'),
  );
  const isOnboardingFlow = isOnboardingFlowRef.current;

  // Вызываем хук безусловно, но мемоизируем селектор
  const vacancyData = useEmployerOnboardingStore(state => state.vacancyData);
  const setVacancyData = useEmployerOnboardingStore(
    state => state.setVacancyData,
  );
  const storeIsSubmitting = useEmployerOnboardingStore(
    state => state.isSubmitting,
  );
  const setStoreIsSubmitting = useEmployerOnboardingStore(
    state => state.setIsSubmitting,
  );

  const isSubmitting =
    externalIsSubmitting !== undefined
      ? externalIsSubmitting
      : // eslint-disable-next-line unicorn/no-nested-ternary
        isOnboardingFlow
        ? storeIsSubmitting
        : localIsSubmitting;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const sectionRefs = {
    basicInfo: useRef<HTMLDivElement>(null),
    education: useRef<HTMLDivElement>(null),
    salary: useRef<HTMLDivElement>(null),
    language: useRef<HTMLDivElement>(null),
    skills: useRef<HTMLDivElement>(null),
    certifications: useRef<HTMLDivElement>(null),
    drivingLicense: useRef<HTMLDivElement>(null),
  };

  const sections = [
    { id: 'basicInfo', label: 'Job Information', ref: sectionRefs.basicInfo },
    { id: 'education', label: 'Education', ref: sectionRefs.education },
    { id: 'salary', label: 'Salary', ref: sectionRefs.salary },
    { id: 'language', label: 'Language', ref: sectionRefs.language },
    { id: 'skills', label: 'Skill', ref: sectionRefs.skills },
    {
      id: 'certifications',
      label: 'Certification',
      ref: sectionRefs.certifications,
    },
    {
      id: 'drivingLicense',
      label: 'Driving License',
      ref: sectionRefs.drivingLicense,
    },
  ];

  const updateStoreData = useCallback(() => {
    const now = Date.now();
    if (now - lastUpdateTime.current < 500) {
      return;
    }

    if (formInitialized.current && isOnboardingFlow) {
      const currentData = form.getValues();
      if (JSON.stringify(currentData) !== JSON.stringify(vacancyData)) {
        setVacancyData(currentData);
        lastUpdateTime.current = now;
      }
    }
  }, [form, vacancyData, setVacancyData, isOnboardingFlow]);

  const handleBlurCapture = useCallback(() => {
    updateStoreData();
  }, [updateStoreData]);

  const updateCompletedSections = useCallback(() => {
    const values = form.getValues();
    const completed = [];

    if (
      values?.title &&
      values?.description &&
      values?.country &&
      values?.city &&
      values?.experience_years_from !== undefined
    ) {
      completed.push('basicInfo');
    }

    if (values.education_degree && values.education_discipline) {
      completed.push('education');
    }

    if (values.languages?.length > 0 && values.languages[0].language) {
      completed.push('language');
    }

    if (values.skills?.length > 0) {
      completed.push('skills');
    }

    if (values.salary_min && values.salary_max) {
      completed.push('salary');
    }

    if (values.certifications?.length > 0) {
      completed.push('certifications');
    }

    if (values.driving_license) {
      completed.push('drivingLicense');
    }

    setCompletedSections(completed);
  }, [form]);

  useEffect(() => {
    const subscription = form.watch(() => {
      updateCompletedSections();
    });

    const timeout = setTimeout(() => {
      formInitialized.current = true;
    }, 500);

    updateCompletedSections();

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeout);
    };
  }, [form, updateCompletedSections]);

  useEffect(() => {
    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (isOnboardingFlow && !formInitialized.current) {
      if (Object.keys(vacancyData).length > 0) {
        form.reset(vacancyData);
      }
    }
  }, [isOnboardingFlow, form, vacancyData]);

  const handleTabClick = useCallback(
    (sectionId: string) => {
      updateStoreData();
      setActiveSection(sectionId);
      const sectionRef = sectionRefs[sectionId as keyof typeof sectionRefs];
      if (sectionRef?.current) {
        sectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    },
    [updateStoreData, sectionRefs],
  );

  const handleSubmit = async () => {
    try {
      if (externalIsSubmitting === undefined) {
        if (isOnboardingFlow) {
          setStoreIsSubmitting(true);
        } else {
          setLocalIsSubmitting(true);
        }
      }

      const values = form.getValues();
      await form.trigger();

      if (form.formState.isValid) {
        if (isOnboardingFlow) {
          setVacancyData(values);
        }
        await onSubmit(values);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      if (externalIsSubmitting === undefined) {
        if (isOnboardingFlow) {
          setStoreIsSubmitting(false);
        } else {
          setLocalIsSubmitting(false);
        }
      }
    }
  };

  return (
    <div className="rounded-lg w-full flex flex-col justify-center items-center relative">
      <SubmitOverlay
        isSubmitting={Boolean(isSubmitting)}
        message="Saving vacancy information..."
      />

      <FormSectionTabs
        sections={sections}
        activeSection={activeSection}
        completedSections={completedSections}
        onTabClick={handleTabClick}
      />

      <div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            onBlurCapture={handleBlurCapture}
          >
            <div className="space-y-8 p-6">
              <SectionContainer
                id="basicInfo"
                sectionRef={sectionRefs.basicInfo}
              >
                <BasicJobInfoForm control={form.control} />
              </SectionContainer>

              <SectionContainer
                id="education"
                sectionRef={sectionRefs.education}
              >
                <EducationForm control={form.control} />
              </SectionContainer>

              <SectionContainer id="salary" sectionRef={sectionRefs.salary}>
                <SalaryForm control={form.control} />
              </SectionContainer>

              <SectionContainer id="language" sectionRef={sectionRefs.language}>
                <LanguageForm control={form.control} />
              </SectionContainer>

              <SectionContainer id="skills" sectionRef={sectionRefs.skills}>
                <SkillsForm
                  control={form.control}
                  setValue={form.setValue}
                  watch={form.watch}
                />
              </SectionContainer>

              <SectionContainer
                id="certifications"
                sectionRef={sectionRefs.certifications}
              >
                <CertificationsForm
                  control={form.control}
                  setValue={form.setValue}
                  watch={form.watch}
                />
              </SectionContainer>

              <SectionContainer
                id="drivingLicense"
                sectionRef={sectionRefs.drivingLicense}
              >
                <DrivingLicenseForm control={form.control} />
              </SectionContainer>
            </div>

            <div className="p-6 border-t flex justify-start space-x-4">
              <Button type="button" variant="secondary" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                variant="default"
                type="submit"
                disabled={Boolean(isSubmitting)}
              >
                {isSubmitting ? 'Loading...' : submitLabel}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};
