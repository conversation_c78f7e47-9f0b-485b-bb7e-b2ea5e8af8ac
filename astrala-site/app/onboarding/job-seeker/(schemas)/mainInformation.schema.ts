import * as z from 'zod';

const CURRENT_YEAR = new Date().getFullYear();
const MIN_EDUCATION_YEAR = 1940;
const MAX_FUTURE_YEARS = 10;

const phoneRegex = /^[+]?[(]?[0-9]{1,4}[)]?[-\s./0-9]*$/;
const nameMinLength = 2;
const yearValidation = {
  startYear: z
    .number()
    .int('Year must be a number')
    .min(
      MIN_EDUCATION_YEAR,
      `Start year must not be earlier than ${MIN_EDUCATION_YEAR}`,
    )
    .max(CURRENT_YEAR, 'Start year cannot be in the future'),
  endYear: z
    .number()
    .int('Year must be a number')
    .min(
      MIN_EDUCATION_YEAR,
      `End year must not be earlier than ${MIN_EDUCATION_YEAR}`,
    )
    .max(
      CURRENT_YEAR + MAX_FUTURE_YEARS,
      `End year cannot be more than ${MAX_FUTURE_YEARS} years in the future`,
    ),
};

export const fileSchema = z.any().optional().nullable();

export const certificationSchema = z.object({
  id: z.number({ required_error: 'Certification ID is required' }),
  name: z
    .string({ required_error: 'Certification name is required' })
    .min(1, 'Certification name cannot be empty')
    .max(100, 'Certification name is too long'),
  file: fileSchema,
  file_path: z.string().optional().nullable(),
});

export type Certification = z.infer<typeof certificationSchema>;

export const skillSchema = z.object({
  id: z.number({ required_error: 'Skill ID is required' }),
  name: z
    .string({ required_error: 'Skill name is required' })
    .min(1, 'Skill name cannot be empty')
    .max(50, 'Skill name is too long'),
});

export type Skill = z.infer<typeof skillSchema>;

export const educationSchema = z
  .object({
    type: z.string().min(1, 'Select education type'),
    institution: z
      .string()
      .min(
        nameMinLength,
        `Institution name must contain at least ${nameMinLength} characters`,
      )
      .max(100, 'Institution name is too long'),
    discipline: z
      .string()
      .min(
        nameMinLength,
        `Discipline must contain at least ${nameMinLength} characters`,
      )
      .max(100, 'Discipline name is too long'),
    startYear: yearValidation.startYear,
    endYear: yearValidation.endYear,
  })
  .refine(data => data.startYear <= data.endYear, {
    message: 'End year must be greater than or equal to start year',
    path: ['endYear'],
  });

export const workExperienceSchema = z
  .object({
    companyName: z
      .string()
      .min(
        nameMinLength,
        `Company name must contain at least ${nameMinLength} characters`,
      )
      .max(80, 'Company name is too long'),
    jobTitle: z
      .string()
      .min(
        nameMinLength,
        `Position must contain at least ${nameMinLength} characters`,
      )
      .max(80, 'Job title is too long'),
    startMonth: z
      .number()
      .int('Month must be a number')
      .min(1, 'Month must be between 1 and 12')
      .max(12, 'Month must be between 1 and 12'),
    startYear: yearValidation.startYear,
    endMonth: z
      .number()
      .int('Month must be a number')
      .min(1, 'Month must be between 1 and 12')
      .max(12, 'Month must be between 1 and 12'),
    endYear: yearValidation.endYear,
    comment: z.string().max(2000, 'Comment is too long').optional(),
  })
  .refine(
    data => {
      const startDate = new Date(data.startYear, data.startMonth - 1);
      const endDate = new Date(data.endYear, data.endMonth - 1);
      return startDate <= endDate;
    },
    {
      message: 'End date must be after start date',
      path: ['endYear'],
    },
  );

export const languageSchema = z.object({
  language: z.string().min(1, 'Select a language'),
  level: z.string().min(1, 'Select language proficiency level'),
});

export const mainInformationFormSchema = z
  .object({
    personalInfo: z.object({
      fullName: z
        .string()
        .min(
          nameMinLength,
          `Name must contain at least ${nameMinLength} characters`,
        )
        .max(100, 'Name is too long'),
      jobTitle: z
        .string()
        .min(
          nameMinLength,
          `Position must contain at least ${nameMinLength} characters`,
        )
        .max(100, 'Job title is too long'),
      phone: z
        .string()
        .min(5, 'Phone must contain at least 5 characters')
        .max(20, 'Phone number is too long')
        .regex(phoneRegex, 'Invalid phone number format'),
      country: z
        .string()
        .min(
          nameMinLength,
          `Country must contain at least ${nameMinLength} characters`,
        ),
      city: z
        .string()
        .min(
          nameMinLength,
          `City must contain at least ${nameMinLength} characters`,
        ),
    }),
    education: z.array(educationSchema).min(1, 'Add at least one education'),
    certifications: z.array(certificationSchema).optional().default([]),
    workExperience: z
      .array(workExperienceSchema)
      .min(1, 'Add at least one work experience'),
    languages: z.array(languageSchema).min(1, 'Add at least one language'),
    skills: z.array(skillSchema).min(1, 'Add at least one skill'),
    salary_min: z.number().min(1, 'Salary cannot be less than 1'),
    salary_max: z.number().min(1, 'Salary cannot be less than 1'),
    drivingLicense: z
      .object({
        file: fileSchema,
        hasDocument: z.boolean().nullable().default(null),
        file_path: z.string().optional(),
      })
      .optional(),
    passport: z.object({
      file: fileSchema,
      hasDocument: z.boolean().default(false),
      file_path: z.string().optional(),
    }),
  })
  .refine(
    data => {
      if (data.salary_min && data.salary_max) {
        return data.salary_min <= data.salary_max;
      }
      return true;
    },
    {
      message: 'Minimum salary cannot be greater than maximum salary',
      path: ['salary_max'],
    },
  );

export type MainInformationFormSchema = z.infer<
  typeof mainInformationFormSchema
>;

export const getDefaultValues = () => {
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();

  return {
    personalInfo: {
      fullName: '',
      jobTitle: '',
      phone: '',
      country: '',
      city: '',
    },
    education: [
      {
        type: '',
        institution: '',
        discipline: '',
        startYear: currentYear - 4,
        endYear: currentYear,
      },
    ],
    certifications: [],
    workExperience: [
      {
        companyName: '',
        jobTitle: '',
        startMonth: currentMonth,
        startYear: currentYear - 1,
        endMonth: currentMonth,
        endYear: currentYear,
        comment: '',
      },
    ],
    languages: [
      {
        language: '',
        level: '',
      },
    ],
    skills: [],
    drivingLicense: {
      file: undefined,
      hasDocument: null,
    },
    passport: {
      file: undefined,
      hasDocument: false,
    },
    salary_min: 0,
    salary_max: 0,
  };
};
