import React, { useEffect } from 'react';

import { useOnboardingStore } from '~/app/onboarding/job-seeker/(store)/useOnboardingStore';

import QuestionnaireComponent from '~/shared/components/QuestionnaireComponent.tsx';
import SubmitOverlay from '~/shared/components/SubmitOverlay.tsx';

import {
  createJobSeekerCertificationById,
  createJobSeekerCertificationByName,
} from '~/api/entities/certification/actions';
import {
  useAddEducation,
  useAddLanguage,
  useAddWorkExperience,
  useUpdateJobSeekerProfile,
  useUploadCertification,
  useUploadDrivingLicense,
  useUploadPassport,
} from '~/api/entities/job-seeker/queries';
import { processSkills } from '~/api/entities/skill/actions';
import {
  useQuestionGroups,
  useSaveQuestionnaireAnswers,
} from '~/api/features/questionaire/queries';
import { useAuth } from '~/providers/AuthProvider';

type WorkStyleTestStepProps = {
  onComplete: () => void;
};

type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};

const WorkStyleTestStep: React.FC<WorkStyleTestStepProps> = ({
  onComplete,
}) => {
  const { jobSeekerProfile } = useAuth();

  const {
    workStyleAnswers,
    updateWorkStyleAnswer,
    setWorkStyleAnswers,
    workStyleCurrentGroupIndex,
    setWorkStyleCurrentGroupIndex,
    setIsSubmitting,
    isSubmitting,
    formData,
  } = useOnboardingStore();

  const { data, isLoading, error } = useQuestionGroups();
  const saveAnswersMutation = useSaveQuestionnaireAnswers();

  const updateProfileMutation = useUpdateJobSeekerProfile();
  const addEducationMutation = useAddEducation();
  const addWorkExperienceMutation = useAddWorkExperience();
  const addLanguageMutation = useAddLanguage();
  const uploadDrivingLicenseMutation = useUploadDrivingLicense();
  const uploadPassportMutation = useUploadPassport();
  const uploadCertificationMutation = useUploadCertification();

  const groupedQuestions = React.useMemo(() => {
    if (!data) return [];

    try {
      return data.groups
        .map(group => {
          const groupQuestions = data.questions
            .filter(q => q.group_id === group.id)
            .map(q => ({
              id: q.id,
              name: q.name,
              group_id: q.group_id,
              created_at: q.created_at,
            }));

          return {
            id: group.id,
            name: group.name,
            created_at: group.created_at,
            questions: groupQuestions,
          };
        })
        .filter(group => group.questions.length > 0);
    } catch (error) {
      console.error(error);
      return [];
    }
  }, [data]);

  useEffect(() => {
    if (data && groupedQuestions.length > 0 && workStyleAnswers.size === 0) {
      try {
        const initialAnswers = new Map<number, number>();

        groupedQuestions.forEach(group => {
          group.questions.forEach(question => {
            const isTradeoffQuestion = data.answerTags.some(
              tag => tag.question_id === question.id && tag.answer_text,
            );
            initialAnswers.set(question.id, isTradeoffQuestion ? 1 : 3);
          });
        });

        setWorkStyleAnswers(initialAnswers);
      } catch (error) {
        console.error(error);
      }
    }
  }, [data, groupedQuestions, workStyleAnswers.size, setWorkStyleAnswers]);

  const handleAnswerChange = (questionId: number, value: number) => {
    updateWorkStyleAnswer(questionId, value);
  };

  const handleNext = async () => {
    if (workStyleCurrentGroupIndex < groupedQuestions.length - 1) {
      const newIndex = workStyleCurrentGroupIndex + 1;
      setWorkStyleCurrentGroupIndex(newIndex);
    } else if (jobSeekerProfile?.id) {
      try {
        setIsSubmitting(true);

        if (jobSeekerProfile?.id && jobSeekerProfile?.user_id) {
          const profileUpdateParams = {
            jobSeekerId: jobSeekerProfile.id,
            fullName: formData.personalInfo.fullName,
            jobTitle: formData.personalInfo.jobTitle,
            phone: formData.personalInfo.phone,
            country: formData.personalInfo.country,
            city: formData.personalInfo.city,
            salary_min: formData.salary_min,
            salary_max: formData.salary_max,
            has_driving_license: formData.drivingLicense?.hasDocument === true,
          };

          await updateProfileMutation.mutateAsync(profileUpdateParams);

          for (const education of formData.education) {
            await addEducationMutation.mutateAsync({
              jobSeekerId: jobSeekerProfile.id,
              type: education.type,
              institution: education.institution,
              discipline: education.discipline,
              startYear: education.startYear,
              endYear: education.endYear,
            });
          }

          for (const experience of formData.workExperience) {
            await addWorkExperienceMutation.mutateAsync({
              jobSeekerId: jobSeekerProfile.id,
              companyName: experience.companyName,
              jobTitle: experience.jobTitle,
              startMonth: experience.startMonth,
              startYear: experience.startYear,
              endMonth: experience.endMonth,
              endYear: experience.endYear,
              comment: experience.comment || '',
            });
          }

          for (const lang of formData.languages) {
            await addLanguageMutation.mutateAsync({
              jobSeekerId: jobSeekerProfile.id,
              language: lang.language,
              level: lang.level,
            });
          }

          if (formData.skills.length > 0 && jobSeekerProfile?.id) {
            const skillsResult = await processSkills(
              { id: Number(jobSeekerProfile.id), type: 'jobSeeker' },
              formData.skills,
            );
            if (!skillsResult) {
              throw new Error('Failed to process skills');
            }
          }

          if (
            formData.certifications &&
            formData.certifications.length > 0 &&
            jobSeekerProfile?.id
          ) {
            for (const certification of formData.certifications) {
              let result;

              if (certification.id > 0) {
                result = await createJobSeekerCertificationById(
                  Number(jobSeekerProfile.id),
                  certification.id,
                );
              } else {
                result = await createJobSeekerCertificationByName(
                  Number(jobSeekerProfile.id),
                  certification.name,
                );
              }

              if (!result.success || !result.jobSeekerCertificationId) {
                continue;
              }

              if (certification.file && jobSeekerProfile.user_id) {
                await uploadCertificationMutation.mutateAsync({
                  file: certification.file,
                  jobSeekerCertificationId: result.jobSeekerCertificationId,
                  userId: jobSeekerProfile.user_id,
                });
              }
            }
          }

          if (
            formData.drivingLicense?.hasDocument === true &&
            formData.drivingLicense?.file &&
            jobSeekerProfile?.id &&
            jobSeekerProfile?.user_id
          ) {
            await uploadDrivingLicenseMutation.mutateAsync({
              file: formData.drivingLicense.file,
              userId: jobSeekerProfile.user_id,
              jobSeekerId: jobSeekerProfile.id,
            });
          }

          if (
            formData.passport?.file &&
            jobSeekerProfile?.id &&
            jobSeekerProfile?.user_id
          ) {
            await uploadPassportMutation.mutateAsync({
              file: formData.passport.file,
              userId: jobSeekerProfile.user_id,
              jobSeekerId: jobSeekerProfile.id,
            });
          }
        }

        const serializedAnswers: QuestionnaireAnswer[] = Array.from(
          workStyleAnswers.entries(),
          // eslint-disable-next-line @typescript-eslint/naming-convention
        ).map(([question_id, value]) => ({
          question_id,
          value,
        }));

        await saveAnswersMutation.mutateAsync({
          jobSeekerId: jobSeekerProfile.id,
          answers: serializedAnswers,
        });

        setIsSubmitting(false);
        onComplete();
      } catch (error) {
        console.error(error);
        setIsSubmitting(false);
      }
    } else {
      console.error('Error: No job seeker profile ID found');
    }
  };

  const handleBack = () => {
    if (workStyleCurrentGroupIndex > 0) {
      const newIndex = workStyleCurrentGroupIndex - 1;
      setWorkStyleCurrentGroupIndex(newIndex);
    }
  };

  const renderButtonText = () => {
    const isLastGroup =
      workStyleCurrentGroupIndex === groupedQuestions.length - 1;

    if (isLastGroup) {
      return isSubmitting ? 'Saving all information...' : 'Finish';
    }

    return 'Next';
  };

  const overlay =
    isSubmitting &&
    workStyleCurrentGroupIndex === groupedQuestions.length - 1 ? (
      <SubmitOverlay
        isSubmitting={true}
        message="Saving your profile and test results..."
      />
    ) : null;

  return (
    <div className="relative">
      {overlay}
      <div className="w-full flex justify-center items-center">
        <div className="w-[60%] my-6">
          <QuestionnaireComponent
            groups={groupedQuestions}
            answerTags={data?.answerTags || []}
            currentGroupIndex={workStyleCurrentGroupIndex}
            answers={workStyleAnswers}
            isLoading={isLoading}
            error={error}
            isSubmitting={isSubmitting}
            onAnswerChange={handleAnswerChange}
            onNext={handleNext}
            onBack={handleBack}
            getNextButtonText={renderButtonText}
          />
        </div>
      </div>
    </div>
  );
};

export default WorkStyleTestStep;
