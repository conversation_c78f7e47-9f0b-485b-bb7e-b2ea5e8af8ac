import React from 'react';
import {
  Control,
  useFieldArray,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { Trash2 } from 'lucide-react';

import { MainInformationFormSchema } from '../../(schemas)/mainInformation.schema';

import { Button } from '~/components/ui/button.tsx';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import { Input } from '~/components/ui/input.tsx';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select.tsx';
import { educationTypes } from '~/constants/educationTypes.ts';

type EducationFormProps = {
  control: Control<MainInformationFormSchema>;
  setValue: UseFormSetValue<MainInformationFormSchema>;
  watch: UseFormWatch<MainInformationFormSchema>;
};

export const EducationForm: React.FC<EducationFormProps> = ({ control }) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'education',
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Education</h2>
        <Button
          type="button"
          variant="default"
          size="sm"
          className="ml-auto"
          onClick={() =>
            append({
              type: '',
              institution: '',
              discipline: '',
              startYear: 0,
              endYear: 0,
            })
          }
        >
          Add Education
        </Button>
      </div>

      {fields.map((field, index) => (
        <div
          key={field.id}
          className="space-y-4 border p-4 rounded-lg mb-4 relative"
        >
          {index > 0 && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 text-destructive"
              onClick={() => remove(index)}
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          )}

          <FormField
            control={control}
            name={`education.${index}.type`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type Of Education</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Bachelor" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {educationTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name={`education.${index}.institution`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Institution</FormLabel>
                <FormControl>
                  <Input placeholder="Institution name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name={`education.${index}.discipline`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Discipline</FormLabel>
                <FormControl>
                  <Input placeholder="Discipline" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={control}
              name={`education.${index}.startYear`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Year</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="2020"
                      min={1940}
                      max={new Date().getFullYear()}
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name={`education.${index}.endYear`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Year</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="2024"
                      min={1940}
                      max={new Date().getFullYear() + 10}
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      ))}
    </div>
  );
};
