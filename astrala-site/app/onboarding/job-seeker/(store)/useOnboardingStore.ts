import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import type { MainInformationFormSchema } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';
import { getDefaultValues } from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';
import type { ParsedResume } from '~/app/onboarding/job-seeker/page';

export type OnboardingStep = 'upload-cv' | 'main-info' | 'work-style';

type OnboardingState = {
  currentStep: OnboardingStep;
  setCurrentStep: (step: OnboardingStep) => void;

  resumeFile: File | null;
  isResumeUploaded: boolean;
  parsedResumeData: ParsedResume | undefined;
  setResumeFile: (file: File | null) => void;
  setParsedResumeData: (data: ParsedResume | undefined) => void;

  resumeDataApplied: boolean;
  setResumeDataApplied: (applied: boolean) => void;

  formData: MainInformationFormSchema;
  updateFormData: (data: Partial<MainInformationFormSchema>) => void;
  setFormData: (data: MainInformationFormSchema) => void;

  workStyleAnswers: Map<number, number>;
  workStyleCurrentGroupIndex: number;
  updateWorkStyleAnswer: (questionId: number, value: number) => void;
  setWorkStyleAnswers: (answers: Map<number, number>) => void;
  setWorkStyleCurrentGroupIndex: (index: number) => void;

  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;

  canGoToStep: (step: OnboardingStep) => boolean;
  isStepCompleted: (step: OnboardingStep) => boolean;
};

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set, get) => ({
      currentStep: 'upload-cv',
      setCurrentStep: step => set({ currentStep: step }),

      resumeFile: null,
      isResumeUploaded: false,
      parsedResumeData: undefined,
      setResumeFile: file =>
        set({
          resumeFile: file,
          isResumeUploaded: file !== null,
        }),
      setParsedResumeData: data =>
        set({
          parsedResumeData: data,
          isResumeUploaded: data !== undefined,
        }),

      resumeDataApplied: false,
      setResumeDataApplied: applied => set({ resumeDataApplied: applied }),

      formData: getDefaultValues(),
      updateFormData: data =>
        set(state => ({
          formData: { ...state.formData, ...data },
        })),
      setFormData: data => set({ formData: data }),

      workStyleAnswers: new Map<number, number>(),
      workStyleCurrentGroupIndex: 0,
      updateWorkStyleAnswer: (questionId, value) =>
        set(state => {
          const newAnswers = new Map(state.workStyleAnswers);
          newAnswers.set(questionId, value);
          return { workStyleAnswers: newAnswers };
        }),
      setWorkStyleAnswers: answers => set({ workStyleAnswers: answers }),
      setWorkStyleCurrentGroupIndex: index =>
        set({ workStyleCurrentGroupIndex: index }),

      isSubmitting: false,
      setIsSubmitting: value => set({ isSubmitting: value }),

      canGoToStep: step => {
        const state = get();

        if (step === 'upload-cv') return true;

        if (step === 'main-info') {
          return state.isResumeUploaded || state.isStepCompleted('upload-cv');
        }

        if (step === 'work-style') {
          return state.isStepCompleted('main-info');
        }

        return false;
      },

      isStepCompleted: step => {
        const state = get();

        if (step === 'upload-cv') {
          return state.isResumeUploaded;
        }

        if (step === 'main-info') {
          const { personalInfo, education, workExperience, languages, skills } =
            state.formData;

          const hasPersonalInfo =
            personalInfo.fullName &&
            personalInfo.jobTitle &&
            personalInfo.phone &&
            personalInfo.country &&
            personalInfo.city;

          const hasEducation =
            education.length > 0 &&
            education[0].type &&
            education[0].institution &&
            education[0].discipline;

          const hasWorkExperience =
            workExperience.length > 0 &&
            workExperience[0].companyName &&
            workExperience[0].jobTitle;

          const hasLanguages =
            languages.length > 0 && languages[0].language && languages[0].level;

          const hasSkills = skills.length > 0;

          return Boolean(
            hasPersonalInfo &&
              hasEducation &&
              hasWorkExperience &&
              hasLanguages &&
              hasSkills,
          );
        }

        return false;
      },
    }),
    {
      name: 'onboarding-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        currentStep: state.currentStep,
        isResumeUploaded: state.isResumeUploaded,
        parsedResumeData: state.parsedResumeData,
        resumeDataApplied: state.resumeDataApplied,
        formData: {
          ...state.formData,
          certifications:
            state.formData.certifications?.map(cert => ({
              id: cert.id,
              name: cert.name,
              file_path: cert.file_path,
            })) || [],
        },
        workStyleCurrentGroupIndex: state.workStyleCurrentGroupIndex,
        workStyleAnswers: Array.from(state.workStyleAnswers.entries()),
      }),
      merge: (persisted, current) => {
        const persistedState = persisted as Partial<OnboardingState> & {
          workStyleAnswers?: [number, number][];
        };

        return {
          ...current,
          ...persistedState,
          formData: persistedState.formData
            ? {
                ...current.formData,
                ...persistedState.formData,
                certifications:
                  persistedState.formData.certifications?.map(cert => ({
                    ...cert,
                    file: null,
                  })) || [],
              }
            : current.formData,
          workStyleAnswers: persistedState.workStyleAnswers
            ? new Map(persistedState.workStyleAnswers)
            : current.workStyleAnswers,
        };
      },
    },
  ),
);
