'use client';

import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react';

import { MatchCardAccordionSkeleton } from '~/app/dashboard/(components)/(skeletons)/MatchCardAccordionSkeleton.tsx';
import { PaginationSkeleton } from '~/app/dashboard/(components)/(skeletons)/PaginationSkeleton.tsx';
import { MatchCard, MatchData } from '~/app/dashboard/(components)/MatchCard';
import Placeholder from '~/app/dashboard/(components)/Placeholder.tsx';
import { JobSeekerFiles } from '~/app/dashboard/employer/(components)/JobSeekerFiles.tsx';

import { PaginatedResponse } from '~/api/features/matches/job-seeker/actions';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';

type MatchesListProps<T extends MatchData> = {
  matches: T[] | PaginatedResponse<T>;
  isLoading?: boolean;
  error?: Error | null;
  showActions?: boolean;
  emptyMessage?: string;
  viewMode?: 'jobSeeker' | 'employer';
  useAccordion?: boolean;
  showFiles?: boolean;
  onPageChange?: (page: number) => void;
  children?: (match: T) => React.ReactNode;
};

export function MatchesList<T extends MatchData>({
  matches,
  isLoading = false,
  error = null,
  showActions = true,
  emptyMessage = 'No items found.',
  viewMode = 'jobSeeker',
  useAccordion = false,
  showFiles = false,
  onPageChange,
  children,
}: MatchesListProps<T>) {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  const isPaginated =
    matches &&
    typeof (matches as PaginatedResponse<T>).totalCount !== 'undefined';
  const matchesArray = isPaginated
    ? (matches as PaginatedResponse<T>).data
    : (matches as T[]);

  const currentPage = isPaginated
    ? (matches as PaginatedResponse<T>).currentPage
    : 1;
  const totalPages = isPaginated
    ? (matches as PaginatedResponse<T>).totalPages
    : 1;
  const hasNextPage = isPaginated
    ? (matches as PaginatedResponse<T>).hasNextPage
    : false;
  const hasPreviousPage = isPaginated
    ? (matches as PaginatedResponse<T>).hasPreviousPage
    : false;

  const toggleAccordion = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  const goToNextPage = () => {
    if (onPageChange && hasNextPage) {
      onPageChange(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (onPageChange && hasPreviousPage) {
      onPageChange(currentPage - 1);
    }
  };

  const shouldShowFiles = (match: T) => {
    if (!showFiles || !match.jobSeeker) return false;

    if (viewMode === 'employer') {
      return (
        match.employer_status === 'INTERESTED' &&
        match.job_seeker_status === 'INTERESTED'
      );
    }

    return (
      match.employer_status === 'PAID' && match.job_seeker_status === 'PAID'
    );
  };

  const renderPagination = () => {
    if (isLoading) {
      return (
        <div className="w-[70%]">
          <PaginationSkeleton />
        </div>
      );
    }

    if (!isPaginated || totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pageNumbers = [];
      const maxVisiblePages = 5;

      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        pageNumbers.push(1);

        let startPage = Math.max(2, currentPage - 1);
        let endPage = Math.min(totalPages - 1, currentPage + 1);

        if (currentPage <= 3) {
          endPage = Math.min(maxVisiblePages - 1, totalPages - 1);
        }

        if (currentPage >= totalPages - 2) {
          startPage = Math.max(2, totalPages - maxVisiblePages + 2);
        }

        if (startPage > 2) {
          pageNumbers.push('...');
        }

        for (let i = startPage; i <= endPage; i++) {
          pageNumbers.push(i);
        }

        if (endPage < totalPages - 1) {
          pageNumbers.push('...');
        }

        if (totalPages > 1) {
          pageNumbers.push(totalPages);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    return (
      <div className="flex w-[70%] items-center justify-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={goToPrevPage}
          disabled={!hasPreviousPage}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {pageNumbers.map(page =>
          typeof page === 'number' ? (
            <Button
              key={page}
              variant={currentPage === page ? 'default' : 'outline'}
              size="sm"
              onClick={() => onPageChange && onPageChange(page)}
              className="min-w-8 px-3"
            >
              {page}
            </Button>
          ) : (
            <span key={page} className="px-1">
              {page}
            </span>
          ),
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={goToNextPage}
          disabled={!hasNextPage}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  const getContentMinHeight = () => {
    const singleCardHeight = 140;
    const minCardCount = 5;
    const minHeight = minCardCount * singleCardHeight;
    return `${minHeight}px`;
  };

  if (error) {
    return (
      <div className="flex flex-col w-full">
        <div className="w-[70%] mb-auto">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              An error occurred while loading data: {error.message}
            </AlertDescription>
          </Alert>
        </div>
        <div className="mt-6">{renderPagination()}</div>
      </div>
    );
  }

  if (!isLoading && (!matchesArray || matchesArray.length === 0)) {
    return (
      <div className="flex flex-col w-full">
        <div
          className="w-full mb-auto"
          style={{ minHeight: getContentMinHeight() }}
        >
          <Placeholder
            title="Sorry, we have nothing here."
            description={emptyMessage}
          />
        </div>
        <div className="mt-6">{renderPagination()}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full">
      <div className="space-y-2" style={{ minHeight: getContentMinHeight() }}>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, idx) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={idx} className="flex flex-row gap-4">
                <div className="w-[70%]">
                  <MatchCardAccordionSkeleton />
                </div>
                {useAccordion && <div className="w-[30%]" />}
              </div>
            ))}
          </div>
        ) : (
          matchesArray.map((match, index) => {
            const hasFiles = shouldShowFiles(match);

            return (
              <div
                key={match.id}
                className={`flex ${useAccordion ? 'flex-row gap-4' : 'flex-col gap-2'}`}
              >
                <div className={useAccordion ? 'w-[70%]' : 'w-full'}>
                  <MatchCard
                    match={match}
                    viewMode={viewMode}
                    isAccordion={useAccordion}
                    isExpanded={expandedIndex === index}
                    onToggle={() => toggleAccordion(index)}
                  />
                </div>

                {useAccordion && (
                  <AnimatePresence>
                    {expandedIndex === index && (
                      <motion.div
                        className="w-[30%] self-start space-y-3"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        {showActions && children && (
                          <div>{children(match)}</div>
                        )}
                        {hasFiles && (
                          <JobSeekerFiles
                            fullName={match.jobSeeker?.full_name!}
                            resumePath={match.jobSeeker?.resume_path}
                            drivingLicensePath={
                              match.jobSeeker?.driving_license_path
                            }
                            passportPath={match.jobSeeker?.passport_path}
                            certifications={match.jobSeeker?.certifications}
                          />
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}

                {showActions && children && !useAccordion && (
                  <div className="flex justify-center -mt-4">
                    {children(match)}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
      <div className="mt-6 flex justify-start">{renderPagination()}</div>
    </div>
  );
}
