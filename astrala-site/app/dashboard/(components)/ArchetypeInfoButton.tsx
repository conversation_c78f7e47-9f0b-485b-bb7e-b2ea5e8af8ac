'use client';

import React, { useState } from 'react';
import { InfoIcon, MapPin } from 'lucide-react';

import {
  useGetArchetypeById,
  useGetUserArchetypeScores,
} from '~/api/features/archetypes/queries';
import UserIcon from '~/assets/icons/user.svg';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Skeleton } from '~/components/ui/skeleton';

type ArchetypeInfoButtonProps = {
  jobSeekerId: number;
  jobSeekerName?: string;
  jobTitle?: string;
  location?: string;
};

export function ArchetypeInfoButton({
  jobSeekerId,
  jobSeekerName,
  jobTitle,
  location,
}: ArchetypeInfoButtonProps) {
  const [open, setOpen] = useState(false);

  const { data: userScores, isLoading: isLoadingScores } =
    useGetUserArchetypeScores(jobSeekerId);
  const topArchetype = userScores?.archetypes?.[0];

  const { data: archetypeDetail, isLoading: isLoadingDetail } =
    useGetArchetypeById(topArchetype ? topArchetype.id : undefined);

  const isLoading = isLoadingScores || isLoadingDetail;
  const buttonText = isLoading
    ? 'Loading...'
    : archetypeDetail?.name || 'No data';

  if (!archetypeDetail?.name) return;

  const renderContent = () => {
    if (isLoading) {
      return (
        <div>
          <div className="flex items-center mb-6">
            <Skeleton className="w-16 h-16 rounded-full mr-4" />
            <div className="flex-1">
              <Skeleton className="h-6 w-40 mb-2" />
              <Skeleton className="h-4 w-60" />
            </div>
          </div>
          <Skeleton className="h-10 w-48 mb-4" />
          <Skeleton className="h-20 w-full mb-4" />
          <Skeleton className="h-20 w-full" />
        </div>
      );
    }

    if (!topArchetype || !archetypeDetail) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            No archetype data available for this candidate
          </p>
        </div>
      );
    }

    return (
      <div>
        <div className="flex items-center mb-8">
          <div className="flex-shrink-0 p-4 bg-muted border rounded-full flex items-center justify-center mr-4">
            <UserIcon className="w-12 h-12 text-primary" />
          </div>
          <div className="flex flex-col justify-around flex-grow">
            <h2 className="text-2xl font-semibold">
              {jobSeekerName || 'Unnamed Candidate'}
            </h2>
            <div className="mt-1 flex flex-wrap items-center text-lg">
              {jobTitle && (
                <div className="flex items-center mr-4">
                  <InfoIcon className="w-5 h-5 mr-1 text-primary" />
                  <span>{jobTitle}</span>
                </div>
              )}
              {location && (
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 mr-1 text-primary" />
                  <span>{location}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="flex items-center gap-3 mb-4">
            <h3 className="text-4xl font-bold">{archetypeDetail.name}</h3>
          </div>
        </div>

        {archetypeDetail.description && (
          <div className="mb-6">
            <h4 className="font-semibold text-xl mb-2">Description</h4>
            <p className="text-muted-foreground leading-relaxed">
              {archetypeDetail.description}
            </p>
          </div>
        )}

        {archetypeDetail.metaphor && (
          <div>
            <h4 className="font-semibold text-xl mb-2">Metaphor</h4>
            <p className="text-muted-foreground leading-relaxed">
              {archetypeDetail.metaphor}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default">{buttonText}</Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">
            Interview Fit Scorecard
          </DialogTitle>
        </DialogHeader>

        <div className="mt-6">{renderContent()}</div>
        <div className="mt-6 pt-4 border-t">
          <Button
            variant="default"
            className="w-full"
            onClick={() => {
              if (!archetypeDetail || !topArchetype) return;

              const params = new URLSearchParams({
                name: jobSeekerName || 'Unnamed Candidate',
                jobTitle: jobTitle || '',
                location: location || '',
                archetypeName: archetypeDetail.name,
                percentage: topArchetype.percentage.toString(),
                description: archetypeDetail.description || '',
                metaphor: archetypeDetail.metaphor || '',
              });

              window.open(`/print/archetype?${params.toString()}`, '_blank');
            }}
          >
            Download Interview Fit Scorecard (PDF)
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
