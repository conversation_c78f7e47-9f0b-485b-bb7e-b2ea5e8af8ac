'use client';

import { useEffect, useState } from 'react';
import { UserPlus } from 'lucide-react';
import { z } from 'zod';

import { useSendInvitation } from '~/api/features/company/queries';
import { Button } from '~/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { useToast } from '~/hooks/use-toast';

const emailSchema = z.string().email('Please enter a valid email address');

export function InviteEmployeeDialog() {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const sendInvitation = useSendInvitation();

  useEffect(() => {
    if (!email) {
      setIsEmailValid(false);
      setValidationError(null);
      return;
    }

    const result = emailSchema.safeParse(email);
    setIsEmailValid(result.success);
    setValidationError(result.success ? null : result.error.errors[0].message);
  }, [email]);

  const handleSendInvitation = async () => {
    if (!email || !isEmailValid) {
      toast({
        title: 'Error',
        description: 'Please enter a valid email address.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const result = await sendInvitation.mutateAsync({ email });

      if (!result.success) {
        throw new Error(result.error || 'Failed to send invitation');
      }

      toast({
        title: 'Invitation Sent',
        description: `Invitation has been sent to ${email}`,
      });

      setEmail('');
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to send invitation. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2" variant="default">
          <div className="flex items-center gap-2">
            <UserPlus size={16} />
            Invite an Employee
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Invite Employee</DialogTitle>
          <DialogDescription>
            Send an invitation email to a new employee to join your company. The
            invitation will be valid for 48 hours.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <Label htmlFor="email">Email Address</Label>
          <div className="flex gap-2 mt-1">
            <Input
              id="email"
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="flex-1"
            />
          </div>
          {validationError && (
            <p className="text-sm text-destructive mt-1">{validationError}</p>
          )}
          <p className="text-sm text-muted-foreground mt-2">
            An invitation email will be sent to this address with instructions
            to join your company.
          </p>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="secondary" onClick={() => setIsDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="default"
            onClick={handleSendInvitation}
            disabled={sendInvitation.isPending || !isEmailValid || !email}
          >
            {sendInvitation.isPending ? 'Sending...' : 'Send Invitation'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
