'use client';
import React, { useState } from 'react';
import { format } from 'date-fns';
import { MoreHorizontal } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { useUpdateVacancyStatus } from '~/api/entities/vacancy/queries';
import { useCompanyVacancies } from '~/api/features/company/queries';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card } from '~/components/ui/card.tsx';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { Skeleton } from '~/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { useToast } from '~/hooks/use-toast.ts';
import { useAuth } from '~/providers/AuthProvider';
import { useEmployerVacancyStore } from '~/store/employer-vacancies-store';

export default function VacanciesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { employerProfile } = useAuth();
  const { data: vacancies, isLoading, refetch } = useCompanyVacancies();
  const updateVacancyStatusMutation = useUpdateVacancyStatus();
  const { setSelectedVacancyId } = useEmployerVacancyStore();

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [vacancyToClose, setVacancyToClose] = useState<string | null>(null);

  const handleAddVacancy = () => {
    router.push('/dashboard/employer/add-vacancy');
  };

  const handleEditVacancy = (id: string) => {
    router.push(`/dashboard/employer/edit-vacancy/${id}`);
  };

  const openCloseVacancyConfirmation = (id: string) => {
    setVacancyToClose(id);
    setIsConfirmDialogOpen(true);
  };

  const confirmCloseVacancy = async () => {
    if (vacancyToClose) {
      try {
        await updateVacancyStatusMutation.mutateAsync({
          id: vacancyToClose,
          status: 'CLOSED',
        });
        await refetch();
        toast({
          title: 'Vacancy closed',
          description: 'The vacancy has been closed successfully.',
        });
      } catch (error) {
        console.error('Error updating vacancy status:', error);
        toast({
          title: 'Error',
          description: 'Failed to close vacancy. Please try again.',
          variant: 'destructive',
        });
      }
    }
    setIsConfirmDialogOpen(false);
    setVacancyToClose(null);
  };

  const handleVacancyStatus = async (
    id: string,
    status: 'OPENED' | 'CLOSED',
  ) => {
    if (status === 'CLOSED') {
      openCloseVacancyConfirmation(id);
      return;
    }

    try {
      await updateVacancyStatusMutation.mutateAsync({ id, status });
      await refetch();
      toast({
        title: 'Vacancy opened',
        description: 'The vacancy has been opened successfully.',
      });
    } catch (error) {
      console.error('Error updating vacancy status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update vacancy status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleShowSuggestions = (id: number) => {
    setSelectedVacancyId(id);
    router.push('/dashboard/employer/suggestions');
  };

  const handleShowInterested = (id: number) => {
    setSelectedVacancyId(id);
    router.push('/dashboard/employer/pending/job-seeker-interested');
  };

  const handleCompleteWorkStyleTest = (id: number) => {
    setSelectedVacancyId(id);
    router.push(`/dashboard/employer/work-style-test/${id}`);
  };

  if (!employerProfile) {
    return <div>Loading profile...</div>;
  }

  const renderVacanciesContent = () => {
    if (isLoading) {
      return (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vacancy</TableHead>
              <TableHead>Created By</TableHead>
              <TableHead>Posting Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...new Array(5)].map((_, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-6 w-48" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-28" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-6 w-8 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      );
    }

    if (!vacancies || vacancies.length === 0) {
      return (
        <div className="text-center py-6 text-muted-foreground">
          Your company doesn't have any vacancies yet. Create your first vacancy
          to get started.
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Vacancy</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {vacancies.map(vacancy => (
            <TableRow key={vacancy.id}>
              <TableCell className="font-medium">{vacancy.title}</TableCell>
              <TableCell>{vacancy.employer?.full_name || 'Unknown'}</TableCell>
              <TableCell>
                {format(new Date(vacancy.created_at), 'MMM d, yyyy')}
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    vacancy.status === 'CLOSED' ? 'secondary' : 'outline'
                  }
                >
                  {vacancy.status === 'CLOSED' ? 'Closed' : 'Active'}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 relative cursor-pointer"
                    >
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                      {!vacancy.has_questionnaire_answers && (
                        <div className="absolute top-0 right-0 w-2 h-2 bg-red rounded-full" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {vacancy.status === 'CLOSED' ? (
                      <>
                        {!vacancy.has_questionnaire_answers && (
                          <>
                            <DropdownMenuItem
                              onClick={() =>
                                handleCompleteWorkStyleTest(Number(vacancy.id))
                              }
                              className="relative"
                            >
                              Complete Behavioural Profile
                              <div className="ml-2 w-2 h-2 bg-destructive rounded-full" />
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                          </>
                        )}
                        <DropdownMenuItem
                          onClick={() =>
                            handleVacancyStatus(vacancy.id, 'OPENED')
                          }
                          disabled={!vacancy.has_questionnaire_answers}
                          className={
                            !vacancy.has_questionnaire_answers
                              ? 'text-muted-foreground cursor-not-allowed'
                              : ''
                          }
                        >
                          Open vacancy
                        </DropdownMenuItem>
                      </>
                    ) : (
                      <>
                        <DropdownMenuItem
                          onClick={() => handleEditVacancy(vacancy.id)}
                        >
                          Edit vacancy
                        </DropdownMenuItem>
                        {!vacancy.has_questionnaire_answers && (
                          <>
                            <DropdownMenuItem
                              onClick={() =>
                                handleCompleteWorkStyleTest(Number(vacancy.id))
                              }
                              className="relative"
                            >
                              Complete Behavioural Profile
                              <div className="ml-2 w-2 h-2 bg-destructive rounded-full" />
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            handleShowSuggestions(Number(vacancy.id))
                          }
                        >
                          All vacancy suggestions
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleShowInterested(Number(vacancy.id))
                          }
                        >
                          Job seeker interested
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive font-medium"
                          onClick={() =>
                            handleVacancyStatus(vacancy.id, 'CLOSED')
                          }
                        >
                          Close vacancy
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <div className="container p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Your Active Roles</h1>
        <Button
          onClick={handleAddVacancy}
          className="flex items-center gap-2"
          variant="default"
        >
          Add New Vacancy
        </Button>
      </div>

      <Card className="border rounded-md p-2">{renderVacanciesContent()}</Card>

      <AlertDialog
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to close this vacancy. It will no longer be visible
              to job seekers. You can reopen it later if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCloseVacancy}>
              Close Vacancy
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
