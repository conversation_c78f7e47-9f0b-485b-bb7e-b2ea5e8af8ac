'use client';

import { useEffect, useState } from 'react';
import { format, parseISO } from 'date-fns';
import { useRouter } from 'next/navigation';

import { ActionButtonsCard } from '~/app/dashboard/(components)/ActionButtonsCard.tsx';
import { MatchData } from '~/app/dashboard/(components)/MatchCard';

import { useCreateChat } from '~/api/features/chats/queries.ts';
import {
  useCanEmployerChangeRejection,
  useMarkEmployerInterested,
  useMarkEmployerRejected,
} from '~/api/features/matches/employer/queries';
import { Button } from '~/components/ui/button';
import { useToast } from '~/hooks/use-toast.ts';
import { useAuth } from '~/providers/AuthProvider.tsx';

type ActionButtonsBaseProps = {
  match: MatchData;
  onSuccess?: () => void;
  isLoading?: boolean;
  onInterested?: () => Promise<void> | void;
  onRejected?: () => Promise<void> | void;
  disabled?: boolean;
};

export function SuggestionsActionButtons({
  match,
  onSuccess,
  isLoading: externalLoading,
  onInterested,
  onRejected,
  disabled,
}: ActionButtonsBaseProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const { markAsInterested, isLoading: isMarkingInterested } =
    useMarkEmployerInterested();
  const { markAsRejected, isLoading: isMarkingRejected } =
    useMarkEmployerRejected();

  const handleInterested = async () => {
    if (onInterested) {
      return onInterested();
    }

    setIsLoading(true);
    try {
      await markAsInterested(match.id);
      toast({
        title: 'Success!',
        description: "You've expressed interest in this candidate.",
      });
      router.push('/dashboard/employer/matches');
      if (onSuccess) onSuccess();
      if (match.job_seeker_status === 'INTERESTED') {
        toast({
          title: 'Match Created!',
          description: 'You both are interested! Check the Matches page.',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejected = async () => {
    if (onRejected) {
      return onRejected();
    }

    setIsLoading(true);
    try {
      await markAsRejected(match.id);
      toast({
        title: 'Success!',
        description: "You've rejected this candidate.",
      });
      if (onSuccess) onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isDisabled =
    externalLoading !== undefined
      ? externalLoading
      : isLoading || isMarkingInterested || isMarkingRejected;

  return (
    <ActionButtonsCard
      title="Provide your feedback"
      description={`Clicking "Interested" will move this suggestion to a pending match and notify the candidate of your interest.
      If a candidate also expresses interest in your vacancy, you will be able to see full candidate's details.
      Clicking "Reject" will remove the suggestion. You can change your decision once within 24 hours.`}
    >
      <Button
        variant="default"
        className="w-full"
        onClick={handleInterested}
        disabled={
          isDisabled || match.employer_status === 'INTERESTED' || disabled
        }
      >
        Interested
      </Button>
      <Button
        variant="destructive"
        className="w-full"
        onClick={handleRejected}
        disabled={
          isDisabled || match.employer_status === 'REJECTED' || disabled
        }
      >
        Reject
      </Button>
    </ActionButtonsCard>
  );
}

export function IRejectedActionButtons({
  match,
  onSuccess,
  isLoading: externalLoading,
  onInterested,
}: ActionButtonsBaseProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string | null>(null);
  const { markAsInterested, isLoading: isMarkingInterested } =
    useMarkEmployerInterested();
  const { data: canChangeRejection, isLoading: isCheckingChangeability } =
    useCanEmployerChangeRejection(match.id);

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  useEffect(() => {
    if (canChangeRejection?.deadline) {
      const updateTimeLeft = () => {
        try {
          const deadline = parseISO(canChangeRejection.deadline!);
          const now = new Date();

          if (deadline > now) {
            const formattedTimeLeft = `${format(deadline, 'PPP')} at ${format(deadline, 'p')}`;
            setTimeLeft(formattedTimeLeft);
          } else {
            setTimeLeft(null);
          }
        } catch (error) {
          setTimeLeft(null);
        }
      };

      updateTimeLeft();
      const interval = setInterval(updateTimeLeft, 60000);

      return () => clearInterval(interval);
    }
    setTimeLeft(null);
  }, [canChangeRejection]);

  const handleInterested = async () => {
    if (onInterested) {
      return onInterested();
    }

    setIsLoading(true);
    try {
      await markAsInterested(match.id);
      toast({
        title: 'Success!',
        description: "You've expressed interest in this candidate.",
      });
      if (onSuccess) onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isDisabled =
    externalLoading !== undefined
      ? externalLoading
      : isLoading ||
        isMarkingInterested ||
        isCheckingChangeability ||
        !canChangeRejection?.canChange;

  return (
    <ActionButtonsCard
      title={
        timeLeft ? 'You can change your decision' : 'Decision period expired'
      }
      description={
        timeLeft
          ? `You can change your rejection decision until ${timeLeft}.`
          : 'The 24-hour period to change your decision has expired.'
      }
    >
      <Button
        variant="default"
        className="w-full"
        onClick={handleInterested}
        disabled={isDisabled}
      >
        Interested
      </Button>
    </ActionButtonsCard>
  );
}

export function MatchesActionButtons({ match }: { match: MatchData }) {
  const router = useRouter();
  const { toast } = useToast();
  const { employerProfile } = useAuth();
  const createChatMutation = useCreateChat();

  const handleStartConversation = async () => {
    if (!employerProfile?.id) {
      toast({
        title: 'Error',
        description: 'Unable to identify your account. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const result = await createChatMutation.mutateAsync(match.id);

      if (result.success && result.chat_id) {
        toast({
          title: 'Success!',
          description: 'Chat created successfully!',
        });
        router.push(`/dashboard/employer/chats/${result.chat_id}`);
      } else {
        toast({
          title: 'Error',
          description:
            result.error || 'Failed to create chat. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      toast({
        title: 'Error',
        description: 'Failed to create chat. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <ActionButtonsCard
      title="Matched Candidate"
      description="You both expressed interest! Full candidate information is now available."
    >
      <Button
        variant="default"
        className="w-full"
        onClick={handleStartConversation}
        disabled={createChatMutation.isPending}
      >
        {createChatMutation.isPending
          ? 'Creating Chat...'
          : 'Start Conversation'}
      </Button>
    </ActionButtonsCard>
  );
}
