'use client';

import React from 'react';
import { format } from 'date-fns';
import { InfoIcon, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { useJobSeekerCanEditWorkStyle } from '~/api/entities/job-seeker/queries';
import {
  useGetArchetypeById,
  useGetArchetypePinnedStatus,
  useGetUserArchetypeScores,
  useToggleArchetypePinned,
} from '~/api/features/archetypes/queries';
import UserIcon from '~/assets/icons/user.svg';
import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

export default function EditSymbolicProfilePage() {
  const router = useRouter();
  const { toast } = useToast();
  const { jobSeekerProfile, isLoading: isLoadingAuth } = useAuth();
  const jobSeekerId = jobSeekerProfile?.id as number | undefined;

  const { data: userScores, isLoading: isLoadingScores } =
    useGetUserArchetypeScores(jobSeekerId);
  const topArchetype = userScores?.archetypes?.[0];

  const { data: archetypeDetail, isLoading: isLoadingDetail } =
    useGetArchetypeById(topArchetype ? topArchetype.id : undefined);

  const { data: canEditWorkStyleData, isLoading: isLoadingEditWorkStyle } =
    useJobSeekerCanEditWorkStyle(jobSeekerId);

  const { data: pinnedStatusData, isLoading: isLoadingPinned } =
    useGetArchetypePinnedStatus(jobSeekerId);

  const togglePinnedMutation = useToggleArchetypePinned();

  const isLoading =
    isLoadingAuth ||
    isLoadingScores ||
    isLoadingDetail ||
    isLoadingEditWorkStyle ||
    isLoadingPinned;

  const canEditWorkStyle = canEditWorkStyleData?.canEdit ?? false;
  const isPinned = pinnedStatusData?.pinned ?? true;

  const getNextEditDate = (updatedAt: string | null) => {
    if (!updatedAt) return null;

    const lastUpdate = new Date(updatedAt);
    const nextDate = new Date(lastUpdate);
    nextDate.setHours(nextDate.getHours() + 24);

    return nextDate;
  };

  const nextWorkStyleEditDate = jobSeekerProfile?.work_style_updated_at
    ? getNextEditDate(jobSeekerProfile.work_style_updated_at)
    : null;

  const handleTogglePin = async () => {
    if (!jobSeekerId) return;

    try {
      await togglePinnedMutation.mutateAsync(jobSeekerId);
      toast({
        title: isPinned ? 'Unpinned' : 'Pinned',
        description: isPinned
          ? 'Archetype unpinned from profile'
          : 'Archetype pinned to profile',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update pin status',
        variant: 'destructive',
      });
    }
  };

  const handleEditWorkStyle = () => {
    if (canEditWorkStyle) {
      router.push('/dashboard/job-seeker/edit-work-style');
    } else {
      toast({
        title: 'Editing Restricted',
        description:
          'You can only update your Behavioural Profile preferences once every 24 hours.',
        variant: 'destructive',
      });
    }
  };

  const getPinButtonText = () => {
    if (togglePinnedMutation.isPending) {
      return 'Updating...';
    }
    return isPinned ? 'Unpin from Profile' : 'Pin to Profile';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
        <p>Loading profile...</p>
      </div>
    );
  }

  if (!jobSeekerProfile) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>No profile found.</p>
      </div>
    );
  }

  if (!topArchetype || !archetypeDetail) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>No archetype data available.</p>
      </div>
    );
  }

  return (
    <div className="w-full p-8">
      <div className="flex gap-6 w-full">
        <div className="w-[70%]">
          <Card>
            <CardContent className="p-8">
              <div className="flex items-start justify-between mb-8">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-4 bg-muted border rounded-full flex items-center justify-center mr-4">
                    <UserIcon className="w-12 h-12 text-primary" />
                  </div>
                  <div className="flex flex-col justify-around flex-grow">
                    <h2 className="text-2xl font-semibold">
                      {jobSeekerProfile?.full_name || 'Unnamed Candidate'}
                    </h2>
                    <div className="mt-1 flex flex-wrap items-center text-lg">
                      {jobSeekerProfile?.job_title && (
                        <div className="flex items-center mr-4">
                          <InfoIcon className="w-5 h-5 mr-1 text-primary" />
                          <span>{jobSeekerProfile.job_title}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <Button
                  variant={isPinned ? 'destructive' : 'default'}
                  onClick={handleTogglePin}
                  disabled={togglePinnedMutation.isPending}
                >
                  {getPinButtonText()}
                </Button>
              </div>

              <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <h3 className="text-4xl font-bold">{archetypeDetail.name}</h3>
                </div>
              </div>

              {archetypeDetail.description && (
                <div className="mb-6">
                  <h4 className="font-semibold text-xl mb-2">Description</h4>
                  <p className="text-muted-foreground leading-relaxed">
                    {archetypeDetail.description}
                  </p>
                </div>
              )}

              {archetypeDetail.metaphor && (
                <div>
                  <h4 className="font-semibold text-xl mb-2">Metaphor</h4>
                  <p className="text-muted-foreground leading-relaxed">
                    {archetypeDetail.metaphor}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="w-[30%]">
          <Card>
            <CardHeader>
              <CardTitle>Refresh Your Behavioural Insights</CardTitle>
              <CardDescription>
                You can update your answers in the questionnaire. A new attempt
                will be available 24 hours after your last completion.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <Button
                        variant="default"
                        onClick={handleEditWorkStyle}
                        disabled={!canEditWorkStyle}
                        className="w-full"
                      >
                        Refresh Your Behavioural Insights
                      </Button>
                    </div>
                  </TooltipTrigger>
                  {!canEditWorkStyle && nextWorkStyleEditDate && (
                    <TooltipContent>
                      <p>
                        Next edit available on{' '}
                        {format(nextWorkStyleEditDate, 'PPP')} at{' '}
                        {format(nextWorkStyleEditDate, 'p')}.
                      </p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>

              {nextWorkStyleEditDate && !canEditWorkStyle && (
                <div className="text-sm text-center mt-2 text-muted-foreground">
                  Next edit available on {format(nextWorkStyleEditDate, 'PPP')}{' '}
                  at {format(nextWorkStyleEditDate, 'p')}.
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
