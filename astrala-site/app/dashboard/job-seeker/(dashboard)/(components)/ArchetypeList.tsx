'use client';
import React from 'react';
import { useRouter } from 'next/navigation';

import { ArchetypeScore } from '~/api/features/archetypes/types.ts';
import { Button } from '~/components/ui/button';
import { Card } from '~/components/ui/card.tsx';
import { Skeleton } from '~/components/ui/skeleton';

const ArchetypeList = ({
  archetypes,
  isLoading,
}: {
  archetypes: ArchetypeScore[] | undefined;
  isLoading?: boolean;
}) => {
  const router = useRouter();

  if (isLoading || !archetypes) {
    return (
      <div className="space-y-6">
        {[...new Array(6)].map((_, i) => (
          // eslint-disable-next-line react/no-array-index-key
          <Card key={i} className="p-6">
            <div className="flex items-center mb-3 gap-2">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-7 w-12 rounded-md" />
            </div>
            <Skeleton className="w-full h-4 rounded-full" />
          </Card>
        ))}
      </div>
    );
  }

  if (!isLoading && archetypes?.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground mb-4">
          Complete Work style test to see your archetype
        </p>
        <Button
          variant="default"
          onClick={() => router.push('/dashboard/job-seeker/settings')}
        >
          Go to Settings
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {archetypes.map(archetype => (
        <Card key={archetype.id} className="p-6">
          <div className="flex items-center mb-3 gap-2">
            <h4 className="font-medium text-foreground text-2xl">
              {archetype.name}
            </h4>
            <div className="px-2 py-1 rounded-md text-xs font-semibold bg-primary">
              {archetype.percentage}%
            </div>
          </div>

          <div
            className="w-full rounded-full h-4"
            style={{
              background:
                'linear-gradient(270deg, rgba(255, 255, 255, 0.2) 0%, rgba(95, 95, 95, 0.2) 100%)',
            }}
          >
            <div
              className="h-4 rounded-full transition-all duration-300"
              style={{
                width: `${archetype.percentage}%`,
                background: 'linear-gradient(270deg, #4B9C88 0%, #181D19 100%)',
                boxShadow: '0 0 8px #4B9C88',
              }}
            />
          </div>
        </Card>
      ))}
    </div>
  );
};

export default ArchetypeList;
