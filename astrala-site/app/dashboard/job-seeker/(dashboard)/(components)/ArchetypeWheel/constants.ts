import DeliberateIcon from '~/assets/icons/archetypes/deliberate.svg';
import FacilitativeIcon from '~/assets/icons/archetypes/facilitative.svg';
import FormalIcon from '~/assets/icons/archetypes/formal.svg';
import IndependentIcon from '~/assets/icons/archetypes/independent.svg';
import QualitySafetyIcon from '~/assets/icons/archetypes/quality-safety.svg';
import ReactiveIcon from '~/assets/icons/archetypes/reactive.svg';
import ResistantIcon from '~/assets/icons/archetypes/resistant.svg';
import StructuredIcon from '~/assets/icons/archetypes/structured.svg';

export type DimensionConfig = {
  icon: React.ComponentType;
  description: string;
  leftLabel: string;
  rightLabel: string;
};

export const DIMENSION_CONFIG: Record<string, DimensionConfig> = {
  'Decision-Making Style': {
    icon: DeliberateIcon,
    description: 'How you approach choices',
    leftLabel: 'Deliberate',
    rightLabel: 'Decisive',
  },
  'Collaboration Ethos': {
    icon: IndependentIcon,
    description: 'Team vs solo work preference',
    leftLabel: 'Independent',
    rightLabel: 'Integrative',
  },
  'Stress & Ambiguity Response': {
    icon: ReactiveIcon,
    description: 'How you handle pressure',
    leftLabel: 'Reactive',
    rightLabel: 'Regulated',
  },
  'Delivery Priorities': {
    icon: QualitySafetyIcon,
    description: 'Quality vs speed focus',
    leftLabel: 'Quality/Safety',
    rightLabel: 'Programme/Speed',
  },
  'Work Environment Fit': {
    icon: StructuredIcon,
    description: 'Structure vs flexibility',
    leftLabel: 'Structured',
    rightLabel: 'Flexible',
  },
  'Leadership Approach': {
    icon: FacilitativeIcon,
    description: 'How you lead others',
    leftLabel: 'Directive',
    rightLabel: 'Facilitative',
  },
  'Communication Style': {
    icon: FormalIcon,
    description: 'How you communicate',
    leftLabel: 'Formal',
    rightLabel: 'Open',
  },
  'Adaptability & Learning': {
    icon: ResistantIcon,
    description: 'Response to change',
    leftLabel: 'Resistant',
    rightLabel: 'Receptive',
  },
};
