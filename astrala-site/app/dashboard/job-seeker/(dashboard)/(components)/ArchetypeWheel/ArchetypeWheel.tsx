import React, { useEffect, useState } from 'react';

import ArchetypeWheelSkeleton from '~/app/dashboard/job-seeker/(dashboard)/(components)/(skeletons)/ArchetypeWheelSkeleton.tsx';

import { DIMENSION_CONFIG } from './constants.ts';
import { useGetDimensions } from '~/api/features/archetypes/queries.ts';
import { DimensionWithConfig } from '~/api/features/archetypes/types.ts';

const colors = {
  primary: '#4B9C88',
  primaryDark: '#181D19',
  secondary: '#1f2937',
  secondaryLight: '#374151',
  background: '#101010',
  text: '#ffffff',
  textSecondary: '#9ca3af',
  textMuted: '#6b7280',
  border: 'rgba(75, 85, 99, 0.5)',
  glow: 'rgba(75, 156, 136, 0.6)',
  glowBright: 'rgba(75, 156, 136, 0.8)',
  gradientStart: 'rgba(75, 156, 136, 0.1)',
  gradientEnd: 'rgba(75, 156, 136, 0.05)',
};

const createRoundedRingSectorPath = (
  centerX: number,
  centerY: number,
  innerR: number,
  outerR: number,
  startAngle: number,
  endAngle: number,
): string => {
  const startAngleRad = (startAngle * Math.PI) / 180;
  const endAngleRad = (endAngle * Math.PI) / 180;
  const cornerRadius = 15;

  const outerX1 = centerX + outerR * Math.cos(startAngleRad);
  const outerY1 = centerY + outerR * Math.sin(startAngleRad);
  const outerX2 = centerX + outerR * Math.cos(endAngleRad);
  const outerY2 = centerY + outerR * Math.sin(endAngleRad);

  const innerX1 = centerX + innerR * Math.cos(startAngleRad);
  const innerY1 = centerY + innerR * Math.sin(startAngleRad);
  const innerX2 = centerX + innerR * Math.cos(endAngleRad);
  const innerY2 = centerY + innerR * Math.sin(endAngleRad);

  const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

  const angleOffset = cornerRadius / outerR;
  const innerAngleOffset = cornerRadius / innerR;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerX1_start =
    centerX + outerR * Math.cos(startAngleRad + angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerY1_start =
    centerY + outerR * Math.sin(startAngleRad + angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerX2_end = centerX + outerR * Math.cos(endAngleRad - angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const outerY2_end = centerY + outerR * Math.sin(endAngleRad - angleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerX1_start =
    centerX + innerR * Math.cos(startAngleRad + innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerY1_start =
    centerY + innerR * Math.sin(startAngleRad + innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerX2_end =
    centerX + innerR * Math.cos(endAngleRad - innerAngleOffset);
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const innerY2_end =
    centerY + innerR * Math.sin(endAngleRad - innerAngleOffset);

  const midX1 = centerX + (outerR - cornerRadius) * Math.cos(startAngleRad);
  const midY1 = centerY + (outerR - cornerRadius) * Math.sin(startAngleRad);
  const midX2 = centerX + (outerR - cornerRadius) * Math.cos(endAngleRad);
  const midY2 = centerY + (outerR - cornerRadius) * Math.sin(endAngleRad);

  const innerMidX1 =
    centerX + (innerR + cornerRadius) * Math.cos(startAngleRad);
  const innerMidY1 =
    centerY + (innerR + cornerRadius) * Math.sin(startAngleRad);
  const innerMidX2 = centerX + (innerR + cornerRadius) * Math.cos(endAngleRad);
  const innerMidY2 = centerY + (innerR + cornerRadius) * Math.sin(endAngleRad);

  return `
    M ${outerX1_start} ${outerY1_start}
    A ${outerR} ${outerR} 0 ${largeArcFlag} 1 ${outerX2_end} ${outerY2_end}
    Q ${outerX2} ${outerY2} ${midX2} ${midY2}
    L ${innerMidX2} ${innerMidY2}
    Q ${innerX2} ${innerY2} ${innerX2_end} ${innerY2_end}
    A ${innerR} ${innerR} 0 ${largeArcFlag} 0 ${innerX1_start} ${innerY1_start}
    Q ${innerX1} ${innerY1} ${innerMidX1} ${innerMidY1}
    L ${midX1} ${midY1}
    Q ${outerX1} ${outerY1} ${outerX1_start} ${outerY1_start}
    Z
  `;
};

const ArchetypeWheel: React.FC = () => {
  const { data: dimensions, isLoading } = useGetDimensions();
  const [selectedDimension, setSelectedDimension] = useState<number | null>(
    null,
  );

  const [hasAnimated, setHasAnimated] = useState(() => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem('dimension-wheel-animated') === 'true';
    }
    return false;
  });

  const [hoveredDimension, setHoveredDimension] = useState<number | null>(null);

  useEffect(() => {
    if (dimensions?.length && selectedDimension === null) {
      setSelectedDimension(dimensions[0].id);
    }
  }, [dimensions, selectedDimension]);

  useEffect(() => {
    if (!hasAnimated && typeof window !== 'undefined') {
      sessionStorage.setItem('dimension-wheel-animated', 'true');
      setHasAnimated(true);
    }
  }, [hasAnimated]);

  const dimensionsWithConfig: DimensionWithConfig[] = React.useMemo(() => {
    if (!dimensions) return [];

    return dimensions.map(dimension => {
      const config = DIMENSION_CONFIG[dimension.name];
      return {
        ...dimension,
        ...config,
      };
    });
  }, [dimensions]);

  if (isLoading || dimensionsWithConfig.length === 0) {
    return <ArchetypeWheelSkeleton />;
  }

  if (dimensionsWithConfig.length === 0) {
    return (
      <div className="flex items-center justify-center py-12 text-muted-foreground">
        No dimensions available
      </div>
    );
  }

  const centerX = 450;
  const centerY = 450;
  const outerRadius = 350;
  const innerRadius = 200;
  const gap = 40;
  const sectorGap = 4;
  const sectorInnerRadius = innerRadius + gap;
  const sectorAngle = 360 / dimensionsWithConfig.length;
  const actualSectorAngle = sectorAngle - sectorGap;

  const currentDimension = dimensionsWithConfig.find(
    d => d.id === selectedDimension,
  );
  const CurrentIcon = currentDimension?.icon;

  const onDimensionChange = (id: number) => {
    setSelectedDimension(id);
  };

  return (
    <div className="flex items-center justify-center">
      <div className="relative w-full max-w-[800px] aspect-square">
        <div className="w-full h-full">
          <svg
            className="w-full h-full transform -rotate-90"
            viewBox="0 0 900 900"
            preserveAspectRatio="xMidYMid meet"
          >
            <defs>
              <mask id="innerGlowMask">
                <rect x="0" y="0" width="900" height="900" fill="white" />
                <circle
                  cx={centerX}
                  cy={centerY}
                  r={innerRadius}
                  fill="black"
                />
              </mask>

              <mask id="glowOnlyMask">
                <rect x="0" y="0" width="1500" height="1500" fill="white" />
                <circle
                  cx={centerX}
                  cy={centerY}
                  r={outerRadius + 30}
                  fill="black"
                />
              </mask>

              <filter
                id="outerComponentGlow"
                x="-50%"
                y="-50%"
                width="300%"
                height="200%"
              >
                <feGaussianBlur in="SourceAlpha" stdDeviation="30" />
                <feOffset dx="0" dy="0" result="offsetblur" />
                <feFlood floodColor="#4B9C88" floodOpacity="0.8" />
                <feComposite in2="offsetblur" operator="in" />
                <feMerge>
                  <feMergeNode />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <filter
                id="innerCircleShadow"
                x="-100%"
                y="-100%"
                width="300%"
                height="300%"
              >
                <feGaussianBlur in="SourceAlpha" stdDeviation="35" />
                <feOffset dx="0" dy="0" result="offsetblur" />
                <feFlood floodColor="#4B9C88" floodOpacity="0.8" />
                <feComposite in2="offsetblur" operator="in" />
                <feMerge>
                  <feMergeNode />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <filter id="glow">
                <feGaussianBlur stdDeviation="8" result="coloredBlur" />
                <feMerge>
                  <feMergeNode in="coloredBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>

              <radialGradient id="bgGradient" cx="50%" cy="50%">
                <stop offset="0%" stopColor="rgba(75, 156, 136, 0.1)" />
                <stop offset="100%" stopColor="rgba(75, 156, 136, 0.05)" />
              </radialGradient>

              <linearGradient
                id="sectorGradient"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.2)" />
                <stop offset="100%" stopColor="rgba(95, 95, 95, 0.2)" />
              </linearGradient>

              <linearGradient
                id="sectorGradientHover"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="rgba(255, 255, 255, 0.3)" />
                <stop offset="100%" stopColor="rgba(95, 95, 95, 0.3)" />
              </linearGradient>

              <linearGradient
                id="activeGradient"
                x1="0%"
                y1="0%"
                x2="0%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#4B9C88" />
                <stop offset="100%" stopColor="#181D19" />
              </linearGradient>
            </defs>

            <circle
              cx={centerX}
              cy={centerY}
              r={outerRadius + 30}
              fill={colors.primary}
              filter="url(#outerComponentGlow)"
              mask="url(#glowOnlyMask)"
            />

            {dimensionsWithConfig.map((dimension, index) => {
              const slotStartAngle = index * sectorAngle;
              const startAngle = slotStartAngle + sectorGap / 2;
              const endAngle = startAngle + actualSectorAngle;
              const isHovered = hoveredDimension === dimension.id;
              const isActive = dimension.id === selectedDimension;

              const sectorPath = createRoundedRingSectorPath(
                centerX,
                centerY,
                sectorInnerRadius,
                outerRadius,
                startAngle,
                endAngle,
              );

              const getFillGradient = () => {
                if (isActive) return 'url(#activeGradient)';
                if (isHovered) return 'url(#sectorGradientHover)';
                return 'url(#sectorGradient)';
              };

              const SectionIcon = dimension.icon;

              return (
                <g key={dimension.id}>
                  <path
                    d={sectorPath}
                    fill={getFillGradient()}
                    className="cursor-pointer transition-all duration-300"
                    filter={isActive ? 'url(#glow)' : ''}
                    onMouseEnter={() => setHoveredDimension(dimension.id)}
                    onMouseLeave={() => setHoveredDimension(null)}
                    onClick={() => onDimensionChange(dimension.id)}
                  />

                  <foreignObject
                    x={
                      centerX +
                      ((outerRadius + sectorInnerRadius) / 2) *
                        Math.cos(
                          ((startAngle + actualSectorAngle / 2) * Math.PI) /
                            180,
                        ) -
                      12
                    }
                    y={
                      centerY +
                      ((outerRadius + sectorInnerRadius) / 2) *
                        Math.sin(
                          ((startAngle + actualSectorAngle / 2) * Math.PI) /
                            180,
                        ) -
                      12
                    }
                    width="32"
                    height="32"
                    className="cursor-pointer"
                    onClick={() => onDimensionChange(dimension.id)}
                  >
                    <div
                      className="text-white h-8 w-8"
                      style={{ transform: 'rotate(90deg)' }}
                    >
                      <SectionIcon />
                    </div>
                  </foreignObject>
                </g>
              );
            })}

            <circle
              cx={centerX}
              cy={centerY}
              r={innerRadius}
              fill={colors.primary}
              filter="url(#innerCircleShadow)"
              mask="url(#innerGlowMask)"
              opacity="0.5"
            />
          </svg>
        </div>

        {currentDimension && CurrentIcon && (
          <div
            className="absolute flex items-center justify-center text-center"
            style={{
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
          >
            <div className="w-full">
              <div className="flex items-center justify-center mb-4 w-full">
                <div className="w-14 h-14" style={{ color: colors.primary }}>
                  <CurrentIcon />
                </div>
              </div>
              <div
                className="text-lg mb- text-center w-full"
                style={{ color: colors.textMuted }}
              >
                {currentDimension.description}
              </div>
              <div
                className="text-2xl font-semibold"
                style={{ color: colors.text }}
              >
                {currentDimension.name}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArchetypeWheel;
