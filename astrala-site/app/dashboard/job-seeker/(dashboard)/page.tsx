'use client';

import { useRouter } from 'next/navigation';

import ArchetypeList from '~/app/dashboard/job-seeker/(dashboard)/(components)/ArchetypeList.tsx';
import ArchetypeWheel from '~/app/dashboard/job-seeker/(dashboard)/(components)/ArchetypeWheel/ArchetypeWheel.tsx';

import { useGetUserArchetypeScores } from '~/api/features/archetypes/queries.ts';
import { useUnreadChatsCount } from '~/api/features/chats/queries.ts';
import { useVacancyMatchesCounts } from '~/api/features/matches/job-seeker/queries.ts';
import { Button } from '~/components/ui/button.tsx';
import { Card } from '~/components/ui/card.tsx';
import { Skeleton } from '~/components/ui/skeleton.tsx';
import { useAuth } from '~/providers/AuthProvider.tsx';

export default function JobSeekerDashboard() {
  const router = useRouter();
  const { jobSeekerProfile } = useAuth();
  const jobSeekerId = jobSeekerProfile?.id as number | undefined;

  const { data: userScores, isLoading } =
    useGetUserArchetypeScores(jobSeekerId);
  const { data: matchesCounts, isLoading: isLoadingCounts } =
    useVacancyMatchesCounts(jobSeekerId);
  const { data: unreadChats, isLoading: isLoadingChats } = useUnreadChatsCount(
    jobSeekerId,
    'job_seeker',
  );

  return (
    <div className="min-h-screen bg-[#101010] p-8 text-foreground font-medium w-full">
      <div className="flex items-start justify-between mb-4">
        <div>
          <p className="text-primary">Hello, {jobSeekerProfile?.full_name}!</p>
          <p className="text-3xl">Welcome back</p>
        </div>
        <Button
          variant="default"
          onClick={() =>
            router.push('/dashboard/job-seeker/edit-symbolic-profile')
          }
        >
          Edit Symbolic Behavioural Profile
        </Button>
      </div>

      <div className="w-full flex gap-8">
        <div className="w-[70%]">
          <div className="flex items-center gap-4 mb-6 w-full flex-1">
            <Card
              className="w-1/2 p-6 cursor-pointer"
              onClick={() => router.push('/dashboard/job-seeker/suggestions')}
            >
              <p className="text-muted-foreground ">Awaiting Review</p>
              <div className="flex gap-2 items-center">
                <p className="text-3xl">Your Shortlist</p>
                {isLoadingCounts ? (
                  <Skeleton className="h-6 w-8 rounded-full" />
                ) : (
                  <div className="text-sm px-2 py-1 rounded-full bg-primary">
                    {matchesCounts?.suggestions || 0}
                  </div>
                )}
              </div>
            </Card>
            <Card
              className="w-1/2 p-6 cursor-pointer"
              onClick={() => router.push('/dashboard/job-seeker/chats')}
            >
              <p className="text-muted-foreground">Messages</p>
              <div className="flex gap-2 items-center">
                <p className="text-3xl">Unread</p>
                {isLoadingChats ? (
                  <Skeleton className="h-6 w-8 rounded-full" />
                ) : (
                  <div className="text-sm px-2 py-1 rounded-full bg-primary">
                    {unreadChats?.unreadChats || 0}
                  </div>
                )}
              </div>
            </Card>
          </div>
          <Card>
            <ArchetypeWheel />
          </Card>
        </div>
        <div className="w-[30%]">
          <ArchetypeList
            archetypes={userScores?.archetypes}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
