'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';

import { useJobSeekerDataSync } from '~/app/dashboard/job-seeker/settings/(hooks)/useJobSeekerDataSync.ts';
import { CertificationForm } from '~/app/onboarding/job-seeker/(components)/(forms)/CertificationForm';
import { EducationForm } from '~/app/onboarding/job-seeker/(components)/(forms)/EducationForm';
import { PersonalInfoForm } from '~/app/onboarding/job-seeker/(components)/(forms)/PersonalInfoForm';
import { WorkExperienceForm } from '~/app/onboarding/job-seeker/(components)/(forms)/WorkExperienceForm';
import {
  getDefaultValues,
  MainInformationFormSchema,
  mainInformationFormSchema,
} from '~/app/onboarding/job-seeker/(schemas)/mainInformation.schema';
import { SectionContainer } from '~/app/onboarding/job-seeker/SectionContainer';

import { LanguageForm } from '~/shared/components/forms/LanguageForm';
import { SalaryForm } from '~/shared/components/forms/SalaryForm';
import { SkillsForm } from '~/shared/components/forms/SkillsForm';
import { Database } from '~/shared/db/generated/types';

import {
  createJobSeekerCertificationById,
  createJobSeekerCertificationByName,
} from '~/api/entities/certification/actions';
import {
  useAddEducation,
  useAddLanguage,
  useAddWorkExperience,
  useDeleteEducation,
  useDeleteLanguage,
  useDeleteWorkExperience,
  useUpdateJobSeekerProfile,
  useUploadCertification,
  useUploadDrivingLicense,
  useUploadPassport,
} from '~/api/entities/job-seeker/queries';
import { processSkills } from '~/api/entities/skill/actions';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { Form } from '~/components/ui/form';
import { useToast } from '~/hooks/use-toast.ts';

type JobSeekerProfile = Database['public']['Tables']['job_seekers']['Row'];

export default function EditJobSeekerForm({
  jobSeekerProfile,
  onComplete,
  onCancel,
  disabled = false,
  onSectionsChange,
}: {
  jobSeekerProfile: JobSeekerProfile;
  onComplete: () => void;
  onCancel: () => void;
  disabled?: boolean;
  onSectionsChange?: (sections: string[]) => void;
}) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFormDirty, setIsFormDirty] = useState(false);
  const [initialData, setInitialData] =
    useState<MainInformationFormSchema | null>(null);

  const getNextEditDate = () => {
    if (!jobSeekerProfile?.information_updated_at) return null;

    const lastUpdate = new Date(jobSeekerProfile.information_updated_at);
    const nextDate = new Date(lastUpdate);
    nextDate.setHours(nextDate.getHours() + 24);

    return nextDate;
  };

  const nextInfoEditDate = getNextEditDate();
  const canEditInfo = !disabled;

  const sectionRefs = {
    personalInfo: React.useRef<HTMLDivElement>(null),
    education: React.useRef<HTMLDivElement>(null),
    workExperience: React.useRef<HTMLDivElement>(null),
    certifications: React.useRef<HTMLDivElement>(null),
    language: React.useRef<HTMLDivElement>(null),
    skills: React.useRef<HTMLDivElement>(null),
    salary: React.useRef<HTMLDivElement>(null),
    drivingLicense: React.useRef<HTMLDivElement>(null),
    passport: React.useRef<HTMLDivElement>(null),
  };

  const form = useForm<MainInformationFormSchema>({
    resolver: zodResolver(mainInformationFormSchema),
    defaultValues: getDefaultValues(),
  });

  const { isDataLoading, syncError, profileData, formDataInitialized } =
    useJobSeekerDataSync(jobSeekerProfile, form.setValue, form.reset);

  useEffect(() => {
    if (syncError) {
      setError(syncError);
    }
  }, [syncError]);

  useEffect(() => {
    if (formDataInitialized && !initialData) {
      setInitialData(form.getValues());
    }
  }, [formDataInitialized, form, initialData]);

  const updateProfileMutation = useUpdateJobSeekerProfile();
  const addEducationMutation = useAddEducation();
  const addWorkExperienceMutation = useAddWorkExperience();
  const addLanguageMutation = useAddLanguage();
  const uploadDrivingLicenseMutation = useUploadDrivingLicense();
  const uploadPassportMutation = useUploadPassport();
  const uploadCertificationMutation = useUploadCertification();
  const deleteEducationMutation = useDeleteEducation();
  const deleteWorkExperienceMutation = useDeleteWorkExperience();
  const deleteLanguageMutation = useDeleteLanguage();

  const updateCompletedSections = useCallback(() => {
    if (!formDataInitialized) return;

    const values = form.getValues();
    const completed = [];

    const personalInfo = values.personalInfo || {};
    if (
      personalInfo.fullName &&
      personalInfo.jobTitle &&
      personalInfo.phone &&
      personalInfo.country &&
      personalInfo.city
    ) {
      completed.push('personalInfo');
    }

    if (
      values.education?.length > 0 &&
      values.education[0]?.type &&
      values.education[0]?.institution &&
      values.education[0]?.discipline
    ) {
      completed.push('education');
    }

    if (
      values.workExperience?.length > 0 &&
      values.workExperience[0]?.companyName &&
      values.workExperience[0]?.jobTitle
    ) {
      completed.push('workExperience');
    }

    if (
      values.languages?.length > 0 &&
      values.languages[0]?.language &&
      values.languages[0]?.level
    ) {
      completed.push('language');
    }

    if (values.skills?.length > 0) {
      completed.push('skills');
    }

    if (values.certifications?.length > 0) {
      completed.push('certifications');
    }

    if (values.salary_min && values.salary_max) {
      completed.push('salary');
    }

    if (values.drivingLicense?.file || values.drivingLicense?.hasDocument) {
      completed.push('drivingLicense');
    }

    if (values.passport?.file || values.passport?.hasDocument) {
      completed.push('passport');
    }

    if (onSectionsChange) {
      onSectionsChange(completed);
    }
  }, [formDataInitialized, form, onSectionsChange]);

  const deleteExistingEntries = async () => {
    if (!profileData) return;

    for (const education of profileData.educations) {
      if (education.id) {
        await deleteEducationMutation.mutateAsync({
          educationId: education.id,
        });
      }
    }

    for (const experience of profileData.workExperiences) {
      if (experience.id) {
        await deleteWorkExperienceMutation.mutateAsync({
          experienceId: experience.id,
        });
      }
    }

    for (const language of profileData.languages) {
      if (language.id) {
        await deleteLanguageMutation.mutateAsync({ languageId: language.id });
      }
    }
  };

  const checkFormChanges = useCallback(() => {
    if (!initialData || !formDataInitialized) return false;

    const currentValues = form.getValues();

    const initialSkills = initialData.skills || [];
    const currentSkills = currentValues.skills || [];

    if (initialSkills.length !== currentSkills.length) {
      return true;
    }

    const skillsChanged = currentSkills.some((skill, index) => {
      return initialSkills[index] !== skill;
    });

    if (skillsChanged) {
      return true;
    }

    const initialCerts = initialData.certifications || [];
    const currentCerts = currentValues.certifications || [];

    if (initialCerts.length !== currentCerts.length) {
      return true;
    }

    const certsChanged = currentCerts.some(cert => cert.file !== null);

    if (certsChanged) {
      return true;
    }

    const certNamesChanged = currentCerts.some((cert, index) => {
      return initialCerts[index]?.name !== cert.name;
    });

    if (certNamesChanged) {
      return true;
    }

    return form.formState.isDirty;
  }, [form, initialData, formDataInitialized]);

  useEffect(() => {
    const subscription = form.watch(() => {
      if (formDataInitialized) {
        const isDirty = checkFormChanges();
        setIsFormDirty(isDirty);
        updateCompletedSections();
      }
    });

    if (formDataInitialized && !isDataLoading) {
      updateCompletedSections();
    }

    return () => subscription.unsubscribe();
  }, [
    form,
    form.watch,
    isDataLoading,
    formDataInitialized,
    updateCompletedSections,
    checkFormChanges,
  ]);

  const handleSubmit = async (data: MainInformationFormSchema) => {
    if (!jobSeekerProfile?.id || !jobSeekerProfile?.user_id) {
      setError('User profile not found');
      return;
    }

    if (!canEditInfo) {
      toast({
        title: 'Editing Restricted',
        description: 'You can only edit your information once every 24 hours.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await deleteExistingEntries();

      const profileUpdateParams = {
        jobSeekerId: jobSeekerProfile.id,
        fullName: data.personalInfo?.fullName || '',
        jobTitle: data.personalInfo?.jobTitle || '',
        phone: data.personalInfo?.phone || '',
        country: data.personalInfo?.country || '',
        city: data.personalInfo?.city || '',
        salary_min: data.salary_min || 0,
        salary_max: data.salary_max || 0,
      };

      await updateProfileMutation.mutateAsync(profileUpdateParams);

      if (data.education && data.education.length > 0) {
        for (const education of data.education) {
          await addEducationMutation.mutateAsync({
            jobSeekerId: jobSeekerProfile.id,
            type: education.type,
            institution: education.institution,
            discipline: education.discipline,
            startYear: education.startYear,
            endYear: education.endYear,
          });
        }
      }

      if (data.workExperience && data.workExperience.length > 0) {
        for (const experience of data.workExperience) {
          await addWorkExperienceMutation.mutateAsync({
            jobSeekerId: jobSeekerProfile.id,
            companyName: experience.companyName,
            jobTitle: experience.jobTitle,
            startMonth: experience.startMonth,
            startYear: experience.startYear,
            endMonth: experience.endMonth,
            endYear: experience.endYear,
            comment: experience.comment || '',
          });
        }
      }

      if (data.languages && data.languages.length > 0) {
        for (const lang of data.languages) {
          await addLanguageMutation.mutateAsync({
            jobSeekerId: jobSeekerProfile.id,
            language: lang.language,
            level: lang.level,
          });
        }
      }

      if (data.skills && data.skills.length > 0) {
        await processSkills(
          { id: Number(jobSeekerProfile.id), type: 'jobSeeker' },
          data.skills,
        );
      }

      if (data.certifications && data.certifications.length > 0) {
        for (const certification of data.certifications) {
          let result;

          if (certification.id > 0) {
            result = await createJobSeekerCertificationById(
              Number(jobSeekerProfile.id),
              certification.id,
            );
          } else {
            result = await createJobSeekerCertificationByName(
              Number(jobSeekerProfile.id),
              certification.name,
            );
          }

          if (
            result.success &&
            result.jobSeekerCertificationId &&
            certification.file
          ) {
            await uploadCertificationMutation.mutateAsync({
              file: certification.file,
              jobSeekerCertificationId: result.jobSeekerCertificationId,
              userId: jobSeekerProfile.user_id,
            });
          }
        }
      }

      if (data.drivingLicense?.file) {
        await uploadDrivingLicenseMutation.mutateAsync({
          file: data.drivingLicense.file,
          userId: jobSeekerProfile.user_id,
          jobSeekerId: jobSeekerProfile.id,
        });
      }

      if (data.passport?.file) {
        await uploadPassportMutation.mutateAsync({
          file: data.passport.file,
          userId: jobSeekerProfile.user_id,
          jobSeekerId: jobSeekerProfile.id,
        });
      }

      toast({
        title: 'Profile Updated',
        description: 'Your information has been successfully updated',
      });

      setInitialData(data);
      setIsFormDirty(false);

      onComplete();
    } catch (error: unknown) {
      const errorMessage = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setError(errorMessage);
      toast({
        title: 'Update Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isDataLoading) {
    return (
      <div className="rounded-lg w-full flex flex-col justify-center items-center p-10">
        <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
        <p className="text-foreground">Loading your profile data...</p>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col justify-center items-center">
      <Alert className="mb-6 w-full bg-background text-foreground">
        <AlertTitle className={`${disabled ? 'text-destructive' : ''}`}>
          {canEditInfo
            ? 'Information Edit Available'
            : 'Information Edit Restricted'}
        </AlertTitle>
        <AlertDescription>
          {canEditInfo
            ? 'You can edit your personal information once every 24 hours.'
            : `Next edit available on ${nextInfoEditDate ? format(nextInfoEditDate, 'PPP') : ''} at ${nextInfoEditDate ? format(nextInfoEditDate, 'p') : ''}.`}
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className={`${disabled ? 'pointer-events-none' : ''}`}
        >
          <div
            className="space-y-8"
            style={{ maxWidth: '100%', boxSizing: 'border-box' }}
          >
            <SectionContainer
              id="personalInfo"
              className="scroll-m-24"
              sectionRef={sectionRefs.personalInfo}
            >
              <PersonalInfoForm control={form.control} />
            </SectionContainer>

            <SectionContainer
              id="education"
              sectionRef={sectionRefs.education}
              className="scroll-m-24"
            >
              <EducationForm
                control={form.control}
                setValue={form.setValue}
                watch={form.watch}
              />
            </SectionContainer>

            <SectionContainer
              className="scroll-m-24"
              id="workExperience"
              sectionRef={sectionRefs.workExperience}
            >
              <WorkExperienceForm control={form.control} />
            </SectionContainer>

            <SectionContainer
              className="scroll-m-24"
              id="certifications"
              sectionRef={sectionRefs.certifications}
            >
              <CertificationForm
                control={form.control}
                setValue={form.setValue}
                watch={form.watch}
              />
            </SectionContainer>

            <SectionContainer
              id="language"
              sectionRef={sectionRefs.language}
              className="scroll-m-24"
            >
              <LanguageForm control={form.control} />
            </SectionContainer>

            <SectionContainer
              id="skills"
              sectionRef={sectionRefs.skills}
              className="scroll-m-24"
            >
              <SkillsForm
                control={form.control}
                setValue={form.setValue}
                watch={form.watch}
              />
            </SectionContainer>

            <SectionContainer
              id="salary"
              sectionRef={sectionRefs.salary}
              className="scroll-m-24"
            >
              <SalaryForm control={form.control} />
            </SectionContainer>
          </div>

          {error && (
            <div className="p-3 bg-destructive/20 text-destructive rounded-md my-6">
              {error}
            </div>
          )}

          <div className="mt-6 border-t pt-6 flex justify-between">
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={isLoading || !canEditInfo}
            >
              Cancel
            </Button>

            <Button
              variant="default"
              type="submit"
              disabled={isLoading || !isFormDirty || !canEditInfo}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
