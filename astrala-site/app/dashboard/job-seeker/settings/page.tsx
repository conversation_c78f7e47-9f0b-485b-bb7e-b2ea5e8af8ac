'use client';
import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

import EditJobSeekerForm from '~/app/dashboard/job-seeker/(components)/EditJobSeekerForm.tsx';
import { DocumentReupload } from '~/app/dashboard/job-seeker/settings/(components)/DocumentReupload.tsx';
import { FormSectionTabs } from '~/app/onboarding/job-seeker/(components)/FormSectionTabs';

import {
  useJobSeekerCanEditInfo,
  useJobSeekerCanEditWorkStyle,
  useUploadResume,
} from '~/api/entities/job-seeker/queries';
import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip';
import { useToast } from '~/hooks/use-toast';
import { useAuth } from '~/providers/AuthProvider';

export default function SettingsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { jobSeekerProfile, isLoading } = useAuth();

  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [isUploadingResume, setIsUploadingResume] = useState(false);

  const [activeSection, setActiveSection] = useState('personalInfo');
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const { data: canEditInfoData, isLoading: isLoadingEditInfo } =
    useJobSeekerCanEditInfo(
      jobSeekerProfile?.id ? Number(jobSeekerProfile.id) : undefined,
    );

  const { data: canEditWorkStyleData, isLoading: isLoadingEditWorkStyle } =
    useJobSeekerCanEditWorkStyle(
      jobSeekerProfile?.id ? Number(jobSeekerProfile.id) : undefined,
    );

  const uploadResumeMutation = useUploadResume();

  const sections = [
    { id: 'personalInfo', label: 'Personal Info' },
    { id: 'education', label: 'Education' },
    { id: 'workExperience', label: 'Work Experience' },
    { id: 'certifications', label: 'Certification' },
    { id: 'language', label: 'Language' },
    { id: 'skills', label: 'Skill' },
    { id: 'salary', label: 'Salary Range' },
    { id: 'drivingLicense', label: 'Driving License' },
  ];

  const getNextEditDate = (updatedAt: string | null) => {
    if (!updatedAt) return null;

    const lastUpdate = new Date(updatedAt);
    const nextDate = new Date(lastUpdate);
    nextDate.setHours(nextDate.getHours() + 24);

    return nextDate;
  };

  const nextWorkStyleEditDate = jobSeekerProfile?.work_style_updated_at
    ? getNextEditDate(jobSeekerProfile.work_style_updated_at)
    : null;

  const canEditInfo = canEditInfoData?.canEdit ?? false;
  const canEditWorkStyle = canEditWorkStyleData?.canEdit ?? false;

  const handleResumeFileChange = (file: File | null) => {
    setResumeFile(file);
  };

  useEffect(() => {
    if (resumeFile && !isUploadingResume) {
      handleUploadResume();
    }
  }, [resumeFile]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleUploadResume = async () => {
    if (!jobSeekerProfile?.id || !jobSeekerProfile?.user_id || !resumeFile) {
      return;
    }

    setIsUploadingResume(true);
    try {
      await uploadResumeMutation.mutateAsync({
        file: resumeFile,
        userId: jobSeekerProfile.user_id,
        jobSeekerId: jobSeekerProfile.id,
      });

      toast({
        title: 'Resume Uploaded',
        description: 'Your resume has been successfully updated',
      });

      setResumeFile(null);
    } catch (error) {
      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsUploadingResume(false);
    }
  };

  const handleEditWorkStyle = () => {
    if (canEditWorkStyle) {
      router.push('/dashboard/job-seeker/edit-work-style');
    } else {
      toast({
        title: 'Editing Restricted',
        description:
          'You can only update your Behavioural Profile preferences once every 24 hours.',
        variant: 'destructive',
      });
    }
  };

  const handleComplete = () => {
    toast({
      title: 'Profile Updated',
      description: 'Your information has been successfully updated',
    });
    router.push('/dashboard/job-seeker/settings');
  };

  const handleTabClick = (sectionId: string) => {
    setActiveSection(sectionId);

    setTimeout(() => {
      // eslint-disable-next-line unicorn/prefer-query-selector
      const element = document.getElementById(`${sectionId}-section`);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        });
      }
    }, 100);
  };

  if (isLoading || isLoadingEditInfo || isLoadingEditWorkStyle) {
    return (
      <div className="flex justify-center items-center h-full p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
        <p>Loading profile...</p>
      </div>
    );
  }

  if (!jobSeekerProfile) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>No profile found. Please complete onboarding first.</p>
      </div>
    );
  }

  if (!jobSeekerProfile.country) {
    router.push('/onboarding/job-seeker');
    return null;
  }

  const hasResume = Boolean(jobSeekerProfile.resume_path);

  return (
    <div className="w-full">
      <FormSectionTabs
        sections={sections}
        activeSection={activeSection}
        completedSections={completedSections}
        onTabClick={handleTabClick}
      />

      <div className="flex gap-6 w-full px-8 py-4">
        <div className="w-[60%]">
          <EditJobSeekerForm
            onComplete={handleComplete}
            onCancel={() => router.push('/dashboard/job-seeker/settings')}
            disabled={!canEditInfo}
            jobSeekerProfile={jobSeekerProfile}
            onSectionsChange={setCompletedSections}
          />
        </div>

        <div className="w-[40%] flex flex-col gap-6">
          <Card>
            <CardHeader>
              <CardTitle>CV file</CardTitle>
              <CardDescription>The file you uploaded</CardDescription>
            </CardHeader>
            <CardContent>
              <DocumentReupload
                documentType="resume"
                hasDocument={hasResume}
                onFileChange={handleResumeFileChange}
                isUploading={isUploadingResume}
                acceptedFileTypes=".pdf,.doc,.docx"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Change the Behavioural Profile</CardTitle>
              <CardDescription>
                You can change your test answers once every 24 hours.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <Button
                        variant="default"
                        onClick={handleEditWorkStyle}
                        disabled={!canEditWorkStyle}
                        className="w-full"
                      >
                        Change test
                      </Button>
                    </div>
                  </TooltipTrigger>
                  {!canEditWorkStyle && nextWorkStyleEditDate && (
                    <TooltipContent>
                      <p>
                        Next edit available on{' '}
                        {format(nextWorkStyleEditDate, 'PPP')} at{' '}
                        {format(nextWorkStyleEditDate, 'p')}.
                      </p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>

              {nextWorkStyleEditDate && !canEditWorkStyle && (
                <div className="text-sm text-center mt-2 text-muted-foreground">
                  Next edit available on {format(nextWorkStyleEditDate, 'PPP')}{' '}
                  at {format(nextWorkStyleEditDate, 'p')}.
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
