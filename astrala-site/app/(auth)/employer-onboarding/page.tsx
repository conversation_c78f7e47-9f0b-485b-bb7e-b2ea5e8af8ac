'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import AuthLayout from '~/app/(auth)/AuthLayout.tsx';

import { useCreateEmployerCompany } from '~/api/features/employer-onboarding/queries.ts';
import OnboardingImage from '~/assets/images/auth/company-info.jpg';
import { Button } from '~/components/ui/button.tsx';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form';
import { Input } from '~/components/ui/input.tsx';
import { useAuth } from '~/providers/AuthProvider.tsx';

const employerOnboardingSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  fullName: z.string().min(1, 'Full name is required'),
  role: z.string().min(1, 'Role is required'),
});

type EmployerOnboardingFormValues = z.infer<typeof employerOnboardingSchema>;

export default function EmployerOnboarding() {
  const router = useRouter();
  const { session, isLoading, employerProfile } = useAuth();

  const form = useForm<EmployerOnboardingFormValues>({
    resolver: zodResolver(employerOnboardingSchema),
    defaultValues: {
      companyName: '',
      fullName: '',
      role: '',
    },
  });

  const {
    mutate: completeOnboarding,
    isPending,
    error,
    isError,
  } = useCreateEmployerCompany();

  useEffect(() => {
    if (
      employerProfile?.company_id &&
      employerProfile.full_name &&
      employerProfile.role
    ) {
      router.push('/dashboard/employer/suggestions');
    }
  }, [employerProfile, router]);

  const onSubmit = (data: EmployerOnboardingFormValues) => {
    if (!session?.user?.id) {
      return;
    }

    completeOnboarding({
      userId: session.user.id,
      companyName: data.companyName,
      fullName: data.fullName,
      role: data.role,
    });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <AuthLayout sideImage={OnboardingImage}>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-center">
          Fill In The Information
        </h2>
        <div className="mt-6">
          {isError && (
            <div className="text-destructive text-sm mb-4">
              {error?.message || 'Error while creating a profile'}
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-foreground text-sm">
                      Company Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Company name"
                        className="w-full h-12 px-3 rounded-md border"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-destructive text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-foreground text-sm">
                      Full name
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Full Name"
                        className="w-full h-12 px-3 rounded-md border"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-destructive text-sm" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-foreground text-sm">
                      Role
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Role Title"
                        className="w-full h-12 px-3 rounded-md border"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-destructive text-sm" />
                  </FormItem>
                )}
              />

              <Button
                variant="default"
                type="submit"
                disabled={isPending}
                className="w-full"
                size="lg"
              >
                {isPending ? 'Loading...' : 'Sign Up'}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </AuthLayout>
  );
}
