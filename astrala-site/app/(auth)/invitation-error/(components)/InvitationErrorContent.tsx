'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

import { Button } from '~/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '~/components/ui/card';

type ErrorType =
  | 'invalid'
  | 'expired'
  | 'already_used'
  | 'already_logged_in'
  | 'unknown';

export default function InvitationErrorContent() {
  const searchParams = useSearchParams();
  const [errorType, setErrorType] = useState<ErrorType>('unknown');

  useEffect(() => {
    const type = searchParams.get('type');
    if (
      type &&
      ['invalid', 'expired', 'already_used', 'already_logged_in'].includes(type)
    ) {
      setErrorType(type as ErrorType);
    } else {
      setErrorType('invalid');
    }
  }, [searchParams]);

  const getErrorContent = () => {
    switch (errorType) {
      case 'already_used':
        return {
          title: 'Invitation Already Used',
          description: 'This invitation link has already been used.',
          content: (
            <>
              <p className="text-center text-muted-foreground">
                This invitation link has already been used to register an
                account. Each invitation can only be used once.
              </p>
              <p className="mt-4 text-center text-muted-foreground">
                If you need to join this company, please ask for a new
                invitation.
              </p>
            </>
          ),
        };

      case 'already_logged_in':
        return {
          title: 'Already Registered',
          description: 'You are already logged in.',
          content: (
            <>
              <p className="text-center text-muted-foreground">
                You are currently logged in to an account. Invitation links can
                only be used to create new accounts.
              </p>
              <p className="mt-4 text-center text-muted-foreground">
                Please log out first if you want to create a new account with
                this invitation.
              </p>
            </>
          ),
        };

      case 'expired':
        return {
          title: 'Invitation Expired',
          description: 'This invitation link has expired.',
          content: (
            <p className="text-center text-muted-foreground">
              The invitation link you tried to use has expired. Please ask for a
              new invitation.
            </p>
          ),
        };

      case 'invalid':
      default:
        return {
          title: 'Invalid Invitation',
          description: 'This invitation link is invalid.',
          content: (
            <>
              <p className="text-center text-muted-foreground">
                The invitation link you tried to access is not valid. This may
                be because:
              </p>
              <ul className="mt-4 list-disc pl-6 text-muted-foreground">
                <li>The invitation link was entered incorrectly</li>
                <li>The invitation has been revoked</li>
                <li>The invitation never existed</li>
              </ul>
            </>
          ),
        };
    }
  };

  const errorContent = getErrorContent();

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-destructive">
            {errorContent.title}
          </CardTitle>
          <CardDescription className="text-center">
            {errorContent.description}
          </CardDescription>
        </CardHeader>
        <CardContent>{errorContent.content}</CardContent>
        <CardFooter className="flex justify-center gap-4">
          <Link href="/">
            <Button>Return to Homepage</Button>
          </Link>
          {errorType === 'already_logged_in' && (
            <Link href="/signout">
              <Button variant="outline">Sign Out</Button>
            </Link>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
