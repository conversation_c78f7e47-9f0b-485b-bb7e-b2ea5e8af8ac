'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function PrintArchetypeContent() {
  const searchParams = useSearchParams();

  const name = searchParams.get('name') || 'Unnamed Candidate';
  const jobTitle = searchParams.get('jobTitle') || '';
  const location = searchParams.get('location') || '';
  const archetypeName = searchParams.get('archetypeName') || '';
  const percentage = searchParams.get('percentage') || '0';
  const description = searchParams.get('description') || '';
  const metaphor = searchParams.get('metaphor') || '';

  useEffect(() => {
    if (archetypeName) {
      setTimeout(() => {
        window.print();
      }, 300);
    }
  }, [archetypeName]);

  if (!archetypeName) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>No data available</p>
      </div>
    );
  }

  return (
    <>
      {/* eslint-disable-next-line react/no-unknown-property */}
      <style jsx>{`
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
        @media screen {
          .print-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
          }
        }
      `}</style>

      <div className="print-content bg-white text-black p-8">
        <h1 className="text-3xl font-bold mb-8">Interview Fit Scorecard</h1>

        <div className="flex items-center mb-8 pb-6 border-b-2 border-gray-300">
          <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
            <span className="text-2xl font-bold">{name.charAt(0)}</span>
          </div>
          <div>
            <h2 className="text-2xl font-semibold mb-1">{name}</h2>
            <div className="flex flex-wrap items-center text-base text-gray-600">
              {jobTitle && <span className="mr-4">{jobTitle}</span>}
              {location && <span>{location}</span>}
            </div>
          </div>
        </div>

        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <h3 className="text-4xl font-bold">{archetypeName}</h3>
            <div className="px-3 py-1 bg-gray-200 rounded text-sm font-semibold">
              {percentage}%
            </div>
          </div>
        </div>

        {description && (
          <div className="mb-6">
            <h4 className="text-xl font-semibold mb-2">Description</h4>
            <p className="text-gray-700 leading-relaxed">{description}</p>
          </div>
        )}

        {metaphor && (
          <div>
            <h4 className="text-xl font-semibold mb-2">Metaphor</h4>
            <p className="text-gray-700 leading-relaxed">{metaphor}</p>
          </div>
        )}
      </div>
    </>
  );
}
