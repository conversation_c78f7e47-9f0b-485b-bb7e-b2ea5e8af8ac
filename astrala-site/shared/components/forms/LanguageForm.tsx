import React, { useMemo, useState } from 'react';
import {
  Control,
  FieldValues,
  Path,
  useController,
  useFieldArray,
} from 'react-hook-form';
import { Check, ChevronsUpDown, Plus, Trash2 } from 'lucide-react';

import { Button } from '~/components/ui/button.tsx';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '~/components/ui/command';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form.tsx';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select.tsx';
import { languageLevels, languages } from '~/constants/languages.ts';

type FormWithLanguages = {
  languages: Array<{
    language: string;
    level: string;
  }>;
};

type LanguageFormProps<T extends FieldValues & FormWithLanguages> = {
  control: Control<T>;
};

const LanguageSelect = <T extends FieldValues>({
  control,
  name,
}: {
  control: Control<T>;
  name: Path<T>;
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const { field, fieldState } = useController({
    name,
    control,
  });

  const { error } = fieldState;

  const filteredLanguages = useMemo(() => {
    if (!searchQuery) return languages;

    const lowerCaseQuery = searchQuery.toLowerCase();
    return languages.filter(
      language =>
        language.label.toLowerCase().includes(lowerCaseQuery) ||
        language.value.toLowerCase().includes(lowerCaseQuery),
    );
  }, [searchQuery]);

  return (
    <FormItem className="flex flex-col space-y-1 w-full">
      <FormLabel className="text-white">Language and level</FormLabel>
      <FormControl>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between h-9 bg-background text-white border hover:bg-hover hover:text-white"
            >
              {field.value
                ? languages.find(language => language.value === field.value)
                    ?.label || 'Select language...'
                : 'Select language...'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 text-white" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="p-0 bg-background border"
            style={{ width: 'var(--radix-popover-trigger-width)' }}
          >
            <Command shouldFilter={false} className="bg-background">
              <CommandInput
                placeholder="Search language..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="bg-background text-white placeholder:text-placeholder"
              />
              <CommandList className="bg-background text-white">
                {filteredLanguages.length === 0 && (
                  <CommandEmpty className="text-white p-2 pt-1">
                    No language found.
                  </CommandEmpty>
                )}
                <CommandGroup className="text-white">
                  {filteredLanguages.map(language => (
                    <CommandItem
                      key={language.value}
                      className="cursor-pointer text-white hover:bg-hover hover:text-white focus:bg-hover focus:text-white data-[selected=true]:bg-hover data-[selected=true]:text-white"
                      value={language.value}
                      onSelect={currentValue => {
                        field.onChange(
                          currentValue === field.value ? '' : currentValue,
                        );
                        setOpen(false);
                      }}
                    >
                      <Check
                        className={`mr-2 h-4 w-4 text-white ${
                          field.value === language.value
                            ? 'opacity-100'
                            : 'opacity-0'
                        }`}
                      />
                      {language.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </FormControl>
      <FormMessage className="min-h-5 text-destructive">
        {error?.message}
      </FormMessage>
    </FormItem>
  );
};

export const LanguageForm = <T extends FieldValues & FormWithLanguages>({
  control,
}: LanguageFormProps<T>): React.ReactElement => {
  const { fields, append, remove } = useFieldArray({
    control,
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-expect-error
    name: 'languages' as Path<T>,
  });

  return (
    <div className="space-y-4 p-2 text-white">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-white">Language and level</h2>
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="ml-auto bg-background text-white border hover:bg-hover hover:text-white"
          onClick={() =>
            append({
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              //@ts-ignore
              language: '',
              level: '',
            })
          }
        >
          <Plus className="h-4 w-4 mr-2 text-white" />
          Add Language
        </Button>
      </div>

      {fields.map((field, index) => (
        <div
          key={field.id}
          className="grid grid-cols-12 gap-4 items-end w-full"
        >
          <div className="col-span-6">
            <FormField
              control={control}
              name={`languages.${index}.language` as Path<T>}
              render={() => (
                <LanguageSelect
                  control={control}
                  name={`languages.${index}.language` as Path<T>}
                />
              )}
            />
          </div>

          <div className="col-span-5">
            <FormField
              control={control}
              name={`languages.${index}.level` as Path<T>}
              render={({ field, fieldState }) => (
                <FormItem className="flex flex-col space-y-1 w-full">
                  <FormLabel className="text-white">Level</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full bg-background text-white border">
                        <SelectValue placeholder="C1" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-background text-white border">
                      {languageLevels.map(level => (
                        <SelectItem
                          key={level.value}
                          value={level.value}
                          className="text-white hover:bg-hover focus:bg-hover"
                        >
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage className="min-h-5 text-destructive">
                    {fieldState.error?.message}
                  </FormMessage>
                </FormItem>
              )}
            />
          </div>

          {index > 0 ? (
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => remove(index)}
              aria-label="Remove language"
              className="bg-background border hover:bg-hover"
            >
              <Trash2 className="h-4 w-4 text-destructive" />
            </Button>
          ) : (
            <div className="w-9" />
          )}
        </div>
      ))}
    </div>
  );
};
