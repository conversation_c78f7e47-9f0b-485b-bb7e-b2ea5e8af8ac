import React, { useRef, useState } from 'react';
import { Trash2, Upload } from 'lucide-react';
import Image from 'next/image';

import FileUploadIllustration from '~/assets/images/file-input.png';
import { Button } from '~/components/ui/button.tsx';

export const FILE_TYPES = {
  document: [
    { extension: '.pdf', mimeType: 'application/pdf' },
    { extension: '.doc', mimeType: 'application/msword' },
    {
      extension: '.docx',
      mimeType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ],
  image: [
    { extension: '.jpg', mimeType: 'image/jpeg' },
    { extension: '.jpeg', mimeType: 'image/jpeg' },
    { extension: '.png', mimeType: 'image/png' },
    { extension: '.gif', mimeType: 'image/gif' },
    { extension: '.webp', mimeType: 'image/webp' },
  ],
  spreadsheet: [
    { extension: '.xls', mimeType: 'application/vnd.ms-excel' },
    {
      extension: '.xlsx',
      mimeType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    { extension: '.csv', mimeType: 'text/csv' },
  ],
};

export type FileTypeCategory = keyof typeof FILE_TYPES;

export type FileUploadProps = {
  onChange: (files: File[]) => void;
  value?: File[];
  maxSizeMB?: number;
  fileTypeCategory?: FileTypeCategory | FileTypeCategory[];
  multiple?: boolean;
  label?: string;
  inputId?: string;
  disabled?: boolean;
  withoutImage?: boolean;
};

export const FileUpload = ({
  onChange,
  value,
  maxSizeMB = 2,
  fileTypeCategory = 'document',
  multiple = false,
  inputId = 'file-upload',
  disabled = false,
  withoutImage = false,
}: FileUploadProps) => {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>(value || []);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getAllowedFileTypes = () => {
    if (Array.isArray(fileTypeCategory)) {
      return fileTypeCategory.flatMap(category => FILE_TYPES[category]);
    }
    return FILE_TYPES[fileTypeCategory];
  };

  const allowedFileTypes = getAllowedFileTypes();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      const fileArray = Array.from(selectedFiles);
      validateFiles(fileArray);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateFiles = (newFiles: File[]) => {
    setError(null);

    const allowedMimeTypes = allowedFileTypes.map(type => type.mimeType);

    const invalidFiles = newFiles.filter(
      file => !allowedMimeTypes.includes(file.type),
    );

    if (invalidFiles.length > 0) {
      const extensions = allowedFileTypes
        .map(type => type.extension)
        .join(', ');
      setError(`Please select files in ${extensions} format`);
      return;
    }

    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    const largeFiles = newFiles.filter(file => file.size > maxSizeBytes);
    if (largeFiles.length > 0) {
      setError(`File size should not exceed ${maxSizeMB}MB`);
      return;
    }

    let updatedFiles: File[];

    if (multiple) {
      updatedFiles = [...files, ...newFiles];
    } else {
      updatedFiles = newFiles.slice(0, 1);
    }

    setFiles(updatedFiles);
    onChange(updatedFiles);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const fileArray = Array.from(e.dataTransfer.files);
      validateFiles(multiple ? fileArray : [fileArray[0]]);
    }
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    onChange(newFiles);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const acceptAttribute = allowedFileTypes
    .map(type => `${type.extension},${type.mimeType}`)
    .join(',');

  const getFormattedFileTypes = () => {
    return allowedFileTypes
      .map(type => type.extension.replace('.', ''))
      .join(', ')
      .toUpperCase();
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="w-full">
      <div
        className={`rounded-lg border border-white border-dashed p-6 flex flex-col items-center bg-black text-white justify-center ${
          dragActive && 'border-blue border'
        } ${!withoutImage ? 'h-72' : ''}`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id={inputId}
          ref={fileInputRef}
          className="hidden"
          accept={acceptAttribute}
          onChange={handleFileChange}
          multiple={multiple}
        />

        <div className="flex flex-col items-center justify-center gap-4">
          {!withoutImage && (
            <div className="mb-2">
              <Image
                src={FileUploadIllustration}
                alt="File upload"
                width={128}
                height={128}
                priority
              />
            </div>
          )}

          <div className="text-center">
            <span className="text-base mb-1 flex items-center justify-center gap-1">
              <Upload className="inline-block" size={18} />
              Drag & Drop or{' '}
              <button
                type="button"
                className="text-blue hover:underline"
                onClick={handleBrowseClick}
              >
                Browse Files
              </button>
            </span>

            {!withoutImage && (
              <Button
                variant="default"
                disabled={disabled}
                className="w-full my-2"
                onClick={handleBrowseClick}
              >
                Choose file
              </Button>
            )}
          </div>
        </div>
      </div>
      <p className="text-muted-foreground mt-1">
        {getFormattedFileTypes()} - Max File Size {maxSizeMB}MB
      </p>

      {files.length > 0 && (
        <div className="mt-4">
          <ul className="space-y-2">
            {files.map((file, index) => (
              <li
                key={`${file.name}`}
                className="flex items-center justify-between p-3 rounded-lg"
              >
                <span className="truncate max-w-xs text-sm">{file.name}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeFile(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {error && (
        <div className="mt-2 text-sm text-destructive bg-red-50 p-2 rounded">
          {error}
        </div>
      )}
    </div>
  );
};
