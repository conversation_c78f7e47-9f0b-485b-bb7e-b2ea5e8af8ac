import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

import ActiveButton from './ActiveButton';
import { sidebarBadge, sidebarIcon, sidebarItem } from './sidebar.styles';
import SidebarChild from './SidebarChild';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '~/components/ui/collapsible';

type Child = { title: string; href: string; badge?: string | number };

export default function SidebarGroup({
  title,
  icon,
  badge,
  children,
  parentActive,
  isChildActive,
}: {
  title: string;
  icon?: React.ReactNode;
  badge?: string | number;
  children: Child[];
  parentActive: boolean;
  isChildActive: (href: string) => boolean;
}) {
  const triggerContent = (
    <div className={sidebarItem({ variant: 'item', active: parentActive })}>
      <div className="flex items-center gap-3">
        {icon}
        <span>{title}</span>
        {badge !== undefined && (
          <span className={sidebarBadge({ active: parentActive })}>
            {badge}
          </span>
        )}
      </div>
      <div className="flex items-center">
        <ChevronDown
          className={`${sidebarIcon({ variant: 'item' })} transition-transform duration-300 data-[state=open]:rotate-0 rotate-[-90deg]`}
        />
      </div>
    </div>
  );

  return (
    <Collapsible defaultOpen={parentActive}>
      <CollapsibleTrigger className="w-full">
        {parentActive ? (
          <ActiveButton layoutId="sidebar-group-active">
            {triggerContent}
          </ActiveButton>
        ) : (
          triggerContent
        )}
      </CollapsibleTrigger>

      <CollapsibleContent className="overflow-hidden">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="relative pl-11 py-2 space-y-1"
        >
          <div className="absolute left-6 w-px bg-[#737373] top-4 bottom-4" />
          {children.map((child, index) => (
            <motion.div
              key={child.title}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.2,
                delay: index * 0.05,
              }}
            >
              <SidebarChild
                title={child.title}
                href={child.href}
                badge={child.badge}
                active={isChildActive(child.href)}
              />
            </motion.div>
          ))}
        </motion.div>
      </CollapsibleContent>
    </Collapsible>
  );
}
