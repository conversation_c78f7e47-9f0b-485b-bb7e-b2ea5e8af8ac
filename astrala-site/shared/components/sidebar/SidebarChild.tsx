import Link from 'next/link';

import ActiveButton from './ActiveButton';
import { sidebarBadge, sidebarItem } from './sidebar.styles';

export default function SidebarChild({
  title,
  href,
  badge,
  active,
}: {
  title: string;
  href: string;
  badge?: string | number;
  active: boolean;
}) {
  const content = (
    <div className={sidebarItem({ variant: 'child', active })}>
      <span>{title}</span>
      {badge && <span className={sidebarBadge({ active })}>{badge}</span>}
    </div>
  );

  return (
    <Link href={href} className="block">
      {active ? (
        <ActiveButton layoutId="sidebar-child-active">{content}</ActiveButton>
      ) : (
        content
      )}
    </Link>
  );
}
