import Link from 'next/link';

import ActiveButton from './ActiveButton';
import { sidebarBadge, sidebarItem } from './sidebar.styles';

export default function SidebarItem({
  title,
  href,
  icon,
  badge,
  active,
}: {
  title: string;
  href: string;
  icon?: React.ReactNode;
  badge?: string | number;
  active: boolean;
}) {
  const content = (
    <div className={sidebarItem({ variant: 'item', active })}>
      <div className="flex items-center gap-3">
        {icon}
        <span>{title}</span>
      </div>
      {badge && <span className={sidebarBadge({ active })}>{badge}</span>}
    </div>
  );

  return (
    <Link href={href} className="block">
      {active ? (
        <ActiveButton layoutId="sidebar-active">{content}</ActiveButton>
      ) : (
        content
      )}
    </Link>
  );
}
