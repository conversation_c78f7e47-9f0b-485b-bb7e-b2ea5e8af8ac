'use client';

import { createContext, ReactNode, useContext } from 'react';

import { useAuth } from './AuthProvider';
import { useCompanyQuestionnaireTokens } from '~/api/entities/vacancy/questionnaire/queries';
import { useCompanyVacancies } from '~/api/features/company/queries.ts';

type OnboardingContextType = {
  isLoading: boolean;
  needsBasicOnboarding: boolean;
  needsVacancyOnboarding: boolean;
  needsJobSeekerOnboarding: boolean;
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined,
);

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const {
    isAuthenticated,
    userRole,
    employerProfile,
    jobSeekerProfile,
    isLoading: authLoading,
  } = useAuth();

  const { data: vacancies, isLoading: vacanciesLoading } =
    useCompanyVacancies();

  const { data: questionnaireTokens, isLoading: tokensLoading } =
    useCompanyQuestionnaireTokens();

  const isLoading = authLoading || vacanciesLoading || tokensLoading;

  const employerBasicOnboardingCompleted = (() => {
    if (!isAuthenticated || userRole !== 'employer' || !employerProfile) {
      return false;
    }

    const isCompleted = Boolean(
      employerProfile.company_id && employerProfile.full_name,
    );
    return isCompleted;
  })();

  const jobSeekerOnboardingCompleted = (() => {
    if (!isAuthenticated || userRole !== 'job_seeker' || !jobSeekerProfile) {
      return false;
    }

    const isCompleted = Boolean(
      jobSeekerProfile.full_name &&
        jobSeekerProfile.job_title &&
        jobSeekerProfile.resume_path &&
        jobSeekerProfile.phone &&
        jobSeekerProfile.city &&
        jobSeekerProfile.country &&
        jobSeekerProfile.salary_min &&
        jobSeekerProfile.salary_max,
    );

    return isCompleted;
  })();

  const hasCompletedVacancyOnboarding = (() => {
    if (!vacancies || !questionnaireTokens) {
      return false;
    }

    const hasVacanciesWithAnswers = vacancies.some(
      vacancy => vacancy.status === 'OPENED',
    );

    const hasSentTokens = questionnaireTokens.length > 0;

    return hasVacanciesWithAnswers || hasSentTokens;
  })();

  const needsBasicOnboarding =
    isAuthenticated &&
    userRole === 'employer' &&
    !employerBasicOnboardingCompleted;

  const needsVacancyOnboarding =
    isAuthenticated &&
    userRole === 'employer' &&
    employerBasicOnboardingCompleted &&
    !hasCompletedVacancyOnboarding;

  const needsJobSeekerOnboarding =
    isAuthenticated &&
    userRole === 'job_seeker' &&
    !jobSeekerOnboardingCompleted;

  const value = {
    isLoading,
    needsBasicOnboarding,
    needsVacancyOnboarding,
    needsJobSeekerOnboarding,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
