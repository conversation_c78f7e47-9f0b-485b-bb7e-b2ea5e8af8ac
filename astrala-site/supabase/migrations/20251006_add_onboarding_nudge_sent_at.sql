-- Add onboarding_nudge_sent_at column to job_seekers table
-- This column tracks when an onboarding nudge email was sent to inactive job seekers

ALTER TABLE public.job_seekers
  ADD COLUMN onboarding_nudge_sent_at timestamp with time zone;

-- Add comment to document the purpose of this column
COMMENT ON COLUMN public.job_seekers.onboarding_nudge_sent_at IS 'Timestamp when onboarding nudge email was sent to inactive job seeker';


-- Add information_update_nudge_sent_at column to job_seekers table
-- This column tracks when a profile update nudge email was sent to active job seekers

ALTER TABLE public.job_seekers
  ADD COLUMN information_update_nudge_sent_at timestamp with time zone;

-- Add comment to document the purpose of this column
COMMENT ON COLUMN public.job_seekers.information_update_nudge_sent_at IS 'Timestamp when profile update nudge email was sent to job seeker who hasnt updated profile in 60+ days';
