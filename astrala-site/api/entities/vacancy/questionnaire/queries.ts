'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  getCompanyQuestionnaireTokens,
  getQuestionnaireTokenData,
  getVacancyQuestionGroups,
  getVacancyQuestionnaireAnswers,
  QuestionnaireAnswer,
  savePublicQuestionnaireAnswers,
  saveVacancyQuestionnaireAnswers,
} from './actions';
import { companyKeys } from '~/api/features/company/queries.ts';
import {
  sendQuestionnaireEmail,
  SendQuestionnaireRequest,
} from '~/api/features/mailing/actions.ts';

export const vacancyQuestionnaireKeys = {
  all: ['vacancy', 'questionnaire'] as const,
  groups: () => [...vacancyQuestionnaireKeys.all, 'groups'] as const,
  answers: (vacancyId: number) =>
    [...vacancyQuestionnaireKeys.all, 'answers', vacancyId] as const,
};

export function useVacancyQuestionGroups() {
  return useQuery({
    queryKey: vacancyQuestionnaireKeys.groups(),
    queryFn: async () => {
      try {
        const result = await getVacancyQuestionGroups();

        return {
          groups: result.groups.map(group => ({
            id: group.id,
            name: group.name,
            created_at: group.created_at,
          })),
          questions: result.questions.map(question => ({
            id: question.id,
            name: question.name,
            group_id: question.group_id,
            created_at: question.created_at,
          })),
          answerTags: result.answerTags,
        };
      } catch (error) {
        console.error('Error in useVacancyQuestionGroups:', error);
        throw error;
      }
    },
  });
}

export function useSaveVacancyQuestionnaireAnswers() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      vacancyId,
      answers,
    }: {
      vacancyId: number;
      answers: QuestionnaireAnswer[];
    }) => {
      return saveVacancyQuestionnaireAnswers(vacancyId, answers);
    },
    onSuccess: async () => {
      return await queryClient.invalidateQueries({
        queryKey: companyKeys.vacancies(),
        refetchType: 'all',
      });
    },
  });
}

export function useSendQuestionnaireEmail() {
  return useMutation({
    mutationFn: async (request: SendQuestionnaireRequest) => {
      const result = await sendQuestionnaireEmail(request);
      if (!result.success) {
        throw new Error(result.error || 'Failed to send questionnaire email');
      }
      return result;
    },
    onError: error => {
      console.error('Failed to send questionnaire email:', error);
    },
  });
}

export function useCompanyQuestionnaireTokens() {
  return useQuery({
    queryKey: ['questionnaireTokens'],
    queryFn: async () => {
      const result = await getCompanyQuestionnaireTokens();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch tokens');
      }
      return result.data || [];
    },
  });
}

export function useQuestionnaireTokenData(token: string) {
  return useQuery({
    queryKey: ['questionnaireTokenData'],
    queryFn: async () => {
      const result = await getQuestionnaireTokenData(token);
      if (!result.success) {
        throw new Error(result.error || 'Invalid token');
      }
      return result.data;
    },
    enabled: Boolean(token),
  });
}

export function useSavePublicQuestionnaireAnswers() {
  return useMutation({
    mutationFn: async ({
      token,
      answers,
    }: {
      token: string;
      answers: { question_id: number; value: number }[];
    }) => {
      const result = await savePublicQuestionnaireAnswers(token, answers);
      if (!result.success) {
        throw new Error(result.error || 'Failed to save answers');
      }
      return result;
    },
  });
}

export function useVacancyQuestionnaireAnswers(vacancyId?: number) {
  return useQuery({
    queryKey: vacancyQuestionnaireKeys.answers(vacancyId!),
    queryFn: async () => {
      if (!vacancyId) return [];
      return await getVacancyQuestionnaireAnswers(vacancyId);
    },
    enabled: Boolean(vacancyId),
  });
}
