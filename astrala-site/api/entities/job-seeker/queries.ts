'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { userKeys } from '../../features/user/queries.ts';

import {
  addEducation,
  addLanguage,
  addWorkExperience,
  getJobSeekerCanEditInfo,
  getJobSeekerCanEditWorkStyle,
  getJobSeekerFullProfile,
  updateJobSeekerProfile,
  updateJobSeekerWorkStyle,
} from './actions.ts';
import {
  uploadCertification,
  uploadResume,
} from '~/api/entities/job-seeker/upload-actions.ts';
import { createClient } from '~/supabase/client.ts';

export function useUpdateJobSeekerProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      jobSeekerId: number;
      fullName: string;
      jobTitle: string;
      phone: string;
      country: string;
      city: string;
      salary_min?: number;
      salary_max?: number;
      has_driving_license?: boolean;
    }) => {
      return await updateJob<PERSON>eekerProfile(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.all,
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useAddEducation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      jobSeekerId: number;
      type: string;
      institution: string;
      discipline: string;
      startYear: number;
      endYear: number;
    }) => {
      return await addEducation(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.profile(''),
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useUploadCertification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      file,
      jobSeekerCertificationId,
      userId,
    }: {
      file: File;
      jobSeekerCertificationId: number;
      userId: string;
    }) => {
      return await uploadCertification(file, jobSeekerCertificationId, userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.profile(''),
      });
    },
  });
}

export function useAddWorkExperience() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      jobSeekerId: number;
      companyName: string;
      jobTitle: string;
      startMonth: number;
      startYear: number;
      endMonth: number;
      endYear: number;
      comment: string;
    }) => {
      return await addWorkExperience(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.profile(''),
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useAddLanguage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      jobSeekerId: number;
      language: string;
      level: string;
    }) => {
      return await addLanguage(params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.profile(''),
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useUploadResume() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      file,
      userId,
      jobSeekerId,
    }: {
      file: File;
      userId: string;
      jobSeekerId: number;
    }) => {
      return await uploadResume(userId, file, jobSeekerId);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: userKeys.profile(variables.userId),
      });
    },
  });
}

export function useUpdateJobSeekerWorkStyle() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (jobSeekerId: number) => {
      return await updateJobSeekerWorkStyle(jobSeekerId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.all,
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useJobSeekerCanEditInfo(jobSeekerId?: number) {
  return useQuery({
    queryKey: ['jobSeeker', 'canEditInfo', jobSeekerId],
    queryFn: async () => {
      if (!jobSeekerId) return { canEdit: false };
      return await getJobSeekerCanEditInfo(jobSeekerId);
    },
    enabled: Boolean(jobSeekerId),
  });
}

export function useJobSeekerCanEditWorkStyle(jobSeekerId?: number) {
  return useQuery({
    queryKey: ['jobSeeker', 'canEditWorkStyle', jobSeekerId],
    queryFn: async () => {
      if (!jobSeekerId) return { canEdit: false };
      return await getJobSeekerCanEditWorkStyle(jobSeekerId);
    },
    enabled: Boolean(jobSeekerId),
  });
}

export function useJobSeekerFullProfile(jobSeekerId?: number, userId?: string) {
  return useQuery({
    queryKey: ['jobSeeker', 'fullProfile', jobSeekerId, userId],
    queryFn: async () => {
      if (!jobSeekerId || !userId)
        return {
          success: true,
          data: {
            educations: [],
            workExperiences: [],
            languages: [],
            skills: [],
            certifications: [],
          },
        };
      return await getJobSeekerFullProfile(jobSeekerId);
    },
    enabled: Boolean(jobSeekerId) && Boolean(userId),
  });
}

export function useDeleteEducation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ educationId }: { educationId: number }) => {
      const supabase = createClient();
      const { error } = await supabase
        .from('job_seeker_educations')
        .delete()
        .eq('id', educationId);

      if (error) throw new Error(error.message);
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'educations'],
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'fullProfile'],
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'canEditInfo'],
      });
    },
  });
}

export function useDeleteWorkExperience() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ experienceId }: { experienceId: number }) => {
      const supabase = createClient();
      const { error } = await supabase
        .from('job_seeker_work_experience')
        .delete()
        .eq('id', experienceId);

      if (error) throw new Error(error.message);
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'workExperiences'],
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'fullProfile'],
      });
    },
  });
}

export function useDeleteLanguage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ languageId }: { languageId: number }) => {
      const supabase = createClient();
      const { error } = await supabase
        .from('job_seeker_languages')
        .delete()
        .eq('id', languageId);

      if (error) throw new Error(error.message);
      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'languages'],
      });
      queryClient.invalidateQueries({
        queryKey: ['jobSeeker', 'fullProfile'],
      });
    },
  });
}
