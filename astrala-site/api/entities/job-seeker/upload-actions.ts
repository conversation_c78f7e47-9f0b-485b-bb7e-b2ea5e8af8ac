'use client';
import { ParsedResume } from '~/app/onboarding/job-seeker/page.tsx';

import { uploadFile } from '~/api/entities/file/actions.ts';
import { parseResume } from '~/api/features/cv-parsing/actions';
import { requireAuth } from '~/api/security';
import { createClient } from '~/supabase/client.ts';

export async function uploadCertification(
  file: File,
  jobSeekerCertificationId: number,
  userId: string,
): Promise<{
  success: boolean;
  file_path: string | null;
  error?: string;
}> {
  const authContext = await requireAuth();

  if (authContext.userId !== userId) {
    throw new Error('Forbidden: Can only upload files for yourself');
  }
  const fileResult = await uploadFile(userId, file, 'certifications');

  if (!fileResult.success) {
    return {
      success: false,
      file_path: null,
      error: fileResult.error,
    };
  }

  const supabase = createClient();
  try {
    const { error: updateError } = await supabase
      .from('job_seeker_certifications')
      .update({ file_path: fileResult.file_path })
      .eq('id', jobSeekerCertificationId);

    if (updateError) {
      return {
        success: false,
        file_path: fileResult.file_path,
        error: updateError.message,
      };
    }

    return {
      success: true,
      file_path: fileResult.file_path,
    };
  } catch (error: unknown) {
    return {
      success: false,
      file_path: fileResult.file_path,
      error: String(error),
    };
  }
}

export async function uploadDrivingLicense(
  userId: string,
  file: File,
  jobSeekerId: number,
): Promise<{
  success: boolean;
  driving_license_path: string | null;
  error?: string;
}> {
  const authContext = await requireAuth();

  if (authContext.userId !== userId) {
    throw new Error('Forbidden: Can only upload files for yourself');
  }
  if (
    authContext.userType !== 'job_seeker' ||
    authContext.jobSeekerId !== jobSeekerId
  ) {
    throw new Error('Forbidden: Can only upload files for your profile');
  }
  const fileResult = await uploadFile(userId, file, 'driving-licenses');

  if (!fileResult.success) {
    return {
      success: false,
      driving_license_path: null,
      error: fileResult.error,
    };
  }

  const supabase = createClient();
  try {
    const { error: updateError } = await supabase
      .from('job_seekers')
      .update({ driving_license_path: fileResult.file_path })
      .eq('id', jobSeekerId);

    if (updateError) {
      return {
        success: false,
        driving_license_path: fileResult.file_path,
        error: updateError.message,
      };
    }

    return {
      success: true,
      driving_license_path: fileResult.file_path,
    };
  } catch (error: unknown) {
    return {
      success: false,
      driving_license_path: fileResult.file_path,
      error: String(error),
    };
  }
}

export async function uploadResume(
  userId: string,
  file: File,
  jobSeekerId: number,
): Promise<{
  success: boolean;
  resume_path: string | null;
  parsed_data?: ParsedResume;
  error?: string;
}> {
  const authContext = await requireAuth();

  if (authContext.userId !== userId) {
    throw new Error('Forbidden: Can only upload files for yourself');
  }
  if (
    authContext.userType !== 'job_seeker' ||
    authContext.jobSeekerId !== jobSeekerId
  ) {
    throw new Error('Forbidden: Can only upload files for your profile');
  }
  const fileResult = await uploadFile(userId, file, 'cvs');

  if (!fileResult.success) {
    return {
      success: false,
      resume_path: null,
      error: fileResult.error,
    };
  }

  const supabase = createClient();
  try {
    const { data: presignedData, error: presignedError } =
      await supabase.storage
        .from('cvs')
        .createSignedUrl(fileResult.file_path!.replace('cvs/', ''), 10);

    if (presignedError || !presignedData?.signedUrl) {
      throw new Error(
        presignedError?.message || 'Could not generate presigned URL',
      );
    }

    const parseResult = await parseResume(presignedData.signedUrl);

    const { error: updateError } = await supabase
      .from('job_seekers')
      .update({ resume_path: fileResult.file_path })
      .eq('id', jobSeekerId);

    if (updateError) {
      return {
        success: false,
        resume_path: fileResult.file_path,
        error: updateError.message,
      };
    }

    return {
      success: true,
      resume_path: fileResult.file_path,
      parsed_data: parseResult.success
        ? (parseResult.data as unknown as ParsedResume)
        : undefined,
      error: parseResult.success ? undefined : parseResult.error,
    };
  } catch (error: unknown) {
    return {
      success: false,
      resume_path: fileResult.file_path,
      error: String(error),
    };
  }
}

export async function uploadPassport(
  userId: string,
  file: File,
  jobSeekerId: number,
): Promise<{
  success: boolean;
  passport_path: string | null;
  error?: string;
}> {
  const authContext = await requireAuth();

  if (authContext.userId !== userId) {
    throw new Error('Forbidden: Can only upload files for yourself');
  }
  if (
    authContext.userType !== 'job_seeker' ||
    authContext.jobSeekerId !== jobSeekerId
  ) {
    throw new Error('Forbidden: Can only upload files for your profile');
  }
  const fileResult = await uploadFile(userId, file, 'passports');

  if (!fileResult.success) {
    return {
      success: false,
      passport_path: null,
      error: fileResult.error,
    };
  }

  const supabase = createClient();
  try {
    const { error: updateError } = await supabase
      .from('job_seekers')
      .update({ passport_path: fileResult.file_path })
      .eq('id', jobSeekerId);

    if (updateError) {
      return {
        success: false,
        passport_path: fileResult.file_path,
        error: updateError.message,
      };
    }

    return {
      success: true,
      passport_path: fileResult.file_path,
    };
  } catch (error: unknown) {
    return {
      success: false,
      passport_path: fileResult.file_path,
      error: String(error),
    };
  }
}
