'use server';

import { randomUUID } from 'crypto';
import { z } from 'zod';

import { createClient } from '~/supabase/server.ts';

export type EmailData = {
  from: string;
  to: string;
  subject: string;
  htmlBody: string;
  textBody: string;
  messageStream?: string;
  tag?: string;
};

export async function sendEmail(emailData: EmailData) {
  try {
    const postmarkServerToken = process.env.POSTMARK_SERVER_API;

    if (!postmarkServerToken) {
      throw new Error(
        'Postmark server token is not configured in environment variables',
      );
    }

    const postmarkData = {
      From: emailData.from,
      To: emailData.to,
      Subject: emailData.subject,
      HtmlBody: emailData.htmlBody,
      TextBody: emailData.textBody,
      MessageStream: emailData.messageStream || 'outbound',
      Tag: emailData.tag,
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': postmarkServerToken,
      },
      body: JSON.stringify(postmarkData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Postmark API error: ${errorData.Message || 'Unknown error'}`,
      );
    }

    const result = await response.json();
    return { success: true, messageId: result.MessageID };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

const SendInvitationSchema = z.object({
  recipientEmail: z.string().email('Invalid email address'),
  inviteSenderName: z.string().min(1, 'Sender name is required'),
  companyName: z.string().min(1, 'Company name is required'),
  actionUrl: z.string().url('Invalid action URL'),
});

type SendInvitationInput = z.infer<typeof SendInvitationSchema>;

export async function sendInvitationEmail(input: SendInvitationInput) {
  try {
    const postmarkServerToken = process.env.POSTMARK_SERVER_API;

    if (!postmarkServerToken) {
      throw new Error(
        'Postmark server token is not configured in environment variables',
      );
    }

    const validatedInput = SendInvitationSchema.parse(input);

    const htmlBody = `
      <h1>Hi!</h1>
      <p>${validatedInput.inviteSenderName} from ${validatedInput.companyName} has invited you to use Astrala Nexus to collaborate with them. Use the button below to set up your account and get started:</p>
      <!-- Action -->
      <table class="body-action" align="center" width="100%" cellpadding="0" cellspacing="0">
        <tr>
          <td align="center">
            <!-- Border based button https://litmus.com/blog/a-guide-to-bulletproof-buttons-in-email-design -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td align="center">
                  <table border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td>
                        <a href="${validatedInput.actionUrl}" class="button button--" target="_blank">Set up account</a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <p>Welcome aboard,
        <br>The Astrala Nexus Team</p>
      <!-- Sub copy -->
      <table class="body-sub">
        <tr>
          <td>
            <p class="sub">If you're having trouble with the button above, copy and paste the URL below into your web browser.</p>
            <p class="sub">${validatedInput.actionUrl}</p>
          </td>
        </tr>
      </table>
    `;

    const textBody = `
      Hi!

      ${validatedInput.inviteSenderName} from ${validatedInput.companyName} has invited you to use Astrala Nexus to collaborate with them.

      Set up your account: ${validatedInput.actionUrl}

      Welcome aboard,
      The Astrala Nexus Team

    `;

    const emailData = {
      From: '<EMAIL>',
      To: validatedInput.recipientEmail,
      Subject: `${validatedInput.inviteSenderName} invited you to Astrala Nexus`,
      HtmlBody: htmlBody,
      TextBody: textBody,
      MessageStream: 'outbound',
      Tag: 'Invitation',
    };

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': postmarkServerToken,
      },
      body: JSON.stringify(emailData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `Postmark API error: ${errorData.Message || 'Unknown error'}`,
      );
    }

    const result = await response.json();
    return { success: true, messageId: result.MessageID };
  } catch (error) {
    console.error('Error sending invitation email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

const SendQuestionnaireSchema = z.object({
  vacancyId: z.number(),
  hiringManagerName: z.string().min(1),
  hiringManagerEmail: z.string().email(),
  note: z.string().optional(),
  vacancyTitle: z.string().min(1),
});

export type SendQuestionnaireRequest = z.infer<typeof SendQuestionnaireSchema>;

export async function sendQuestionnaireEmail(data: SendQuestionnaireRequest) {
  try {
    const validated = SendQuestionnaireSchema.parse(data);
    const supabase = await createClient();

    const token = randomUUID().replace(/-/g, '');

    const { error: insertError } = await supabase
      .from('questionnaire_tokens')
      .insert({
        vacancy_id: validated.vacancyId,
        token,
        hiring_manager_email: validated.hiringManagerEmail,
        hiring_manager_name: validated.hiringManagerName,
        note: validated.note || null,
      });

    if (insertError) {
      throw new Error(`Database error: ${insertError.message}`);
    }

    const questionnaireUrl = `${process.env.APP_URL}/questionnaire/${token}`;

    const htmlBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 40px 20px;">
        <h1 style="color: #333; font-size: 24px; margin-bottom: 30px; text-align: center;">
          Define the Behavioural Insight Preferences for your ${validated.vacancyTitle} role.
        </h1>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          Hi ${validated.hiringManagerName},
        </p>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          Please complete the Behavioural Insight Preferences for your ${validated.vacancyTitle} role.
        </p>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          This behavioural preferences questionnaire helps clarify the traits you value most for this role.
        </p>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 30px;">
          Click below to get started — it takes just a few minutes.
        </p>

        ${
          validated.note
            ? `
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
          <p style="color: #333; font-size: 14px; margin: 0; font-style: italic;">
            Note: ${validated.note}
          </p>
        </div>
        `
            : ''
        }

        <div style="text-align: center; margin: 40px 0;">
          <a href="${questionnaireUrl}"
             style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: 500; display: inline-block;">
            Define Behavioural Insight Preferences
          </a>
        </div>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
          Your preferences will be automatically linked to this vacancy and guide smarter, insight-led matching.
        </p>

        <p style="color: #333; font-size: 16px; line-height: 1.5; margin-bottom: 5px;">
          Warm regards,
        </p>
        <p style="color: #333; font-size: 16px; line-height: 1.5;">
          The Brownlee Cale Connect Team
        </p>
      </div>
    `;

    const textBody = `
      Define the Behavioural Insight Preferences for your ${validated.vacancyTitle} role.

      Hi ${validated.hiringManagerName},

      Please complete the Behavioural Insight Preferences for your ${validated.vacancyTitle} role.

      This behavioural preferences questionnaire helps clarify the traits you value most for this role.

      Click the link to get started: ${questionnaireUrl}

      ${validated.note ? `Note: ${validated.note}\n\n` : ''}

      Your preferences will be automatically linked to this vacancy and guide smarter, insight-led matching.

      Warm regards,
      The Brownlee Cale Connect Team
    `;

    const emailResult = await sendEmail({
      from: '<EMAIL>',
      to: validated.hiringManagerEmail,
      subject: `Complete Behavioural Preferences for ${validated.vacancyTitle} role`,
      htmlBody,
      textBody,
      tag: 'Questionnaire',
    });

    if (!emailResult.success) {
      throw new Error(emailResult.error || 'Failed to send email');
    }

    return {
      success: true,
      token,
      messageId: emailResult.messageId,
    };
  } catch (error) {
    console.error('Error sending questionnaire email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
