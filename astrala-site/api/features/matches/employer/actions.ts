'use server';

import { MatchData as BaseMatchData } from '~/app/dashboard/(components)/MatchCard.tsx';

import { Database } from '~/shared/db/generated/types';

import { sendCompanyInterestedEmail } from '../../mailing/company-interested/action';

import { sendMatchNotificationEmail } from '~/api/features/mailing/match/action.ts';
import { createClient } from '~/supabase/server.ts';

type MatchData = BaseMatchData & {
  jobSeeker: {
    passport_path?: string;
    certifications?: Database['public']['Tables']['job_seeker_certifications']['Row'][];
  };
};

export type EmployerMatchStatus =
  | 'PENDING'
  | 'INTERESTED'
  | 'REJECTED'
  | 'PAID';

export type EmployerVacancyMatch = {
  id: number;
  created_at: string;
  updated_at: string;
  job_seeker_id: number;
  job_seeker_status: string;
  employer_status: string;
  ai_match_score: number;
  vacancy_id: number;
  job_seeker_transaction_id?: number;
  employer_transaction_id?: number;
  employer_rejected_at?: string;
  job_seeker_rejected_at?: string;

  jobSeeker?: {
    id: number;
    full_name: string;
    job_title: string;
    country?: string;
    city?: string;
    salary_min?: number;
    salary_max?: number;
    driving_license_path?: string;
    resume_path?: string;
    passport_path?: string;
    phone?: string;
    work_experience?: Array<{
      id: number;
      company_name: string;
      job_title: string;
      start_date: string;
      end_date?: string;
      comment?: string;
    }>;
    education?: Array<{
      id: number;
      type: string;
      institution: string;
      discipline: string;
      start_year: number;
      end_year?: number;
    }>;
    certifications?: Database['public']['Tables']['job_seeker_certifications']['Row'][];
  };

  vacancy?: {
    id: number;
    title: string;
    description: string;
    salary_min?: number;
    salary_max?: number;
    employer_id: number;
    company_id?: number;
    driving_license?: boolean;
    education_degree?: string;
    education_discipline?: string;
    country?: string;
    city?: string;
    experience_years_from?: number;
  };

  match_description?: {
    vacancy_match_id: number;
    driving_license_match: boolean;
    education_match: boolean;
    location_match: boolean;
    salary_match: boolean;
    work_experience_match: boolean;
    work_experience_years?: number;
    lacking_skills?: string | string[];
    matching_skills?: string | string[];
    matching_behavioural_tags?: string | string[];
    lacking_behavioural_tags?: string | string[];
    ai_summary?: string;
  };
};

export type PaginatedResponse<T> = {
  data: T[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

function maskName(fullName: string): string {
  if (!fullName) return '';

  return fullName
    .split(' ')
    .map(namePart => {
      if (!namePart) return '';
      return namePart[0] + '*'.repeat(Math.max(namePart.length - 1, 0));
    })
    .join(' ');
}

async function getSignedUrl(
  filePath: string,
  fileType: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  supabase: any,
): Promise<string | null> {
  if (!filePath) return null;

  try {
    let bucket = '';
    switch (fileType) {
      case 'resume':
        bucket = 'cvs';
        break;
      case 'driving_license':
        bucket = 'driving-licenses';
        break;
      case 'passport':
        bucket = 'passports';
        break;
      case 'certification':
        bucket = 'certifications';
        break;
      default:
        bucket = 'cvs';
    }

    const fullPath = filePath;

    if (!fullPath) return null;

    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(fullPath, 180 * 24 * 60 * 60);

    if (error) {
      console.error(`Error creating signed URL from bucket ${bucket}:`, error);
      return null;
    }

    return data?.signedUrl || null;
  } catch (error) {
    console.error('Exception creating signed URL:', error);
    return null;
  }
}

async function getEmployerVacancyMatchesPaginated(
  employerId: number,
  vacancyId: number | null,
  jobSeekerStatus: EmployerMatchStatus,
  employerStatus: EmployerMatchStatus,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  if (!employerId) {
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  const supabase = await createClient();

  try {
    let vacancyIds: number[] | null = null;
    if (!vacancyId) {
      const { data: vacancies } = await supabase
        .from('vacancies')
        .select('id')
        .eq('employer_id', employerId);

      if (!vacancies || vacancies.length === 0) {
        return {
          data: [],
          totalCount: 0,
          currentPage: page,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      }

      vacancyIds = vacancies.map(v => v.id);
    }

    let countQuery = supabase
      .from('vacancy_matches')
      .select('*', { count: 'exact' })
      .eq('employer_status', employerStatus)
      .eq('job_seeker_status', jobSeekerStatus);

    if (vacancyId) {
      countQuery = countQuery.eq('vacancy_id', vacancyId);
    } else if (vacancyIds) {
      countQuery = countQuery.in('vacancy_id', vacancyIds);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting employer vacancy matches:', countError);
      return {
        data: [],
        totalCount: 0,
        currentPage: page,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      };
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    let query = supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id (
          *
        )
      `,
      )
      .eq('employer_status', employerStatus)
      .eq('job_seeker_status', jobSeekerStatus)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (vacancyId) {
      query = query.eq('vacancy_id', vacancyId);
    } else if (vacancyIds) {
      query = query.in('vacancy_id', vacancyIds);
    }

    const { data: matchesData, error: matchesError } = await query;

    if (matchesError) {
      console.error('Error fetching employer vacancy matches:', matchesError);
      return {
        data: [],
        totalCount,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    }

    if (!matchesData || matchesData.length === 0) {
      return {
        data: [],
        totalCount,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    }

    const enhancedMatches = await Promise.all(
      matchesData.map(async (match: MatchData) => {
        if (match.job_seeker_id) {
          const isMatched =
            match.employer_status === 'INTERESTED' &&
            match.job_seeker_status === 'INTERESTED';
          const { data: jobSeekerData, error: jobSeekerError } = await supabase
            .from('job_seekers')
            .select('*')
            .eq('id', match.job_seeker_id)
            .single();

          if (!jobSeekerError && jobSeekerData) {
            const { data: workExperienceData, error: workExperienceError } =
              await supabase
                .from('job_seeker_work_experience')
                .select('*')
                .eq('job_seeker_id', match.job_seeker_id)
                .order('end_date', { ascending: false });

            const { data: educationData, error: educationError } =
              await supabase
                .from('job_seeker_educations')
                .select('*')
                .eq('job_seeker_id', match.job_seeker_id)
                .order('end_year', { ascending: false });

            let certificationsData = [];
            if (isMatched) {
              const { data: certData, error: certError } = await supabase
                .from('job_seeker_certifications')
                .select('*')
                .eq('job_seeker_id', match.job_seeker_id);

              if (!certError && certData) {
                certificationsData = certData;
              }
            }

            const jobSeekerFullName = isMatched
              ? jobSeekerData.full_name
              : maskName(jobSeekerData.full_name);

            const jobSeekerObj = {
              ...jobSeekerData,
              full_name: jobSeekerFullName,
              work_experience:
                !workExperienceError && workExperienceData
                  ? workExperienceData
                  : [],
              education: !educationError && educationData ? educationData : [],
            };

            if (!isMatched) {
              delete jobSeekerObj.phone;
            }

            if (isMatched) {
              jobSeekerObj.certifications = certificationsData;

              if (jobSeekerData.resume_path) {
                const resumeUrl = await getSignedUrl(
                  jobSeekerData.resume_path,
                  'resume',
                  supabase,
                );
                if (resumeUrl) jobSeekerObj.resume_path = resumeUrl;
              }

              if (jobSeekerData.driving_license_path) {
                const licenseUrl = await getSignedUrl(
                  jobSeekerData.driving_license_path,
                  'driving_license',
                  supabase,
                );
                if (licenseUrl) jobSeekerObj.driving_license_path = licenseUrl;
              }

              if (jobSeekerData.passport_path) {
                const passportUrl = await getSignedUrl(
                  jobSeekerData.passport_path,
                  'passport',
                  supabase,
                );
                if (passportUrl) jobSeekerObj.passport_path = passportUrl;
              }

              for (const certificationsDatum of certificationsData) {
                if (certificationsDatum.file_path) {
                  const certUrl = await getSignedUrl(
                    certificationsDatum.file_path,
                    'certification',
                    supabase,
                  );
                  if (certUrl) certificationsDatum.file_path = certUrl;
                }
              }
            }

            // eslint-disable-next-line require-atomic-updates
            match.jobSeeker = jobSeekerObj;
          }
        }

        const { data: matchDescData, error: matchDescError } = await supabase
          .from('vacancy_match_descriptions')
          .select(
            '*, matching_behavioural_tags, lacking_behavioural_tags, ai_summary',
          )
          .eq('vacancy_match_id', match.id)
          .single();

        if (!matchDescError && matchDescData) {
          // eslint-disable-next-line require-atomic-updates
          match.match_description = matchDescData;
        }

        return match;
      }),
    );

    return {
      data: enhancedMatches,
      totalCount,
      currentPage: page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  } catch (error) {
    console.error('Exception fetching employer vacancy matches:', error);
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }
}

export async function getSuggestions(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  return getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'PENDING',
    'PENDING',
    page,
    pageSize,
  );
}

export async function getCompanyInterested(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  return getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'PENDING',
    'INTERESTED',
    page,
    pageSize,
  );
}

export async function getJobSeekerInterested(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  return getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'INTERESTED',
    'PENDING',
    page,
    pageSize,
  );
}

export async function getJobSeekerRejected(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  return getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'REJECTED',
    'INTERESTED',
    page,
    pageSize,
  );
}

export async function getMatches(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  return getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'INTERESTED',
    'INTERESTED',
    page,
    pageSize,
  );
}

export async function getCompanyRejected(
  employerId: number,
  vacancyId: number | null,
  page = 1,
  pageSize = 5,
): Promise<PaginatedResponse<EmployerVacancyMatch>> {
  if (!employerId) {
    return {
      data: [],
      totalCount: 0,
      currentPage: page,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  const interestedRejectedCount = await getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'INTERESTED',
    'REJECTED',
    1,
    1,
  );

  const pendingRejectedCount = await getEmployerVacancyMatchesPaginated(
    employerId,
    vacancyId,
    'PENDING',
    'REJECTED',
    1,
    1,
  );

  const totalCount =
    interestedRejectedCount.totalCount + pendingRejectedCount.totalCount;
  const totalPages = Math.ceil(totalCount / pageSize);

  const interestedOffset = (page - 1) * pageSize;
  const pendingOffset = Math.max(
    0,
    interestedOffset - interestedRejectedCount.totalCount,
  );

  let interestedRejected: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: interestedRejectedCount.totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };

  if (interestedOffset < interestedRejectedCount.totalCount) {
    interestedRejected = await getEmployerVacancyMatchesPaginated(
      employerId,
      vacancyId,
      'INTERESTED',
      'REJECTED',
      Math.floor(interestedOffset / pageSize) + 1,
      Math.min(pageSize, interestedRejectedCount.totalCount - interestedOffset),
    );
  }

  let pendingRejected: PaginatedResponse<EmployerVacancyMatch> = {
    data: [],
    totalCount: pendingRejectedCount.totalCount,
    currentPage: 1,
    totalPages: Math.ceil(pendingRejectedCount.totalCount / pageSize),
    hasNextPage: false,
    hasPreviousPage: false,
  };

  if (pendingOffset >= 0 && interestedRejected.data.length < pageSize) {
    const remainingItems = pageSize - interestedRejected.data.length;

    pendingRejected = await getEmployerVacancyMatchesPaginated(
      employerId,
      vacancyId,
      'PENDING',
      'REJECTED',
      Math.floor(pendingOffset / pageSize) + 1,
      remainingItems,
    );
  }

  const combinedData = [...interestedRejected.data, ...pendingRejected.data];

  return {
    data: combinedData,
    totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}

export async function updateEmployerStatus(
  matchId: number,
  status: EmployerMatchStatus,
): Promise<{
  success: boolean;
  error?: string;
  canChangeDecision?: boolean;
  decisionDeadline?: string;
}> {
  if (!matchId) return { success: false, error: 'No match ID provided' };

  const supabase = await createClient();

  try {
    const { data: matchData, error: fetchError } = await supabase
      .from('vacancy_matches')
      .select(
        `
        *,
        vacancy:vacancy_id (
          *,
          employer:employer_id (
            *,
            company:company_id (*)
          )
        ),
        job_seeker:job_seeker_id (*)
      `,
      )
      .eq('id', matchId)
      .single();

    if (fetchError) {
      console.error('Error fetching match data:', fetchError);
      return { success: false, error: fetchError.message };
    }

    // eslint-disable-next-line sonarjs/no-collapsible-if
    if (status === 'INTERESTED' && matchData.employer_status === 'REJECTED') {
      if (matchData.employer_rejected_at) {
        const rejectedAt = new Date(matchData.employer_rejected_at);
        const now = new Date();
        const hoursSinceRejection =
          (now.getTime() - rejectedAt.getTime()) / (1000 * 60 * 60);

        if (hoursSinceRejection > 24) {
          return {
            success: false,
            error:
              'You can only change your decision within 24 hours of rejection',
            canChangeDecision: false,
          };
        }
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateData: Record<string, any> = {
      employer_status: status,
      updated_at: new Date().toISOString(),
    };

    if (status === 'REJECTED' && matchData.employer_status !== 'REJECTED') {
      updateData.employer_rejected_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('vacancy_matches')
      .update(updateData)
      .eq('id', matchId);

    if (error) {
      console.error('Error updating employer status:', error);
      return { success: false, error: error.message };
    }

    let decisionDeadline;
    if (status === 'REJECTED') {
      const deadline = new Date();
      deadline.setHours(deadline.getHours() + 24);
      decisionDeadline = deadline.toISOString();
    }

    if (status === 'INTERESTED') {
      const appUrl = process.env.APP_URL || 'https://astralanexus.ai';

      const { data: jobSeekerData, error: jobSeekerError } = await supabase
        .from('job_seekers')
        .select('email, full_name')
        .eq('id', matchData.job_seeker_id)
        .single();

      if (jobSeekerError || !jobSeekerData) {
        console.error('Error fetching job seeker data:', jobSeekerError);
        return { success: true };
      }

      const { data: vacancyData, error: vacancyError } = await supabase
        .from('vacancies')
        .select('employer_id, company_id')
        .eq('id', matchData.vacancy_id)
        .single();

      if (vacancyError) {
        console.error('Error fetching vacancy data:', vacancyError);
      }

      let companyName = 'Company';

      if (!vacancyError && vacancyData) {
        if (vacancyData.company_id) {
          const { data: companyData } = await supabase
            .from('companies')
            .select('name')
            .eq('id', vacancyData.company_id)
            .single();

          if (companyData) {
            companyName = companyData.name;
          }
        } else if (vacancyData.employer_id) {
          const { data: employerData } = await supabase
            .from('employers')
            .select('full_name')
            .eq('id', vacancyData.employer_id)
            .single();

          if (employerData) {
            companyName = employerData.full_name;
          }
        }
      }

      if (matchData.job_seeker_status === 'INTERESTED') {
        await sendMatchNotificationEmail({
          recipientEmail: jobSeekerData.email,
          recipientName: jobSeekerData.full_name,
          matchName: companyName,
          role: 'jobseeker',
          appUrl,
        });
      } else {
        await sendCompanyInterestedEmail({
          jobSeekerEmail: jobSeekerData.email,
          jobSeekerName: jobSeekerData.full_name,
          companyName,
          appUrl,
        });
      }
    }

    return {
      success: true,
      decisionDeadline,
    };
  } catch (error) {
    console.error('Exception updating employer status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export type EmployerMatchesCountsResponse = {
  suggestions: number;
  companyInterested: number;
  companyRejected: number;
  jobSeekerInterested: number;
  jobSeekerRejected: number;
  matches: number;
};

export async function getEmployerVacancyMatchesCounts(
  employerId: number,
  vacancyId: number | null,
): Promise<EmployerMatchesCountsResponse> {
  if (!employerId) {
    return {
      suggestions: 0,
      companyInterested: 0,
      companyRejected: 0,
      jobSeekerInterested: 0,
      jobSeekerRejected: 0,
      matches: 0,
    };
  }

  const supabase = await createClient();

  try {
    let vacancyIds: number[] | null = null;
    if (!vacancyId) {
      const { data: vacancies } = await supabase
        .from('vacancies')
        .select('id')
        .eq('employer_id', employerId);

      if (!vacancies || vacancies.length === 0) {
        return {
          suggestions: 0,
          companyInterested: 0,
          companyRejected: 0,
          jobSeekerInterested: 0,
          jobSeekerRejected: 0,
          matches: 0,
        };
      }

      vacancyIds = vacancies.map(v => v.id);
    }

    const getCount = async (
      jobSeekerStatus: EmployerMatchStatus,
      employerStatus: EmployerMatchStatus,
    ) => {
      let query = supabase
        .from('vacancy_matches')
        .select('*', { count: 'exact' })
        .eq('job_seeker_status', jobSeekerStatus)
        .eq('employer_status', employerStatus);

      if (vacancyId) {
        query = query.eq('vacancy_id', vacancyId);
      } else if (vacancyIds) {
        query = query.in('vacancy_id', vacancyIds);
      }

      const { count, error } = await query;

      if (error) {
        console.error(
          `Error counting matches (${jobSeekerStatus}, ${employerStatus}):`,
          error,
        );
        return 0;
      }

      return count || 0;
    };

    const suggestionsCount = await getCount('PENDING', 'PENDING');
    const companyInterestedCount = await getCount('PENDING', 'INTERESTED');

    const interestedRejectedCount = await getCount('INTERESTED', 'REJECTED');
    const pendingRejectedCount = await getCount('PENDING', 'REJECTED');
    const companyRejectedCount = interestedRejectedCount + pendingRejectedCount;

    const jobSeekerInterestedCount = await getCount('INTERESTED', 'PENDING');
    const jobSeekerRejectedCount = await getCount('REJECTED', 'INTERESTED');

    const matchesCount = await getCount('INTERESTED', 'INTERESTED');

    return {
      suggestions: suggestionsCount,
      companyInterested: companyInterestedCount,
      companyRejected: companyRejectedCount,
      jobSeekerInterested: jobSeekerInterestedCount,
      jobSeekerRejected: jobSeekerRejectedCount,
      matches: matchesCount,
    };
  } catch (error) {
    return {
      suggestions: 0,
      companyInterested: 0,
      companyRejected: 0,
      jobSeekerInterested: 0,
      jobSeekerRejected: 0,
      matches: 0,
    };
  }
}

export async function canEmployerChangeRejection(
  matchId: number,
): Promise<{ canChange: boolean; deadline?: string; error?: string }> {
  if (!matchId) return { canChange: false, error: 'No match ID provided' };

  const supabase = await createClient();

  try {
    const { data: matchData, error: fetchError } = await supabase
      .from('vacancy_matches')
      .select('*, employer_rejected_at')
      .eq('id', matchId)
      .single();

    if (fetchError) {
      console.error('Error fetching match data:', fetchError);
      return { canChange: false, error: fetchError.message };
    }

    if (
      matchData.employer_status !== 'REJECTED' ||
      !matchData.employer_rejected_at
    ) {
      return { canChange: false };
    }

    const rejectedAt = new Date(matchData.employer_rejected_at);
    const now = new Date();
    const hoursSinceRejection =
      (now.getTime() - rejectedAt.getTime()) / (1000 * 60 * 60);

    if (hoursSinceRejection <= 24) {
      const deadline = new Date(rejectedAt);
      deadline.setHours(deadline.getHours() + 24);

      return {
        canChange: true,
        deadline: deadline.toISOString(),
      };
    }

    return { canChange: false };
  } catch (error) {
    console.error(
      'Exception checking if employer can change rejection:',
      error,
    );
    return {
      canChange: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
