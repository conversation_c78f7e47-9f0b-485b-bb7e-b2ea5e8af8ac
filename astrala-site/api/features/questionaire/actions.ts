'use server';

import { Database } from '~/shared/db/generated/types.ts';

import { requireJobSeekerOwnership } from '~/api/security/job-seeker.ts';
import { createClient } from '~/supabase/client.ts';

export type Question =
  Database['public']['Tables']['job_seeker_questionnaire_questions']['Row'];
export type QuestionGroup =
  Database['public']['Tables']['job_seeker_questionnaire_groups']['Row'];
export type QuestionnaireAnswerRow =
  Database['public']['Tables']['job_seeker_questionnaire_answers']['Row'];
export type AnswerTag =
  Database['public']['Tables']['job_seeker_answer_tags']['Row'];

export type QuestionnaireAnswer = {
  question_id: number;
  value: number;
};

export async function getQuestionGroups(): Promise<{
  groups: QuestionGroup[];
  questions: Question[];
  answerTags: AnswerTag[];
}> {
  const supabase = createClient();

  const { data: groups, error: groupsError } = await supabase
    .from('job_seeker_questionnaire_groups')
    .select('*')
    .order('id', { ascending: true });

  if (groupsError) {
    throw new Error(`Failed to fetch question groups: ${groupsError.message}`);
  }

  const { data: questions, error: questionsError } = await supabase
    .from('job_seeker_questionnaire_questions')
    .select('*')
    .order('id', { ascending: true });

  if (questionsError) {
    throw new Error(`Failed to fetch questions: ${questionsError.message}`);
  }

  const { data: answerTags, error: answerTagsError } = await supabase
    .from('job_seeker_answer_tags')
    .select('*')
    .order('question_id', { ascending: true })
    .order('value', { ascending: true });

  if (answerTagsError) {
    throw new Error(`Failed to fetch answer tags: ${answerTagsError.message}`);
  }

  return {
    groups,
    questions,
    answerTags: answerTags || [],
  };
}

export async function saveQuestionnaireAnswers(
  jobSeekerId: number,
  answers: QuestionnaireAnswer[],
): Promise<{ success: boolean }> {
  await requireJobSeekerOwnership(jobSeekerId);
  const supabase = createClient();
  const now = new Date().toISOString();

  const { error: deleteError } = await supabase
    .from('job_seeker_questionnaire_answers')
    .delete()
    .eq('job_seeker_id', jobSeekerId);

  if (deleteError) {
    console.error(
      `[saveQuestionnaireAnswers] Delete failed for job_seeker_id=${jobSeekerId}:`,
      deleteError.message,
    );
    throw new Error(
      `Failed to delete existing answers: ${deleteError.message}`,
    );
  }

  const answersWithJobSeekerId = answers.map(answer => ({
    job_seeker_id: jobSeekerId,
    question_id: answer.question_id,
    value: answer.value,
    created_at: now,
    updated_at: now,
  }));

  const { error: insertError } = await supabase
    .from('job_seeker_questionnaire_answers')
    .insert(answersWithJobSeekerId);

  if (insertError) {
    console.error(
      `[saveQuestionnaireAnswers] Insert failed for job_seeker_id=${jobSeekerId}:`,
      insertError.message,
    );
    throw new Error(`Failed to save answers: ${insertError.message}`);
  }

  const { error: updateError } = await supabase
    .from('job_seekers')
    .update({
      work_style_updated_at: now,
      matches_last_calculated_at: null,
    })
    .eq('id', jobSeekerId);

  if (updateError) {
    console.error(
      `[saveQuestionnaireAnswers] Update job_seeker failed for job_seeker_id=${jobSeekerId}:`,
      updateError.message,
    );
    throw new Error(`Failed to update job seeker: ${updateError.message}`);
  }

  try {
    await calculateJobSeekerArchetypes(jobSeekerId);
  } catch (error) {
    console.error(
      `[saveQuestionnaireAnswers] Archetype calculation failed for job_seeker_id=${jobSeekerId}:`,
      error,
    );
  }

  return { success: true };
}

export async function getQuestionnaireAnswers(jobSeekerId?: number): Promise<{
  answers: QuestionnaireAnswerRow[];
}> {
  const supabase = createClient();

  let query = supabase.from('job_seeker_questionnaire_answers').select('*');

  if (jobSeekerId) {
    query = query.eq('job_seeker_id', jobSeekerId);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch questionnaire answers: ${error.message}`);
  }

  return {
    answers: data || [],
  };
}

export async function calculateJobSeekerArchetypes(
  jobSeekerId: number,
): Promise<{ success: boolean }> {
  await requireJobSeekerOwnership(jobSeekerId);
  const supabase = createClient();

  const { data: userAnswers, error: userAnswersError } = await supabase
    .from('job_seeker_questionnaire_answers')
    .select('question_id, value')
    .eq('job_seeker_id', jobSeekerId);

  if (userAnswersError) {
    throw new Error(
      `Failed to fetch user answers: ${userAnswersError.message}`,
    );
  }

  const { data: allAnswerTags, error: allTagsError } = await supabase
    .from('job_seeker_answer_tags')
    .select('question_id, value, tag, dimension_id, spectrum_pole')
    .not('dimension_id', 'is', null);

  if (allTagsError) {
    console.error(
      '[calculateJobSeekerArchetypes] Failed to fetch answer tags:',
      allTagsError.message,
    );
    throw new Error(`Failed to fetch answer tags: ${allTagsError.message}`);
  }

  const { data: dimensions, error: dimensionsError } = await supabase
    .from('dimensions')
    .select('*');

  if (dimensionsError) {
    console.error(
      '[calculateJobSeekerArchetypes] Failed to fetch dimensions:',
      dimensionsError.message,
    );
    throw new Error(`Failed to fetch dimensions: ${dimensionsError.message}`);
  }

  const { data: archetypes, error: archetypesError } = await supabase.from(
    'archetypes',
  ).select(`
      id,
      name,
      description,
      metaphor,
      archetype_dimension_targets (
        dimension_id,
        target_value
      )
    `);

  if (archetypesError) {
    console.error(
      '[calculateJobSeekerArchetypes] Failed to fetch archetypes:',
      archetypesError.message,
    );
    throw new Error(`Failed to fetch archetypes: ${archetypesError.message}`);
  }

  const dimensionScores: Record<number, number> = {};

  dimensions?.forEach(dimension => {
    let leftCount = 0;
    let rightCount = 0;

    userAnswers?.forEach(answer => {
      const matchingTags = allAnswerTags?.filter(
        tag =>
          tag.question_id === answer.question_id &&
          tag.value === answer.value &&
          tag.dimension_id === dimension.id,
      );

      matchingTags?.forEach(tag => {
        if (tag.spectrum_pole === 'left') {
          leftCount++;
        } else if (tag.spectrum_pole === 'right') {
          rightCount++;
        }
      });
    });

    const totalCount = leftCount + rightCount;
    const score = totalCount > 0 ? rightCount / totalCount : 0.5;

    dimensionScores[dimension.id] = score;
  });

  const archetypeResults: Array<{
    archetype_id: number;
    distance: number;
    dimension_scores: Record<string, number>;
  }> = [];

  archetypes?.forEach(archetype => {
    let totalDistance = 0;
    const archetypeDimensionScores: Record<string, number> = {};

    archetype.archetype_dimension_targets.forEach(target => {
      const userScore = dimensionScores[target.dimension_id] || 0;
      const targetValue = Number(target.target_value);
      const distance = Math.abs(targetValue - userScore);

      totalDistance += distance;
      archetypeDimensionScores[target.dimension_id.toString()] = userScore;
    });

    archetypeResults.push({
      archetype_id: archetype.id,
      distance: totalDistance,
      dimension_scores: archetypeDimensionScores,
    });
  });

  archetypeResults.sort((a, b) => a.distance - b.distance);

  const { error: deleteError } = await supabase
    .from('job_seeker_archetypes')
    .delete()
    .eq('job_seeker_id', jobSeekerId);

  if (deleteError) {
    throw new Error(
      `Failed to delete old archetype results: ${deleteError.message}`,
    );
  }

  const resultsToInsert = archetypeResults.map(result => ({
    job_seeker_id: jobSeekerId,
    archetype_id: result.archetype_id,
    distance: result.distance,
    dimension_scores: result.dimension_scores,
    calculated_at: new Date().toISOString(),
  }));

  const { error: insertError } = await supabase
    .from('job_seeker_archetypes')
    .insert(resultsToInsert);

  if (insertError) {
    throw new Error(`Failed to save archetype results: ${insertError.message}`);
  }

  return { success: true };
}
