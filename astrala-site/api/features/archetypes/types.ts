export type Dimension = {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
};

export type DimensionWithConfig = {
  icon: React.ComponentType;
  description: string;
  leftLabel: string;
  rightLabel: string;
} & Dimension;

export type WheelProps = {
  selectedDimension: number | null;
  onDimensionChange: (dimensionId: number) => void;
};

export type UserDimensionScore = {
  dimensionId: number;
  score: number;
};

export type ArchetypeScore = {
  id: number;
  name: string;
  description: string;
  distance: number;
  percentage: number;
};
