'use server';

import {
  ArchetypeScore,
  Dimension,
  UserDimensionScore,
} from '~/api/features/archetypes/types.ts';
import { createClient } from '~/supabase/server.ts';

export async function getUserArchetypeScores(jobSeekerId: number) {
  if (!jobSeekerId) return null;

  const supabase = await createClient();

  try {
    const { data: userArchetypes, error } = await supabase
      .from('job_seeker_archetypes')
      .select(
        `
        archetype_id,
        distance,
        dimension_scores,
        archetypes (
          id,
          name,
          description
        )
      `,
      )
      .eq('job_seeker_id', jobSeekerId)
      .order('distance', { ascending: true });

    if (error) {
      console.error(
        `[getUserArchetypeScores] Query error for job_seeker_id=${jobSeekerId}:`,
        error.message,
      );
      return null;
    }

    if (!userArchetypes || userArchetypes.length === 0) {
      return null;
    }

    const maxDistance = Math.max(...userArchetypes.map(a => a.distance));

    const archetypes: ArchetypeScore[] = userArchetypes
      .map(ua => {
        const archetype = Array.isArray(ua.archetypes)
          ? ua.archetypes[0]
          : ua.archetypes;

        if (!archetype) {
          console.error(
            `[getUserArchetypeScores] Missing archetype data for archetype_id=${ua.archetype_id}`,
          );
          return null;
        }

        return {
          id: archetype.id,
          name: archetype.name,
          description: archetype.description,
          distance: ua.distance,
          percentage: Math.round((1 - ua.distance / maxDistance) * 100),
        };
      })
      .filter((a): a is ArchetypeScore => a !== null);

    if (archetypes.length === 0) {
      console.error(
        `[getUserArchetypeScores] No valid archetypes after mapping for job_seeker_id=${jobSeekerId}`,
      );
      return null;
    }

    const dimensionScores: UserDimensionScore[] = [];
    if (userArchetypes[0]?.dimension_scores) {
      Object.entries(userArchetypes[0].dimension_scores).forEach(
        ([dimId, score]) => {
          dimensionScores.push({
            dimensionId: Number.parseInt(dimId, 10),
            score: score as number,
          });
        },
      );
    }

    return {
      archetypes,
      dimensionScores,
    };
  } catch (error) {
    console.error(
      `[getUserArchetypeScores] Error for job_seeker_id=${jobSeekerId}:`,
      error,
    );
    return null;
  }
}

export async function getDimensions(): Promise<Dimension[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('dimensions')
      .select('id, name, created_at, updated_at')
      .order('id');

    if (error) {
      console.error('[getDimensions] Error:', error.message);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('[getDimensions] Error:', error);
    return [];
  }
}

export async function getArchetypeById(archetypeId: number) {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('archetypes')
      .select('id, name, description, metaphor')
      .eq('id', archetypeId)
      .single();

    if (error) {
      console.error('[getArchetypeById] Error:', error.message);
      return null;
    }

    return data;
  } catch (error) {
    console.error('[getArchetypeById] Error:', error);
    return null;
  }
}

export async function toggleArchetypePinned(jobSeekerId: number) {
  const supabase = await createClient();

  try {
    const { data: current, error: fetchError } = await supabase
      .from('job_seekers')
      .select('archetype_pinned')
      .eq('id', jobSeekerId)
      .single();

    if (fetchError) {
      console.error('[toggleArchetypePinned] Fetch error:', fetchError.message);
      throw new Error('Failed to fetch current pin status');
    }

    const newValue = !current.archetype_pinned;

    const { error: updateError } = await supabase
      .from('job_seekers')
      .update({ archetype_pinned: newValue })
      .eq('id', jobSeekerId);

    if (updateError) {
      console.error(
        '[toggleArchetypePinned] Update error:',
        updateError.message,
      );
      throw new Error('Failed to update pin status');
    }

    return { success: true, pinned: newValue };
  } catch (error) {
    console.error('[toggleArchetypePinned] Error:', error);
    throw error;
  }
}

export async function getArchetypePinnedStatus(jobSeekerId: number) {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('job_seekers')
      .select('archetype_pinned')
      .eq('id', jobSeekerId)
      .single();

    if (error) {
      console.error('[getArchetypePinnedStatus] Error:', error.message);
      return { pinned: true };
    }

    return { pinned: data.archetype_pinned ?? true };
  } catch (error) {
    console.error('[getArchetypePinnedStatus] Error:', error);
    return { pinned: true };
  }
}
