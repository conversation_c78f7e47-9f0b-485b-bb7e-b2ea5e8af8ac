'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  getArchetypeById,
  getArchetypePinnedStatus,
  getDimensions,
  getUserArchetypeScores,
  toggleArchetypePinned,
} from './actions.ts';
import type { Dimension } from './types.ts';

export const archetypeKeys = {
  all: ['archetypes'] as const,
  userScores: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'scores', jobSeekerId] as const,
  dimensions: () => [...archetypeKeys.all, 'dimensions'] as const,
  detail: (archetypeId: number) =>
    [...archetypeKeys.all, 'detail', archetypeId] as const,
  pinnedStatus: (jobSeekerId: number) =>
    [...archetypeKeys.all, 'pinned', jobSeekerId] as const,
};

export function useGetUserArchetypeScores(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.userScores(jobSeekerId)
      : [...archetypeKeys.all, 'scores', null],
    queryFn: () => getUserArchetypeScores(jobSeekerId as number),
  });
}

export function useGetDimensions() {
  const result = useQuery<Dimension[]>({
    queryKey: archetypeKeys.dimensions(),
    queryFn: () => {
      return getDimensions();
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  return result;
}

export function useGetArchetypeById(archetypeId: number | undefined) {
  return useQuery({
    queryKey: archetypeId
      ? archetypeKeys.detail(archetypeId)
      : [...archetypeKeys.all, 'detail', null],
    queryFn: () => getArchetypeById(archetypeId as number),
    enabled: Boolean(archetypeId),
  });
}

export function useGetArchetypePinnedStatus(jobSeekerId: number | undefined) {
  return useQuery({
    queryKey: jobSeekerId
      ? archetypeKeys.pinnedStatus(jobSeekerId)
      : [...archetypeKeys.all, 'pinned', null],
    queryFn: () => getArchetypePinnedStatus(jobSeekerId as number),
    enabled: Boolean(jobSeekerId),
  });
}

export function useToggleArchetypePinned() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobSeekerId: number) => toggleArchetypePinned(jobSeekerId),
    onSuccess: (_, jobSeekerId) => {
      queryClient.invalidateQueries({
        queryKey: archetypeKeys.pinnedStatus(jobSeekerId),
      });
    },
  });
}
