'use server';

import { z } from 'zod';

import { Database } from '~/shared/db/generated/types.ts';

import { sendInvitationEmail } from '../mailing/actions';

import { requireCompanyAccess } from '~/api/security/employer.ts';
import { createClient } from '~/supabase/server.ts';

export type Company = Database['public']['Tables']['companies']['Row'];

export type CompanyEmployee = {
  id: number;
  full_name: string;
  role: string;
  user_id: string;
  email: string;
  created_at: string;
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export async function getCompanyEmployees(
  companyId: number,
  page = 1,
  limit = 10,
): Promise<PaginatedResponse<CompanyEmployee>> {
  await requireCompanyAccess(companyId);
  if (!companyId) {
    return {
      data: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }

  const supabase = await createClient();
  const offset = (page - 1) * limit;

  try {
    const { count, error: countError } = await supabase
      .from('employers')
      .select('id', { count: 'exact', head: true })
      .eq('company_id', companyId);

    if (countError) {
      console.error('Error counting company employees:', countError);
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    const { data: employees, error } = await supabase
      .from('employers')
      .select(
        `
        id,
        full_name,
        role,
        user_id,
        email,
        created_at
      `,
      )
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching company employees:', error);
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      data: employees || [],
      total: totalCount,
      page,
      limit,
      totalPages,
    };
  } catch (error) {
    console.error('Exception fetching company employees:', error);
    return {
      data: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }
}

export async function getCompanyVacancies(companyId: number) {
  await requireCompanyAccess(companyId);
  if (!companyId) {
    return [];
  }

  const supabase = await createClient();

  try {
    const { data: vacancies, error } = await supabase
      .from('vacancies')
      .select(
        `
        *,
        employer:employer_id(id, full_name),
        vacancy_questionnaire_answers(id, vacancy_id)
      `,
      )
      .eq('company_id', companyId)
      .order('status', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching company vacancies:', error);
      return [];
    }

    const processedVacancies =
      vacancies?.map(vacancy => {
        const hasQuestionnaireAnswers =
          vacancy.vacancy_questionnaire_answers &&
          vacancy.vacancy_questionnaire_answers.length > 0;

        return {
          ...vacancy,
          has_questionnaire_answers: hasQuestionnaireAnswers,
          vacancy_questionnaire_answers: undefined,
        };
      }) || [];

    return processedVacancies;
  } catch (error) {
    console.error('Exception fetching company vacancies:', error);
    return [];
  }
}

export type InvitationTokenWithStatus = {
  id: number;
  company_id: number;
  token: string;
  email: string;
  created_at: string;
  expires_at: string;
  is_used: boolean;
  used_at: string | null;
  created_by_employer_id: number;
  used_by_employer_id: number | null;
  created_by_employer_name?: string;
};

export async function getCompanyInvitations(
  companyId: number,
  page = 1,
  limit = 10,
): Promise<PaginatedResponse<InvitationTokenWithStatus>> {
  await requireCompanyAccess(companyId);

  if (!companyId) {
    return {
      data: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }

  const supabase = await createClient();
  const offset = (page - 1) * limit;

  try {
    const { count, error: countError } = await supabase
      .from('invitation_tokens')
      .select('id', { count: 'exact', head: true })
      .eq('company_id', companyId);

    if (countError) {
      console.error('Error counting company invitations:', countError);
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    const { data: invitations, error } = await supabase
      .from('invitation_tokens')
      .select(
        `
        *,
        created_by:created_by_employer_id(full_name)
      `,
      )
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching company invitations:', error);
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    if (!invitations || invitations.length === 0) {
      return {
        data: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      data: invitations.map(invitation => ({
        ...invitation,
        created_by_employer_name: invitation.created_by?.full_name || 'Unknown',
      })),
      total: totalCount,
      page,
      limit,
      totalPages,
    };
  } catch (error) {
    console.error('Exception fetching company invitations:', error);
    return {
      data: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }
}

export async function validateInvitationToken(token: string): Promise<{
  valid: boolean;
  companyId: number | null;
  error: string | null;
  errorType?: 'invalid' | 'expired' | 'already_used';
}> {
  if (!token) {
    return {
      valid: false,
      companyId: null,
      error: 'No token provided',
      errorType: 'invalid',
    };
  }

  const supabase = await createClient();

  try {
    const { data: tokenExists, error: tokenExistsError } = await supabase
      .from('invitation_tokens')
      .select('id, is_used, expires_at, company_id')
      .eq('token', token)
      .single();

    if (tokenExistsError) {
      return {
        valid: false,
        companyId: null,
        error: 'Invalid token',
        errorType: 'invalid',
      };
    }

    if (tokenExists.is_used) {
      return {
        valid: false,
        companyId: null,
        error: 'Token has already been used',
        errorType: 'already_used',
      };
    }

    if (new Date(tokenExists.expires_at) < new Date()) {
      return {
        valid: false,
        companyId: null,
        error: 'Token has expired',
        errorType: 'expired',
      };
    }

    return {
      valid: true,
      companyId: tokenExists.company_id,
      error: null,
    };
  } catch (error) {
    console.error('Exception validating invitation token:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    return {
      valid: false,
      companyId: null,
      error: errorMessage,
      errorType: 'invalid',
    };
  }
}

export async function markInvitationTokenAsUsed(
  token: string,
  employerId: number,
): Promise<{ success: boolean; error: string | null }> {
  if (!token || !employerId) {
    return { success: false, error: 'Missing required parameters' };
  }

  const supabase = await createClient();

  try {
    const { error } = await supabase
      .from('invitation_tokens')
      .update({
        is_used: true,
        used_by_employer_id: employerId,
        used_at: new Date().toISOString(),
      })
      .eq('token', token);

    if (error) {
      console.error('Error marking invitation token as used:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Exception marking invitation token as used:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    return { success: false, error: errorMessage };
  }
}

const InvitationSchema = z.object({
  email: z.string().email('Invalid email address'),
  companyId: z.number().int().positive('Company ID is required'),
  createdByEmployerId: z.number().int().positive('Employer ID is required'),
  expiresInHours: z.number().int().positive().default(48),
});

export async function sendEmployeeInvitation(data: {
  email: string;
  companyId: number;
  createdByEmployerId: number;
  senderName: string;
  companyName: string;
  expiresInHours?: number;
}): Promise<{
  success: boolean;
  error: string | null;
  token?: string;
  messageId?: string;
}> {
  await requireCompanyAccess(data.companyId);

  try {
    const validatedData = InvitationSchema.parse({
      email: data.email,
      companyId: data.companyId,
      createdByEmployerId: data.createdByEmployerId,
      expiresInHours: data.expiresInHours || 48,
    });

    const supabase = await createClient();

    const token =
      Math.random().toString(36).slice(2, 15) +
      Math.random().toString(36).slice(2, 15);

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + validatedData.expiresInHours);

    const { error: tokenError } = await supabase
      .from('invitation_tokens')
      .insert({
        company_id: validatedData.companyId,
        token,
        email: validatedData.email,
        created_by_employer_id: validatedData.createdByEmployerId,
        is_used: false,
        expires_at: expiresAt.toISOString(),
      });

    if (tokenError) {
      console.error('Error creating invitation token:', tokenError);
      return { success: false, error: tokenError.message };
    }

    const baseUrl = process.env.APP_URL;
    const inviteUrl = `${baseUrl}/invite/${token}`;

    const emailResult = await sendInvitationEmail({
      recipientEmail: validatedData.email,
      inviteSenderName: data.senderName,
      companyName: data.companyName,
      actionUrl: inviteUrl,
    });

    if (!emailResult.success) {
      console.error('Error sending invitation email:', emailResult.error);
      return {
        success: false,
        error: emailResult.error || 'Failed to send invitation email',
      };
    }

    return {
      success: true,
      error: null,
      token,
      messageId: emailResult.messageId,
    };
  } catch (error) {
    console.error('Exception sending employee invitation:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    return { success: false, error: errorMessage };
  }
}

export async function getCompanyInfo(
  companyId: number,
): Promise<Company | null> {
  await requireCompanyAccess(companyId);

  if (!companyId) {
    return null;
  }

  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('companies')
      .select('*')
      .eq('id', companyId)
      .single();

    if (error) {
      console.error('Error fetching company info:', error);
      return null;
    }

    return data as Company;
  } catch (error) {
    console.error('Exception fetching company info:', error);
    return null;
  }
}
