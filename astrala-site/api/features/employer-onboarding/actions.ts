'use server';

import { revalidatePath } from 'next/cache';

import { requireAuth } from '~/api/security';
import { createClient } from '~/supabase/server.ts';

export type CreateCompanyParams = {
  name: string;
};

export type UpdateEmployerParams = {
  userId: string;
  companyId: number;
  fullName: string;
  role: string;
};

export async function createCompany({ name }: CreateCompanyParams) {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('companies')
      .insert({
        name,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Ошибка при создании компании:', error);
      return { error: error.message, data: null };
    }

    return { error: null, data };
  } catch (error: unknown) {
    console.error('Ошибка при создании компании:', error);
    return { error, data: null };
  }
}

export async function updateEmployerProfile({
  userId,
  companyId,
  fullName,
  role,
}: UpdateEmployerParams) {
  const authContext = await requireAuth();

  if (authContext.userId !== userId) {
    throw new Error('Forbidden: Can only update your own profile');
  }
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from('employers')
      .update({
        company_id: companyId,
        full_name: fullName,
        role,
      })
      .eq('user_id', userId)
      .select();

    if (error) {
      console.error('Ошибка при обновлении профиля работодателя:', error);
      return { error: error.message, data: null };
    }

    revalidatePath('/employer/dashboard');
    revalidatePath('/employer/profile');

    return { error: null, data: data[0] || null };
  } catch (error: unknown) {
    console.error('Ошибка при обновлении профиля работодателя:', error);
    return { error, data: null };
  }
}

export async function createEmployerCompany({
  userId,
  companyName,
  fullName,
  role,
}: {
  userId: string;
  companyName: string;
  fullName: string;
  role: string;
}) {
  try {
    const companyResult = await createCompany({ name: companyName });

    if (companyResult.error || !companyResult.data) {
      return {
        error: companyResult.error || 'Не удалось создать компанию',
        data: null,
      };
    }

    const employerResult = await updateEmployerProfile({
      userId,
      companyId: companyResult.data.id,
      fullName,
      role,
    });

    if (employerResult.error) {
      return {
        error: employerResult.error,
        data: null,
      };
    }

    return {
      error: null,
      data: {
        employer: employerResult.data,
        company: companyResult.data,
      },
    };
  } catch (error: unknown) {
    console.error('Ошибка при выполнении онбординга работодателя:', error);
    return { error, data: null };
  }
}
